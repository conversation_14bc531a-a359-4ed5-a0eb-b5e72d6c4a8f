const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Sử dụng PowerShell để tìm tất cả các file TypeScript trong dự án
const command = 'powershell -Command "Get-ChildItem -Path src -Recurse -Filter *.ts | ForEach-Object { $_.FullName }"';

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error(`Lỗi khi tìm kiếm file: ${error.message}`);
    return;
  }

  const files = stdout.trim().split('\r\n');
  
  // Xử lý từng file
  files.forEach(filePath => {
    try {
      // Đọc nội dung file
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Thay thế các import từ @common/response và @/common/response
      let newContent = content
        .replace(
          /import\s+{([^}]*)ApiResponseDto([^}]*)}(\s+from\s+['"]@common\/response['"])/g, 
          'import {$1ApiResponseDto$2} from \'@/common/response\''
        )
        .replace(
          /import\s+{([^}]*)PaginatedResult([^}]*)}(\s+from\s+['"]@common\/response['"])/g, 
          'import {$1PaginatedResult$2} from \'@/common/response\''
        )
        .replace(
          /import\s+{([^}]*)ApiResponseDto\s+as\s+AppApiResponse([^}]*)}(\s+from\s+['"]@\/common\/response['"])/g,
          'import {$1ApiResponseDto as AppApiResponse$2} from \'@/common/response\''
        );
      
      // Nếu có thay đổi, ghi lại file
      if (content !== newContent) {
        console.log(`Cập nhật file: ${filePath}`);
        fs.writeFileSync(filePath, newContent, 'utf8');
      }
    } catch (err) {
      console.error(`Lỗi khi xử lý file ${filePath}: ${err.message}`);
    }
  });

  console.log('Hoàn thành cập nhật import.');
}); 