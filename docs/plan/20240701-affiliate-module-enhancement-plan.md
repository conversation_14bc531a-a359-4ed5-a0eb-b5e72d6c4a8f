# Kế hoạch Nâng cấp Module Affiliate

## Ngày: 01/07/2024

## Tổng quan

Dựa trên phân tích hiện trạng của module Affiliate, tài liệu này đề xuất kế hoạch nâng cấp và triển khai các API còn thiếu để hoàn thiện chức năng của module.

## Phân tích hiện trạng

### Entities hiện có
1. `AffiliateAccount` - Thông tin tài khoản affiliate
2. `AffiliateClick` - Lưu thông tin các lượt click từ affiliate links
3. `AffiliateContract` - L<PERSON><PERSON> thông tin hợp đồng affiliate
4. `AffiliateCustomerOrder` - Đơn hàng affiliate
5. `AffiliatePointConversionHistory` - Lịch sử đổi tiền affiliate sang point hệ thống
6. `AffiliateRank` - Bảng rank của affiliate
7. `AffiliateWithdrawHistory` - Lị<PERSON> sử rút tiền

### APIs đã triển khai

#### User APIs
1. `GET /user/affiliate/statistics` - Lấy thông tin thống kê tài khoản affiliate
2. `GET /user/affiliate/account` - Lấy thông tin tài khoản affiliate
3. `GET /user/affiliate/orders` - Lấy danh sách đơn hàng affiliate
4. `GET /user/affiliate/withdrawals` - Lấy danh sách lịch sử rút tiền
5. `GET /user/affiliate/customers` - Lấy danh sách khách hàng affiliate
6. `POST /user/affiliate/withdraw-requests` - Tạo yêu cầu rút tiền mới

#### Admin APIs
1. `GET /admin/affiliate/overview` - Lấy thông tin tổng quan về affiliate
2. `GET /admin/affiliate/accounts` - Lấy danh sách tài khoản affiliate
3. `GET /admin/affiliate/accounts/:id` - Lấy chi tiết tài khoản affiliate
4. `PATCH /admin/affiliate/accounts/:id/status` - Cập nhật trạng thái tài khoản affiliate
5. `GET /admin/affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền
6. `GET /admin/affiliate/withdrawals/:id` - Lấy chi tiết yêu cầu rút tiền
7. `PATCH /admin/affiliate/withdrawals/:id/status` - Cập nhật trạng thái yêu cầu rút tiền
8. `GET /admin/affiliate/ranks` - Lấy danh sách rank affiliate
9. `GET /admin/affiliate/ranks/:id` - Lấy chi tiết rank affiliate
10. `POST /admin/affiliate/ranks` - Tạo rank affiliate mới
11. `PATCH /admin/affiliate/ranks/:id` - Cập nhật rank affiliate

#### Registration APIs
1. `GET /user/affiliate/registration/status` - Lấy trạng thái đăng ký hiện tại
2. `POST /user/affiliate/registration/select-account-type` - Chọn loại tài khoản
3. `POST /user/affiliate/registration/accept-terms` - Chấp nhận điều khoản
4. `POST /user/affiliate/registration/personal-info` - Cập nhật thông tin cá nhân
5. `POST /user/affiliate/registration/verify-otp` - Xác thực OTP

#### Admin Registration APIs
1. `POST /admin/affiliate/registration/:userId/approve` - Phê duyệt đăng ký affiliate
2. `POST /admin/affiliate/registration/:userId/reject` - Từ chối đăng ký affiliate

### APIs còn thiếu

Dựa trên phân tích entities và chức năng hiện có, các API sau đây cần được triển khai:

#### User APIs
1. `GET /user/affiliate/point-conversions` - Lấy lịch sử chuyển đổi điểm
2. `POST /user/affiliate/convert-to-points` - Chuyển đổi tiền hoa hồng sang điểm trực tiếp
   - Chức năng: Chuyển đổi trực tiếp tiền hoa hồng trong `walletBalance` của `AffiliateAccount` sang `pointsBalance` của `User`
   - Không cần tạo yêu cầu chờ duyệt, thực hiện chuyển đổi ngay lập tức
   - Lưu lịch sử vào bảng `AffiliatePointConversionHistory` với trạng thái `SUCCESS`
3. `GET /user/affiliate/referral-link` - Lấy thông tin link giới thiệu
   - Link giới thiệu có định dạng mặc định: `http://app.redai.vn?ref={id của affiliate-account}`

#### Admin APIs
1. `GET /admin/affiliate/point-conversions` - Lấy danh sách lịch sử chuyển đổi điểm
2. `GET /admin/affiliate/point-conversions/:id` - Lấy chi tiết lịch sử chuyển đổi điểm
3. `GET /admin/affiliate/clicks` - Lấy thống kê lượt click
4. `GET /admin/affiliate/contracts` - Lấy danh sách hợp đồng affiliate
5. `GET /admin/affiliate/contracts/:id` - Lấy chi tiết hợp đồng affiliate
6. `PATCH /admin/affiliate/contracts/:id/status` - Cập nhật trạng thái hợp đồng affiliate

### Vấn đề về chuẩn hóa code

1. **Xử lý lỗi**: Có sự không nhất quán giữa các file error code. Cần hợp nhất vào một file duy nhất.
2. **Swagger Documentation**: Một số API thiếu documentation đầy đủ.
3. **DTO Validation**: Cần kiểm tra lại validation trong các DTO.
4. **Repository Pattern**: Một số repository chưa tuân thủ đúng chuẩn.

## Kế hoạch triển khai

### 1. Chuẩn hóa Error Handling

- Hợp nhất các file error code vào một file duy nhất `src/modules/affiliate/errors/affiliate-error.code.ts`
- Đảm bảo tất cả các service đều sử dụng `AppException` với mã lỗi từ file này

### 2. Triển khai API Lịch sử chuyển đổi điểm (User)

- Tạo DTO cho query và response
- Tạo service để xử lý logic
- Tạo controller endpoint
- Thêm Swagger documentation

### 3. Triển khai API Chuyển đổi tiền hoa hồng sang điểm (User)

- Tạo DTO cho request và response
- Tạo service để xử lý logic chuyển đổi trực tiếp:
  - Trừ tiền từ `walletBalance` trong `AffiliateAccount`
  - Cộng điểm vào `pointsBalance` trong `User`
  - Lưu lịch sử vào `AffiliatePointConversionHistory` với trạng thái `SUCCESS`
- Tạo controller endpoint
- Thêm Swagger documentation

### 4. Triển khai API Link giới thiệu (User)

- Tạo DTO cho response
- Tạo service để xử lý logic trả về link giới thiệu với định dạng `http://app.redai.vn?ref={id của affiliate-account}`
- Tạo controller endpoint
- Thêm Swagger documentation

### 5. Triển khai API Quản lý lịch sử chuyển đổi điểm (Admin)

- Tạo DTO cho query và response
- Tạo service để xử lý logic hiển thị lịch sử chuyển đổi
- Tạo controller endpoint
- Thêm Swagger documentation

### 6. Triển khai API Thống kê lượt click (Admin)

- Tạo DTO cho query và response
- Tạo service để xử lý logic
- Tạo controller endpoint
- Thêm Swagger documentation

### 7. Triển khai API Quản lý hợp đồng (Admin)

- Tạo DTO cho query và response
- Tạo service để xử lý logic
- Tạo controller endpoint
- Thêm Swagger documentation

### 8. Cập nhật Swagger Documentation

- Kiểm tra và cập nhật documentation cho tất cả các API
- Đảm bảo mọi API đều có mô tả đầy đủ, ví dụ và response schema

### 9. Viết Unit Tests

- Viết unit tests cho các service mới
- Viết integration tests cho các API mới
- Đảm bảo độ bao phủ test tối thiểu 80%

## Thứ tự ưu tiên

1. Chuẩn hóa Error Handling
2. API Lịch sử chuyển đổi điểm (User)
3. API Chuyển đổi tiền hoa hồng sang điểm (User)
4. API Link giới thiệu (User)
5. API Quản lý lịch sử chuyển đổi điểm (Admin)
6. API Thống kê lượt click (Admin)
7. API Quản lý hợp đồng (Admin)
8. Cập nhật Swagger Documentation
9. Viết Unit Tests

## Tiến độ

| Công việc | Trạng thái | Ngày hoàn thành |
|-----------|------------|-----------------|
| Chuẩn hóa Error Handling | Hoàn thành | 01/07/2024 |
| API Lịch sử chuyển đổi điểm (User) | Hoàn thành | 01/07/2024 |
| API Chuyển đổi tiền hoa hồng sang điểm (User) | Hoàn thành | 01/07/2024 |
| API Link giới thiệu (User) | Hoàn thành | 01/07/2024 |
| API Quản lý lịch sử chuyển đổi điểm (Admin) | Hoàn thành | 01/07/2024 |
| API Thống kê lượt click (Admin) | Hoàn thành | 01/07/2024 |
| API Quản lý hợp đồng (Admin) | Hoàn thành | 01/07/2024 |
| Cập nhật Swagger Documentation | Hoàn thành | 01/07/2024 |
| Sửa lỗi trong các service | Hoàn thành | 01/07/2024 |
| Viết Unit Tests | Chưa bắt đầu | |
