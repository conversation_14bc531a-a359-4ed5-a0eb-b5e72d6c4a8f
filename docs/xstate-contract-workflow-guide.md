# Hướng dẫn sử dụng XState cho quản lý luồng ký hợp đồng

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

XState là một thư viện JavaScript/TypeScript giúp xây dựng và quản lý máy trạng thái (state machines) trong ứng dụng. Tài liệu này hướng dẫn cách sử dụng XState để quản lý luồng ký hợp đồng trong ứng dụng NestJS.

## 2. Cài đặt

```bash
npm install xstate @xstate/nest
```

## 3. <PERSON>h<PERSON><PERSON> niệm cơ bản

### 3.1. <PERSON><PERSON><PERSON> tr<PERSON> thái (State Machine)

Máy trạng thái là một mô hình toán học mô tả hành vi của hệ thống thông qua các trạng thái và chuyển đổi giữa các trạng thái.

### 3.2. <PERSON><PERSON><PERSON><PERSON>á<PERSON> (State)

Trạng thái biểu diễn tình trạng hiện tại của hệ thống. Ví dụ: `draft`, `pendingApproval`, `approved`, `rejected`.

### 3.3. Sự kiện (Event)

Sự kiện kích hoạt chuyển đổi giữa các trạng thái. Ví dụ: `SIGN`, `APPROVE`, `REJECT`.

### 3.4. Chuyển đổi (Transition)

Chuyển đổi xác định trạng thái tiếp theo dựa trên trạng thái hiện tại và sự kiện.

### 3.5. Hành động (Action)

Hành động được thực hiện khi chuyển đổi trạng thái. Ví dụ: `saveContract`, `notifyUser`.

### 3.6. Điều kiện (Guard)

Điều kiện để cho phép chuyển đổi trạng thái. Ví dụ: `isValidSignature`.

### 3.7. Ngữ cảnh (Context)

Dữ liệu được lưu trữ và cập nhật trong máy trạng thái. Ví dụ: `contractId`, `userId`, `signatureData`.

## 4. Cấu trúc thư mục

```
src/
└── modules/
    └── rule-contract/
        ├── state-machine/
        │   ├── rule-contract.types.ts
        │   ├── rule-contract.machine.ts
        │   ├── rule-contract-state.service.ts
        │   └── rule-contract-actions.service.ts
        └── ...
```

## 5. Triển khai cơ bản

### 5.1. Định nghĩa các trạng thái và sự kiện

```typescript
// rule-contract.types.ts
export enum ContractState {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pendingApproval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export enum ContractEvent {
  CREATE = 'CREATE',
  SIGN = 'SIGN',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  RESUBMIT = 'RESUBMIT',
}

export interface ContractContext {
  contractId: number | null;
  userId: number | null;
  // Các thông tin khác...
}
```

### 5.2. Tạo máy trạng thái

```typescript
// rule-contract.machine.ts
import { createMachine } from 'xstate';
import { ContractState, ContractEvent, ContractContext } from './rule-contract.types';

export const createRuleContractMachine = (initialContext: Partial<ContractContext> = {}) => {
  return createMachine<ContractContext, { type: ContractEvent; data?: any }>({
    id: 'ruleContract',
    initial: ContractState.DRAFT,
    context: {
      contractId: null,
      userId: null,
      ...initialContext,
    },
    states: {
      [ContractState.DRAFT]: {
        on: {
          [ContractEvent.SIGN]: {
            target: ContractState.PENDING_APPROVAL,
            actions: ['saveSignature', 'notifyAdmin'],
            cond: 'isValidSignature',
          },
        },
      },
      [ContractState.PENDING_APPROVAL]: {
        on: {
          [ContractEvent.APPROVE]: {
            target: ContractState.APPROVED,
            actions: ['saveApproval', 'notifyUser'],
          },
          [ContractEvent.REJECT]: {
            target: ContractState.REJECTED,
            actions: ['saveRejection', 'notifyUser'],
          },
        },
      },
      [ContractState.APPROVED]: {
        type: 'final',
      },
      [ContractState.REJECTED]: {
        on: {
          [ContractEvent.RESUBMIT]: {
            target: ContractState.DRAFT,
            actions: 'clearRejectionReason',
          },
        },
      },
    },
  });
};
```

### 5.3. Triển khai service quản lý máy trạng thái

```typescript
// rule-contract-state.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { interpret, Interpreter } from 'xstate';
import { createRuleContractMachine } from './rule-contract.machine';
import { ContractContext, ContractEvent, ContractState } from './rule-contract.types';

@Injectable()
export class RuleContractStateService {
  private readonly logger = new Logger(RuleContractStateService.name);
  private readonly machines: Map<number, Interpreter<ContractContext>> = new Map();

  constructor(
    private readonly ruleContractActionsService: RuleContractActionsService,
  ) {}

  async initializeStateMachine(userId: number, contractId?: number): Promise<Interpreter<ContractContext>> {
    // Khởi tạo máy trạng thái...
  }

  async sendEvent(contractId: number, event: ContractEvent, data?: any): Promise<any> {
    // Gửi sự kiện đến máy trạng thái...
  }

  async getCurrentState(contractId: number): Promise<ContractState> {
    // Lấy trạng thái hiện tại...
  }
}
```

## 6. Sử dụng trong controller

```typescript
// rule-contract-user.controller.ts
@Controller('user/rule-contracts')
export class RuleContractUserController {
  constructor(
    private readonly ruleContractStateService: RuleContractStateService,
  ) {}

  @Post(':id/sign')
  async signContract(
    @CurrentUser() user: JwtPayload,
    @Param('id') contractId: number,
    @Body() signContractDto: SignContractDto,
  ) {
    // Gửi sự kiện SIGN đến máy trạng thái
    const newState = await this.ruleContractStateService.sendEvent(
      contractId,
      ContractEvent.SIGN,
      {
        signatureData: signContractDto.signatureData,
      },
    );

    return { state: newState };
  }
}
```

## 7. Ví dụ luồng ký hợp đồng

### 7.1. Tạo hợp đồng mới

```typescript
// Khởi tạo máy trạng thái cho người dùng
const service = await ruleContractStateService.initializeStateMachine(userId);

// Tạo hợp đồng mới và lưu vào database
const contract = await ruleContractService.createContract(userId, contractData);

// Cập nhật máy trạng thái với ID hợp đồng
service.state.context = {
  ...service.state.context,
  contractId: contract.id,
};
```

### 7.2. Người dùng ký hợp đồng

```typescript
// Người dùng ký hợp đồng
await ruleContractStateService.sendEvent(
  contractId,
  ContractEvent.SIGN,
  {
    signatureData: 'base64-signature-data',
  },
);
```

### 7.3. Admin phê duyệt hoặc từ chối

```typescript
// Admin phê duyệt
await ruleContractStateService.sendEvent(
  contractId,
  ContractEvent.APPROVE,
);

// Hoặc admin từ chối
await ruleContractStateService.sendEvent(
  contractId,
  ContractEvent.REJECT,
  {
    rejectionReason: 'Thông tin không chính xác',
  },
);
```

### 7.4. Người dùng gửi lại hợp đồng đã bị từ chối

```typescript
// Người dùng gửi lại hợp đồng đã bị từ chối
await ruleContractStateService.sendEvent(
  contractId,
  ContractEvent.RESUBMIT,
);
```

## 8. Mẹo và kỹ thuật

### 8.1. Sử dụng context để lưu trữ dữ liệu

```typescript
const machine = createMachine({
  context: {
    contractId: null,
    userId: null,
    signatureData: null,
  },
  // ...
});
```

### 8.2. Sử dụng guards để kiểm tra điều kiện

```typescript
const machine = createMachine({
  states: {
    draft: {
      on: {
        SIGN: {
          target: 'pendingApproval',
          cond: 'isValidSignature', // Chỉ chuyển đổi khi chữ ký hợp lệ
        },
      },
    },
  },
});
```

### 8.3. Sử dụng actions để thực hiện tác vụ

```typescript
const machine = createMachine({
  states: {
    draft: {
      on: {
        SIGN: {
          target: 'pendingApproval',
          actions: ['saveSignature', 'notifyAdmin'], // Thực hiện các tác vụ khi chuyển đổi
        },
      },
    },
  },
});
```

### 8.4. Sử dụng services để gọi API

```typescript
const machine = createMachine({
  states: {
    draft: {
      on: {
        SIGN: {
          target: 'pendingApproval',
        },
      },
      invoke: {
        src: 'loadContractData', // Gọi API để lấy dữ liệu hợp đồng
        onDone: {
          actions: 'updateContractData',
        },
        onError: {
          actions: 'handleError',
        },
      },
    },
  },
});
```

## 9. Trực quan hóa máy trạng thái

XState cung cấp công cụ trực quan hóa máy trạng thái tại [xstate.js.org/viz](https://xstate.js.org/viz/). Bạn có thể dán định nghĩa máy trạng thái vào đó để xem biểu đồ trạng thái.

## 10. Lợi ích của việc sử dụng XState

1. **Quản lý trạng thái rõ ràng**: Định nghĩa rõ ràng các trạng thái và chuyển đổi.
2. **Tránh trạng thái không hợp lệ**: Chỉ cho phép chuyển đổi đến các trạng thái đã định nghĩa.
3. **Dễ mở rộng**: Thêm trạng thái mới hoặc sự kiện mới một cách dễ dàng.
4. **Dễ hiểu và bảo trì**: Biểu đồ trạng thái giúp hiểu rõ luồng xử lý.
5. **Tích hợp với các sự kiện**: Thực hiện các hành động khi chuyển đổi trạng thái.

## 11. Ví dụ thực tế: Luồng ký hợp đồng nguyên tắc

### 11.1. Định nghĩa máy trạng thái

```typescript
const ruleContractMachine = createMachine({
  id: 'ruleContract',
  initial: 'draft',
  context: {
    contractId: null,
    userId: null,
    contractData: {},
    signatureData: null,
    rejectionReason: null,
  },
  states: {
    draft: {
      on: {
        CREATE: {
          actions: 'saveContract',
        },
        SIGN: {
          target: 'pendingApproval',
          actions: ['saveSignature', 'notifyAdmin'],
          cond: 'isValidSignature',
        },
      },
    },
    pendingApproval: {
      on: {
        APPROVE: {
          target: 'approved',
          actions: ['saveApproval', 'notifyUser', 'updateUserType'],
        },
        REJECT: {
          target: 'rejected',
          actions: ['saveRejection', 'notifyUser'],
        },
      },
    },
    approved: {
      type: 'final',
      entry: 'finalizeContract',
    },
    rejected: {
      on: {
        RESUBMIT: {
          target: 'draft',
          actions: 'clearRejectionReason',
        },
      },
    },
  },
});
```

### 11.2. Triển khai các actions

```typescript
const actions = {
  saveContract: async (context, event) => {
    // Lưu hợp đồng vào database
    const contract = await ruleContractRepository.save({
      userId: context.userId,
      type: event.data.type,
      status: ContractStatusEnum.DRAFT,
      contractUrlPdf: event.data.contractKey,
    });

    // Cập nhật context
    return {
      ...context,
      contractId: contract.id,
    };
  },
  saveSignature: async (context, event) => {
    // Lưu chữ ký vào database
    await ruleContractRepository.update(context.contractId, {
      status: ContractStatusEnum.PENDING_APPROVAL,
      userSignatureAt: Date.now(),
    });

    // Cập nhật context
    return {
      ...context,
      signatureData: event.data.signatureData,
    };
  },
  // Các actions khác...
};
```

## 12. Kết luận

XState là một công cụ mạnh mẽ để quản lý trạng thái phức tạp trong ứng dụng. Bằng cách sử dụng XState cho luồng ký hợp đồng, bạn có thể:

1. Định nghĩa rõ ràng các trạng thái và chuyển đổi
2. Đảm bảo tính nhất quán trong quy trình ký hợp đồng
3. Dễ dàng mở rộng và bảo trì
4. Tích hợp với các tác vụ như gửi email, cập nhật database, v.v.

Hãy thử áp dụng XState cho các luồng xử lý phức tạp trong ứng dụng của bạn để thấy sự khác biệt!
