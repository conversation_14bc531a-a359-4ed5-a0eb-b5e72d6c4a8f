import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { JwtService } from '@nestjs/jwt';
import { EmployeeRepository } from '../../src/modules/employee/repositories';
import { AdminEmailServerConfigurationRepository } from '../../src/modules/integration/repositories';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../../src/modules/integration/user/dto';
import { AdminEmailServerConfigurationEntity } from '../../src/modules/integration/entities/admin_email_server_configurations.entity';

describe('AdminEmailServerConfigurationController (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let employeeRepository: EmployeeRepository;
  let emailServerRepository: AdminEmailServerConfigurationRepository;
  let adminToken: string;
  let testServerId: number;

  const mockCreateDto: CreateEmailServerDto = {
    serverName: 'E2E Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: 'test-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
  };

  const mockUpdateDto: UpdateEmailServerDto = {
    serverName: 'Updated E2E Test SMTP Server',
    port: 465,
  };

  const mockTestDto: TestEmailServerDto = {
    recipientEmail: '<EMAIL>',
    subject: 'E2E Test Email',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();

    // Get services
    jwtService = app.get<JwtService>(JwtService);
    employeeRepository = app.get<EmployeeRepository>(EmployeeRepository);
    emailServerRepository = app.get<AdminEmailServerConfigurationRepository>(AdminEmailServerConfigurationRepository);

    // Create admin token
    const admin = await employeeRepository.findOne({ where: { role: 'admin' } });
    if (!admin) {
      throw new Error('Admin user not found in database');
    }
    
    adminToken = jwtService.sign({
      id: admin.id,
      fullName: admin.fullName,
      role: admin.role,
    });

    // Clean up any existing test data
    await emailServerRepository.delete({ serverName: 'E2E Test SMTP Server' });
    await emailServerRepository.delete({ serverName: 'Updated E2E Test SMTP Server' });
  });

  afterAll(async () => {
    // Clean up test data
    await emailServerRepository.delete({ serverName: 'E2E Test SMTP Server' });
    await emailServerRepository.delete({ serverName: 'Updated E2E Test SMTP Server' });
    await app.close();
  });

  describe('/admin/integration/email-server (GET)', () => {
    it('should return a paginated list of email servers', async () => {
      const response = await request(app.getHttpServer())
        .get('/admin/integration/email-server')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Lấy danh sách cấu hình máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      expect(response.body.result.items).toBeDefined();
      expect(response.body.result.meta).toBeDefined();
    });

    it('should apply pagination parameters', async () => {
      const response = await request(app.getHttpServer())
        .get('/admin/integration/email-server?page=1&limit=5')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.result.meta.itemsPerPage).toBe(5);
      expect(response.body.result.meta.currentPage).toBe(1);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get('/admin/integration/email-server')
        .expect(401);
    });
  });

  describe('/admin/integration/email-server (POST)', () => {
    it('should create a new email server', async () => {
      const response = await request(app.getHttpServer())
        .post('/admin/integration/email-server')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(mockCreateDto)
        .expect(201);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Tạo mới cấu hình máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      expect(response.body.result.serverName).toBe(mockCreateDto.serverName);
      expect(response.body.result.host).toBe(mockCreateDto.host);
      expect(response.body.result.password).toBe('********'); // Password should be masked

      // Save the ID for later tests
      testServerId = response.body.result.id;
    });

    it('should validate required fields', async () => {
      await request(app.getHttpServer())
        .post('/admin/integration/email-server')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          // Missing required fields
          serverName: 'Invalid Server',
        })
        .expect(400);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .post('/admin/integration/email-server')
        .send(mockCreateDto)
        .expect(401);
    });
  });

  describe('/admin/integration/email-server/:id (GET)', () => {
    it('should return an email server by id', async () => {
      const response = await request(app.getHttpServer())
        .get(`/admin/integration/email-server/${testServerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Lấy thông tin chi tiết cấu hình máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      expect(response.body.result.id).toBe(testServerId);
      expect(response.body.result.serverName).toBe(mockCreateDto.serverName);
      expect(response.body.result.password).toBe('********'); // Password should be masked
    });

    it('should return 404 for non-existent id', async () => {
      await request(app.getHttpServer())
        .get('/admin/integration/email-server/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get(`/admin/integration/email-server/${testServerId}`)
        .expect(401);
    });
  });

  describe('/admin/integration/email-server/:id (PUT)', () => {
    it('should update an existing email server', async () => {
      const response = await request(app.getHttpServer())
        .put(`/admin/integration/email-server/${testServerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(mockUpdateDto)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Cập nhật cấu hình máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      expect(response.body.result.id).toBe(testServerId);
      expect(response.body.result.serverName).toBe(mockUpdateDto.serverName);
      expect(response.body.result.port).toBe(mockUpdateDto.port);
    });

    it('should return 404 for non-existent id', async () => {
      await request(app.getHttpServer())
        .put('/admin/integration/email-server/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(mockUpdateDto)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .put(`/admin/integration/email-server/${testServerId}`)
        .send(mockUpdateDto)
        .expect(401);
    });
  });

  describe('/admin/integration/email-server/:id/test (POST)', () => {
    it('should test email server connection', async () => {
      // Note: This test might fail in CI environment due to actual SMTP connection
      // We're mocking the nodemailer in unit tests, but for E2E we might need to skip or mock differently
      const response = await request(app.getHttpServer())
        .post(`/admin/integration/email-server/${testServerId}/test`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(mockTestDto)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Kiểm tra kết nối máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      // The actual success might depend on the environment, so we don't assert on success value
    });

    it('should return 404 for non-existent id', async () => {
      await request(app.getHttpServer())
        .post('/admin/integration/email-server/99999/test')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(mockTestDto)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .post(`/admin/integration/email-server/${testServerId}/test`)
        .send(mockTestDto)
        .expect(401);
    });
  });

  describe('/admin/integration/email-server/:id (DELETE)', () => {
    it('should delete an email server', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/admin/integration/email-server/${testServerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Xóa cấu hình máy chủ email thành công');
      expect(response.body.result).toBeDefined();
      expect(response.body.result.message).toBe('Cấu hình máy chủ email đã được xóa');

      // Verify it's actually deleted
      await request(app.getHttpServer())
        .get(`/admin/integration/email-server/${testServerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should return 404 for non-existent id', async () => {
      await request(app.getHttpServer())
        .delete('/admin/integration/email-server/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .delete(`/admin/integration/email-server/${testServerId}`)
        .expect(401);
    });
  });
});
