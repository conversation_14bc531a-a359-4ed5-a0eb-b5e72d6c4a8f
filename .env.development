PORT=3000

# Database
DB_HOST=pub-redai-v2-i6nv7t29-bdc8440.dbaas.bfcplatform.vn
DB_URL=**********************************************************************************
DB_PORT=5432
DB_USERNAME=member
DB_PASSWORD=redai@123
DB_DATABASE=postgres
DB_SSL=true

# CloudFlare R2
CF_R2_ACCESS_KEY=SB4NHSLW6ZL16S3NNFZQ
CF_R2_SECRET_KEY=v6qnYWr82DDo7yaOdeh4GCN7WdupfQomO7G3F7cy
CF_R2_ENDPOINT=https://hn.ss.bfcplatform.vn
CF_BUCKET_NAME=redaivn
CF_R2_REGION=auto

#cdn
CDN_URL=https://cdn.redai.vn
CDN_SECRET_KEY=c359b50178899b3bd2518a59a57fcc75

#openai
OPENAI_API_KEY=********************************************************************************************************************************************************************

# JWT Authentication
JWT_SECRET=80fea_bd9a_17aa_add7_b7b4_2f59_c972_ede0
JWT_ACCESS_TOKEN_EXPIRATION_TIME=604800s # 1 hour
JWT_REFRESH_TOKEN_EXPIRATION_TIME=604800s # 7 days

EXTERNAL_EMAIL_API_URL=http://*************:8080

# Redis Connection URL
REDIS_URL=redis://:<EMAIL>:6379

# PDF Editing API URL
PDF_EDIT_API_URL=http://*************:8080

# SMS Service Configuration
SERVICE_HOST_SMS=http://*************:8080
SERVICE_SMS_API_KEY=08a89789-020f-4ece-ba66-bd67aeae5bde

# Google reCAPTCHA
RECAPTCHA_SECRET_KEY=6Lf_TaUqAAAAAMt4pBrCLNEkxJZ6-0Bc1X4UV28x

# SEPAY HUB CONFIG
SEPAY_HUB_API_KEY=4vGF864GzkV5iyknY32KLJASkHAMlCs6
SEPAY_HUB_CLIENT_ID=RrEpNYofMwIM4wVyYd0hOBPcCjLiTYWyGpCgg2PwqS
SEPAY_HUB_CLIENT_SECRET=00qxdXpdLQ0dDg69OaBk9MCEdOTahaFx
SEPAY_HUB_API_URL=https://partner-api.sepay.vn/merchant/v1

# SEPAY Webhook API Key
SEPAY_WEBHOOK_API_KEY=1a103477-5935-4168-8a3b-23330887fa2a

NODE_ENV=development

# Swagger Configuration
SWAGGER_LOCAL_URL=http://localhost:3000
SWAGGER_DEV_URL=http://localhost:3000
SWAGGER_TEST_URL=http://*************:3000
SWAGGER_STAGING_URL=https://api-staging.redai.vn
SWAGGER_PROD_URL=https://api.redai.vn

# Agent API Key
AGENT_API_KEY=1a103477-1234-4123-8a3b-23330887fa2a

# Encryption Service
ENCRYPTION_SECRET_KEY=80fea_bd9a_17aa_add7_b7b4_2f59_c972_ede0

# Zalo API Configuration
ZALO_APP_ID=your_zalo_app_id
ZALO_APP_SECRET=your_zalo_app_secret
ZALO_WEBHOOK_SECRET=your_zalo_webhook_secret
ZALO_WEBHOOK_URL=https://your-domain.com/api/zalo/webhook

# Google OAuth Configuration
GOOGLE_API_KEY=AIzaSyDdLvThRQAHx0MfGioQuULkf1uZu_BvwIg
GOOGLE_CLIENT_ID=************-lue1qckieptt6vs2ue584mbbo6ch44lm.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3000

# Các URI chuyển hướng khác (nếu cần)
# GOOGLE_REDIRECT_URI_PRODUCTION=https://app.redai.vn/auth/google/callback
# GOOGLE_REDIRECT_URI_STAGING=https://staging.redai.vn/auth/google/callback

# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_STORAGE_BUCKET=your-bucket-name

# Google Ads API Configuration
GOOGLE_ADS_CLIENT_ID=your-ads-client-id
GOOGLE_ADS_CLIENT_SECRET=your-ads-client-secret
GOOGLE_ADS_DEVELOPER_TOKEN=your-developer-token
GOOGLE_ADS_REFRESH_TOKEN=your-refresh-token
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your-manager-account-id

FPT_SMS_CLIENT_ID=888a1ad38442ea0d8dcc6B75f843b32FD5d10c88
FPT_SMS_CLIENT_SECRET=f13F32a055082062d9c508ce81292880EF4987e4763e2D55509073872e8423dC4a6ca42d
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=REDAI

ADMIN_SECRECT_MODEL=admin_secret_key_for_model_encryption
USER_SECRECT_MODEL=user_secret_key_for_model_encryption

FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=
FACEBOOK_REDIRECT_URI=
FACEBOOK_GRAPH_API_VERSION=