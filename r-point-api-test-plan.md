# Kế hoạch kiểm tra API trong module R-Point

## 1. Tổng quan

Module R-Point quản lý các chức năng liên quan đến điểm (point) trong hệ thống, bao gồm:
- Quản lý gói point
- Mua point
- Quản lý giao dịch
- Quản lý coupon
- X<PERSON> lý webhook thanh toán
- Quản lý hóa đơn

Module được chia thành 2 phần chính:
- **Admin**: Dành cho quản trị viên
- **User**: <PERSON>à<PERSON> cho người dùng

## 2. Danh sách API cần kiểm tra

### 2.1. API cho User

#### 2.1.1. Point User API (`point-user.controller.ts`)
- `GET /r-point/points`: Lấy danh sách gói point
- `GET /r-point/points/:id`: <PERSON><PERSON><PERSON> thông tin chi tiết gói point

#### 2.1.2. Payment User API (`payment-user.controller.ts`)
- `POST /r-point/payment/purchase`: Mua R-Point
- `GET /r-point/payment/history`: L<PERSON>y lịch sử giao dịch mua R-Point

#### 2.1.3. Coupon User API (`coupon-user.controller.ts`)
- `GET /r-point/coupons`: Lấy danh sách coupon
- `POST /r-point/coupons/validate`: Xác thực coupon

#### 2.1.4. Webhook User API (`webhook-user.controller.ts`)
- `POST /r-point/webhook`: Xử lý webhook từ cổng thanh toán

#### 2.1.5. Invoice User API (`invoice-user.controller.ts`)
- `GET /r-point/invoices`: Lấy danh sách hóa đơn

### 2.2. API cho Admin

#### 2.2.1. Point Admin API (`point-admin.controller.ts`)
- `GET /admin/r-point/points`: Lấy danh sách gói point
- `POST /admin/r-point/points`: Tạo gói point mới
- `GET /admin/r-point/points/:id`: Lấy thông tin chi tiết gói point
- `PUT /admin/r-point/points/:id`: Cập nhật gói point
- `DELETE /admin/r-point/points/:id`: Xóa gói point

#### 2.2.2. Transaction User API (`transaction-user.controller.ts`)
- `GET /admin/r-point/transactions`: Lấy danh sách giao dịch
- `GET /admin/r-point/transactions/:id`: Lấy thông tin chi tiết giao dịch
- `GET /admin/r-point/transactions/statistics/overview`: Lấy thống kê về r-point và giao dịch

#### 2.2.3. Coupon Admin API (`coupon-admin.controller.ts`)
- `GET /admin/r-point/coupons`: Lấy danh sách coupon
- `POST /admin/r-point/coupons`: Tạo coupon mới
- `GET /admin/r-point/coupons/:id`: Lấy thông tin chi tiết coupon
- `PUT /admin/r-point/coupons/:id`: Cập nhật coupon
- `DELETE /admin/r-point/coupons/:id`: Xóa coupon

#### 2.2.4. Invoice Admin API (`invoice-admin.controller.ts`)
- `GET /admin/r-point/invoices`: Lấy danh sách hóa đơn
- `GET /admin/r-point/invoices/:id`: Lấy thông tin chi tiết hóa đơn

## 3. Kịch bản kiểm tra và rủi ro tiềm ẩn

### 3.1. API Mua R-Point (`POST /r-point/payment/purchase`)

#### Kịch bản kiểm tra:
1. **Mua gói point cố định**
   - Input: `pointId` của gói point cố định
   - Expected: Tạo giao dịch thành công với số point và số tiền đúng với gói

2. **Mua gói point tùy chỉnh**
   - Input: `pointId` của gói point tùy chỉnh, `pointAmount` trong khoảng min-max
   - Expected: Tạo giao dịch thành công với số point và số tiền được tính dựa trên tỷ lệ

3. **Mua gói point với coupon**
   - Input: `pointId`, `couponCode` hợp lệ
   - Expected: Tạo giao dịch thành công với giá đã giảm

4. **Mua gói point tùy chỉnh với số lượng ngoài khoảng cho phép**
   - Input: `pointId` của gói tùy chỉnh, `pointAmount` < min hoặc > max
   - Expected: Trả về lỗi validation

5. **Mua gói point với coupon không hợp lệ**
   - Input: `pointId`, `couponCode` không hợp lệ
   - Expected: Tạo giao dịch với giá gốc, không áp dụng giảm giá

#### Rủi ro tiềm ẩn:
- **Tính toán giá tiền không chính xác**: Khi áp dụng coupon hoặc tính toán giá cho gói tùy chỉnh
- **Race condition**: Khi nhiều request mua point cùng lúc
- **Xử lý coupon không đúng**: Áp dụng coupon đã hết hạn hoặc đã sử dụng
- **Không kiểm tra giới hạn**: Cho phép mua số lượng point ngoài khoảng cho phép
- **Không xác thực người dùng**: Cho phép mua point mà không kiểm tra token

### 3.2. API Webhook (`POST /r-point/webhook`)

#### Kịch bản kiểm tra:
1. **Webhook với giao dịch thành công**
   - Input: Dữ liệu webhook với trạng thái thành công
   - Expected: Cập nhật trạng thái giao dịch, cộng point cho người dùng

2. **Webhook với giao dịch thất bại**
   - Input: Dữ liệu webhook với trạng thái thất bại
   - Expected: Cập nhật trạng thái giao dịch, không cộng point

3. **Webhook với dữ liệu không hợp lệ**
   - Input: Dữ liệu webhook không đúng định dạng
   - Expected: Trả về lỗi validation

4. **Webhook với mã giao dịch không tồn tại**
   - Input: Dữ liệu webhook với mã giao dịch không tồn tại
   - Expected: Trả về lỗi không tìm thấy giao dịch

5. **Webhook với signature không hợp lệ**
   - Input: Dữ liệu webhook với signature không hợp lệ
   - Expected: Trả về lỗi xác thực

#### Rủi ro tiềm ẩn:
- **Xác thực webhook không đúng**: Không kiểm tra signature hoặc API key
- **Double processing**: Xử lý cùng một webhook nhiều lần
- **Race condition**: Khi nhiều webhook cùng cập nhật một giao dịch
- **Không kiểm tra số tiền**: Cập nhật giao dịch mà không kiểm tra số tiền
- **Không ghi log**: Không lưu lại thông tin webhook để debug

### 3.3. API Lấy lịch sử giao dịch (`GET /r-point/payment/history`)

#### Kịch bản kiểm tra:
1. **Lấy lịch sử giao dịch với phân trang**
   - Input: `page=1&limit=10`
   - Expected: Trả về danh sách giao dịch của người dùng với phân trang

2. **Lấy lịch sử giao dịch với filter theo trạng thái**
   - Input: `status=CONFIRMED`
   - Expected: Trả về danh sách giao dịch có trạng thái CONFIRMED

3. **Lấy lịch sử giao dịch với filter theo thời gian**
   - Input: `startTime=...&endTime=...`
   - Expected: Trả về danh sách giao dịch trong khoảng thời gian

4. **Lấy lịch sử giao dịch với tìm kiếm**
   - Input: `search=...`
   - Expected: Trả về danh sách giao dịch có mã tham chiếu chứa từ khóa

5. **Lấy lịch sử giao dịch không có dữ liệu**
   - Input: Filter không có kết quả
   - Expected: Trả về danh sách rỗng với meta data phù hợp

#### Rủi ro tiềm ẩn:
- **Không kiểm tra quyền truy cập**: Cho phép người dùng xem giao dịch của người khác
- **Không xử lý phân trang đúng**: Trả về quá nhiều dữ liệu
- **Không xử lý filter đúng**: Trả về dữ liệu không phù hợp với filter
- **SQL Injection**: Không xử lý đúng các tham số tìm kiếm

### 3.4. API Xác thực Coupon (`POST /r-point/coupons/validate`)

#### Kịch bản kiểm tra:
1. **Xác thực coupon hợp lệ**
   - Input: `couponCode` hợp lệ, `pointId` và `pointAmount` phù hợp
   - Expected: Trả về thông tin coupon và giá sau khi giảm

2. **Xác thực coupon đã hết hạn**
   - Input: `couponCode` đã hết hạn
   - Expected: Trả về lỗi coupon đã hết hạn

3. **Xác thực coupon đã sử dụng hết**
   - Input: `couponCode` đã sử dụng hết
   - Expected: Trả về lỗi coupon đã sử dụng hết

4. **Xác thực coupon không áp dụng cho gói point**
   - Input: `couponCode` không áp dụng cho `pointId`
   - Expected: Trả về lỗi coupon không áp dụng

5. **Xác thực coupon với số lượng point không đủ**
   - Input: `couponCode` yêu cầu số lượng point tối thiểu > `pointAmount`
   - Expected: Trả về lỗi không đủ điều kiện áp dụng

#### Rủi ro tiềm ẩn:
- **Tính toán giảm giá không chính xác**: Khi áp dụng các loại giảm giá khác nhau
- **Không kiểm tra điều kiện áp dụng**: Cho phép áp dụng coupon không phù hợp
- **Không kiểm tra thời hạn**: Cho phép sử dụng coupon đã hết hạn
- **Không kiểm tra số lượng**: Cho phép sử dụng coupon đã hết lượt

### 3.5. API Quản lý Point của Admin

#### Kịch bản kiểm tra:
1. **Tạo gói point mới**
   - Input: Thông tin gói point hợp lệ
   - Expected: Tạo gói point thành công

2. **Tạo gói point với dữ liệu không hợp lệ**
   - Input: Thông tin gói point không hợp lệ (ví dụ: rate < 0)
   - Expected: Trả về lỗi validation

3. **Cập nhật gói point**
   - Input: ID gói point và thông tin cập nhật
   - Expected: Cập nhật gói point thành công

4. **Xóa gói point**
   - Input: ID gói point
   - Expected: Xóa gói point thành công

5. **Xóa gói point đang được sử dụng**
   - Input: ID gói point đang có giao dịch liên quan
   - Expected: Trả về lỗi không thể xóa

#### Rủi ro tiềm ẩn:
- **Không kiểm tra quyền admin**: Cho phép người dùng thông thường truy cập API admin
- **Không kiểm tra ràng buộc dữ liệu**: Cho phép tạo gói point với dữ liệu không hợp lệ
- **Không kiểm tra ràng buộc quan hệ**: Cho phép xóa gói point đang được sử dụng
- **Không xử lý concurrent update**: Khi nhiều admin cùng cập nhật một gói point

## 4. Chiến lược kiểm thử

### 4.1. Kiểm thử đơn vị (Unit Testing)
- Kiểm tra các service với các mock repository
- Kiểm tra các logic phức tạp như tính toán giá, xác thực coupon

### 4.2. Kiểm thử tích hợp (Integration Testing)
- Kiểm tra luồng hoàn chỉnh từ controller đến repository
- Kiểm tra tương tác giữa các module

### 4.3. Kiểm thử API (API Testing)
- Kiểm tra các endpoint với các tham số khác nhau
- Kiểm tra phản hồi API theo đúng định dạng

### 4.4. Kiểm thử bảo mật (Security Testing)
- Kiểm tra xác thực và phân quyền
- Kiểm tra xử lý dữ liệu đầu vào
- Kiểm tra xác thực webhook

### 4.5. Kiểm thử hiệu năng (Performance Testing)
- Kiểm tra thời gian phản hồi của API
- Kiểm tra xử lý đồng thời nhiều request

## 5. Công cụ kiểm thử

1. **Jest**: Framework kiểm thử đơn vị
2. **Supertest**: Kiểm thử HTTP request
3. **Postman**: Kiểm thử API thủ công
4. **JMeter**: Kiểm thử hiệu năng
5. **OWASP ZAP**: Kiểm thử bảo mật

## 6. Ưu tiên kiểm thử

Dựa trên mức độ rủi ro và tầm quan trọng, các API sau đây cần được ưu tiên kiểm thử:

1. **API Mua R-Point** (`POST /r-point/payment/purchase`): Liên quan trực tiếp đến tiền
2. **API Webhook** (`POST /r-point/webhook`): Xử lý thanh toán và cập nhật point
3. **API Xác thực Coupon** (`POST /r-point/coupons/validate`): Ảnh hưởng đến giá tiền
4. **API Quản lý Point của Admin**: Ảnh hưởng đến toàn bộ hệ thống

## 7. Kế hoạch thực hiện

### Giai đoạn 1: Chuẩn bị
- Thiết lập môi trường kiểm thử
- Chuẩn bị dữ liệu kiểm thử
- Xác định các test case chi tiết

### Giai đoạn 2: Kiểm thử đơn vị và tích hợp
- Thực hiện kiểm thử đơn vị cho các service
- Thực hiện kiểm thử tích hợp cho các luồng chính

### Giai đoạn 3: Kiểm thử API
- Thực hiện kiểm thử API cho tất cả các endpoint
- Kiểm tra các trường hợp thành công và thất bại

### Giai đoạn 4: Kiểm thử bảo mật và hiệu năng
- Thực hiện kiểm thử bảo mật
- Thực hiện kiểm thử hiệu năng

### Giai đoạn 5: Sửa lỗi và kiểm thử lại
- Sửa các lỗi phát hiện được
- Kiểm thử lại để đảm bảo lỗi đã được khắc phục

## 8. Kết luận

Module R-Point là một phần quan trọng của hệ thống, liên quan trực tiếp đến tiền và point của người dùng. Việc kiểm thử kỹ lưỡng là cần thiết để đảm bảo tính chính xác và an toàn của hệ thống.

Các rủi ro chính cần chú ý:
- Tính toán giá tiền và point không chính xác
- Xác thực webhook không đúng
- Không kiểm tra quyền truy cập
- Race condition trong các giao dịch đồng thời
- Xử lý coupon không đúng

Kế hoạch kiểm thử này sẽ giúp phát hiện và khắc phục các rủi ro tiềm ẩn, đảm bảo module R-Point hoạt động đúng và an toàn.
