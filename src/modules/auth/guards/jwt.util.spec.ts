import { Test, TestingModule } from '@nestjs/testing';
import { JwtUtilService, TokenType, JwtPayload } from './jwt.util';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';

describe('JwtUtilService', () => {
  let service: JwtUtilService;
  let jwtService: JwtService;
  let configService: ConfigService;

  const mockJwtService = {
    sign: jest.fn().mockReturnValue('mock-token'),
    verify: jest.fn(),
    decode: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key, defaultValue) => {
      const config = {
        JWT_SECRET: 'test-secret',
        JWT_ACCESS_TOKEN_EXPIRATION_TIME: '1d',
        JWT_REFRESH_TOKEN_EXPIRATION_TIME: '7d',
        NODE_ENV: 'test',
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtUtilService,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<JwtUtilService>(JwtUtilService);
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateUserAccessToken', () => {
    it('should generate an access token for a user', () => {
      // Arrange
      const payload = {
        sub: 1,
        username: '<EMAIL>',
        permissions: ['read:profile'],
      };

      // Act
      const result = service.generateUserAccessToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          permissions: ['read:profile'],
          isAdmin: false,
          typeToken: TokenType.ACCESS,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '1d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 86400, // 1 day in seconds
      });
    });
  });

  describe('generateUserVerifyToken', () => {
    it('should generate a verification token for a user', () => {
      // Arrange
      const payload = {
        sub: 1,
        username: '<EMAIL>',
      };

      // Act
      const result = service.generateUserVerifyToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          isAdmin: false,
          typeToken: TokenType.VERIFY,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '1d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 86400, // 1 day in seconds
      });
    });
  });

  describe('generateUserForgotPasswordToken', () => {
    it('should generate a forgot password token for a user', () => {
      // Arrange
      const payload = {
        sub: 1,
        username: '<EMAIL>',
      };

      // Act
      const result = service.generateUserForgotPasswordToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          isAdmin: false,
          typeToken: TokenType.FORGOT_PASSWORD,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '1d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 86400, // 1 day in seconds
      });
    });
  });

  describe('generateUserChangePasswordToken', () => {
    it('should generate a change password token for a user', () => {
      // Arrange
      const payload = {
        sub: 1,
        username: '<EMAIL>',
      };

      // Act
      const result = service.generateUserChangePasswordToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          isAdmin: false,
          typeToken: TokenType.CHANGE_PASSWORD,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '1d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 86400, // 1 day in seconds
      });
    });
  });

  describe('generateEmployeeAccessToken', () => {
    it('should generate an access token for an employee with admin flag', () => {
      // Arrange
      const payload = {
        sub: 1,
        username: '<EMAIL>',
        permissions: ['admin:all'],
      };

      // Act
      const result = service.generateEmployeeAccessToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          permissions: ['admin:all'],
          isAdmin: true,
          typeToken: TokenType.ACCESS,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '1d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 86400, // 1 day in seconds
      });
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate a refresh token with the correct expiration time', () => {
      // Arrange
      const payload: JwtPayload = {
        id: 1,
        sub: 1,
        username: '<EMAIL>',
        permissions: ['read:profile'],
        isAdmin: false,
      };

      // Act
      const result = service.generateRefreshToken(payload);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          sub: 1,
          username: '<EMAIL>',
          permissions: ['read:profile'],
          isAdmin: false,
          typeToken: TokenType.REFRESH,
        }),
        expect.objectContaining({
          secret: 'test-secret',
          expiresIn: '7d',
        }),
      );
      expect(result).toEqual({
        token: 'mock-token',
        expiresInSeconds: 604800, // 7 days in seconds
      });
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', () => {
      // Arrange
      const token = 'valid-token';
      const decodedToken = {
        id: 1,
        sub: 1,
        username: '<EMAIL>',
        typeToken: TokenType.ACCESS,
      };
      mockJwtService.verify.mockReturnValueOnce(decodedToken);

      // Act
      const result = service.verifyToken(token);

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'test-secret',
      });
      expect(result).toEqual(decodedToken);
    });

    it('should verify a token with expected type', () => {
      // Arrange
      const token = 'valid-token';
      const decodedToken = {
        id: 1,
        sub: 1,
        username: '<EMAIL>',
        typeToken: TokenType.ACCESS,
      };
      mockJwtService.verify.mockReturnValueOnce(decodedToken);

      // Act
      const result = service.verifyToken(token, TokenType.ACCESS);

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith(token, {
        secret: 'test-secret',
      });
      expect(result).toEqual(decodedToken);
    });

    it('should throw an error if token type does not match expected type', () => {
      // Arrange
      const token = 'valid-token';
      const decodedToken = {
        id: 1,
        sub: 1,
        username: '<EMAIL>',
        typeToken: TokenType.REFRESH,
      };
      mockJwtService.verify.mockReturnValueOnce(decodedToken);

      // Act & Assert
      expect(() => service.verifyToken(token, TokenType.ACCESS)).toThrow();
    });
  });

  describe('decodeToken', () => {
    it('should decode a token without verification', () => {
      // Arrange
      const token = 'any-token';
      const decodedToken = {
        id: 1,
        sub: 1,
        username: '<EMAIL>',
      };
      mockJwtService.decode.mockReturnValueOnce(decodedToken);

      // Act
      const result = service.decodeToken(token);

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith(token);
      expect(result).toEqual(decodedToken);
    });
  });
});
