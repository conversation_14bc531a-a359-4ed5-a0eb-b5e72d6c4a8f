import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

export const PERMISSIONS_KEY = 'permissions';

@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Lấy thông tin về route và method
    const handler = context.getHandler().name;
    const className = context.getClass().name;
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;

    // Ghi log thông tin user (nếu có)
    if (request.user) {
      this.logger.debug(`User in request: ${JSON.stringify({
        id: request.user.id || request.user.sub,
        username: request.user.username,
        permissions: request.user.permissions ? request.user.permissions.length : 0
      })}`);
    }

    this.logger.debug(`Checking permissions for ${method} ${url} (${className}.${handler})`);

    // Kiểm tra xem có phải kiểm tra quyền động không
    const isDynamicCheck = this.reflector.getAllAndOverride<boolean>(
      PERMISSIONS_KEY + '_DYNAMIC',
      [context.getHandler(), context.getClass()],
    );

    // Nếu là kiểm tra quyền động
    if (isDynamicCheck) {
      return this.checkDynamicPermissions(context);
    }

    // Nếu không, kiểm tra quyền thông thường
    return this.checkStaticPermissions(context);
  }

  /**
   * Kiểm tra quyền thông thường (cố định)
   */
  private checkStaticPermissions(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    // Nếu không có yêu cầu về permissions, cho phép truy cập
    if (!requiredPermissions || requiredPermissions.length === 0) {
      this.logger.debug('No permissions required, access granted');
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const userId = user?.id || user?.sub || 'unknown';

    // Nếu không có user, từ chối truy cập
    if (!user) {
      this.logger.warn(`No user found in request for ${request.method} ${request.url}, access denied`);
      throw new ForbiddenException('Bạn cần đăng nhập để thực hiện hành động này');
    }

    // Nếu không có permissions, từ chối truy cập
    if (!user.permissions || !Array.isArray(user.permissions)) {
      this.logger.warn(`User ${userId} has no permissions array, access denied`);
      throw new ForbiddenException('Tài khoản của bạn không có quyền hạn nào');
    }

    this.logger.debug(`Required permissions: ${requiredPermissions.join(', ')}`);
    this.logger.debug(`User ${userId} permissions: ${user.permissions.join(', ')}`);

    // Kiểm tra xem user có tất cả các permissions cần thiết không
    const missingPermissions = requiredPermissions.filter(
      permission => !user.permissions.includes(permission)
    );

    if (missingPermissions.length > 0) {
      this.logger.warn(`User ${userId} missing permissions: ${missingPermissions.join(', ')}`);
      throw new ForbiddenException(`Bạn không có đủ quyền để thực hiện hành động này. Quyền còn thiếu: ${missingPermissions.join(', ')}`);
    }

    this.logger.debug(`All permissions satisfied for user ${userId}, access granted`);
    return true;
  }

  /**
   * Kiểm tra quyền động dựa trên context
   */
  private checkDynamicPermissions(context: ExecutionContext): boolean {
    // Lấy hàm selector từ metadata
    const permissionSelector = this.reflector.getAllAndOverride<(context: ExecutionContext) => string[]>(
      PERMISSIONS_KEY + '_SELECTOR',
      [context.getHandler(), context.getClass()],
    );

    // Nếu không có hàm selector, cho phép truy cập
    if (!permissionSelector) {
      this.logger.debug('No permission selector found, access granted');
      return true;
    }

    try {
      const request = context.switchToHttp().getRequest();
      const { method, url } = request;

      // Gọi hàm selector để lấy danh sách quyền cần thiết
      const requiredPermissions = permissionSelector(context);
      this.logger.debug(`Dynamic permission selector returned: ${JSON.stringify(requiredPermissions)}`);

      // Nếu không có yêu cầu về permissions, cho phép truy cập
      if (!requiredPermissions || requiredPermissions.length === 0) {
        this.logger.debug(`No permissions required from selector for ${method} ${url}, access granted`);
        return true;
      }

      const { user } = request;
      const userId = user?.id || user?.sub || 'unknown';

      // Nếu không có user, từ chối truy cập
      if (!user) {
        this.logger.warn(`No user found in request for ${method} ${url}, access denied`);
        throw new ForbiddenException('Bạn cần đăng nhập để thực hiện hành động này');
      }

      // Nếu không có permissions, từ chối truy cập
      if (!user.permissions || !Array.isArray(user.permissions)) {
        this.logger.warn(`User ${userId} has no permissions array for ${method} ${url}, access denied`);
        throw new ForbiddenException('Tài khoản của bạn không có quyền hạn nào');
      }

      this.logger.debug(`Required permissions (dynamic): ${requiredPermissions.join(', ')}`);
      this.logger.debug(`User ${userId} permissions: ${user.permissions.join(', ')}`);

      // Kiểm tra xem user có tất cả các permissions cần thiết không
      const missingPermissions = requiredPermissions.filter(
        permission => !user.permissions.includes(permission)
      );

      if (missingPermissions.length > 0) {
        this.logger.warn(`User ${userId} missing permissions for ${method} ${url}: ${missingPermissions.join(', ')}`);
        throw new ForbiddenException(`Bạn không có đủ quyền để thực hiện hành động này. Quyền còn thiếu: ${missingPermissions.join(', ')}`);
      }

      this.logger.debug(`All permissions satisfied (dynamic) for user ${userId}, access granted`);
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      // Log chi tiết lỗi và context
      const request = context.switchToHttp().getRequest();
      const { method, url } = request;
      const handler = context.getHandler().name;
      const className = context.getClass().name;

      this.logger.error(
        `Error in dynamic permission check for ${method} ${url} (${className}.${handler}): ${error.message}`,
        error.stack
      );

      throw new ForbiddenException('Lỗi khi kiểm tra quyền truy cập');
    }
  }
}