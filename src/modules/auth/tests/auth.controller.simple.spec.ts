import { Test, TestingModule } from '@nestjs/testing';

// Mock the AuthService
const mockAuthService = {
  login: jest.fn(),
  register: jest.fn(),
  verifyOtp: jest.fn(),
  forgotPassword: jest.fn(),
  verifyForgotPassword: jest.fn(),
  resetPassword: jest.fn(),
};

// Create a simple AuthController class that uses the mock service
class AuthController {
  constructor(private authService) {}

  async login(loginDto, response) {
    const result = await this.authService.login(loginDto);
    if (result.refreshToken) {
      response.cookie('refresh_token', result.refreshToken);
    }
    return result.result;
  }

  async register(registerDto) {
    return await this.authService.register(registerDto);
  }

  async verifyOtp(verifyOtpDto, response) {
    const result = await this.authService.verifyOtp(verifyOtpDto);
    if (result.refreshToken) {
      response.cookie('refresh_token', result.refreshToken);
    }
    return result.result;
  }

  async forgotPassword(forgotPasswordDto) {
    return await this.authService.forgotPassword(forgotPasswordDto);
  }

  async verifyForgotPassword(verifyForgotPasswordDto) {
    return await this.authService.verifyForgotPassword(verifyForgotPasswordDto);
  }

  async resetPassword(resetPasswordDto) {
    return await this.authService.resetPassword(resetPasswordDto);
  }
}

// Mock Response object
const mockResponse = {
  cookie: jest.fn().mockReturnThis(),
};

describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new AuthController(mockAuthService);
  });

  describe('login', () => {
    it('should call authService.login with correct parameters and set refresh token cookie', async () => {
      // Arrange
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResult = {
        result: {
          code: 200,
          message: 'Login successful',
          result: {
            accessToken: 'access-token',
            user: { id: 1, email: '<EMAIL>' }
          }
        },
        refreshToken: 'refresh-token'
      };

      mockAuthService.login.mockResolvedValue(mockResult);

      // Act
      const result = await controller.login(loginDto, mockResponse);

      // Assert
      expect(mockAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refresh_token',
        'refresh-token'
      );
      expect(result).toEqual(mockResult.result);
    });

    it('should handle login with unverified user', async () => {
      // Arrange
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResult = {
        result: {
          code: 202,
          message: 'Vui lòng xác thực email hoặc số điện thoại',
          result: {
            verifyToken: 'verify-token',
            info: [
              { platform: 'EMAIL', value: '<EMAIL>' }
            ],
            expiresIn: 300
          }
        },
        refreshToken: ''
      };

      mockAuthService.login.mockResolvedValue(mockResult);

      // Act
      const result = await controller.login(loginDto, mockResponse);

      // Assert
      expect(mockAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(mockResponse.cookie).not.toHaveBeenCalled();
      expect(result).toEqual(mockResult.result);
    });
  });

  describe('register', () => {
    it('should call authService.register with correct parameters and return OTP information', async () => {
      // Arrange
      const registerDto = {
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '0912345678',
      };

      const mockResult = {
        code: 200,
        message: 'Registration successful',
        result: {
          otpToken: 'otp-token',
        }
      };

      mockAuthService.register.mockResolvedValue(mockResult);

      // Act
      const result = await controller.register(registerDto);

      // Assert
      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toEqual(mockResult);
    });

    it('should handle registration with existing email', async () => {
      // Arrange
      const registerDto = {
        fullName: 'Existing User',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '0912345678',
      };

      // Mock a conflict exception
      mockAuthService.register.mockRejectedValue(new Error('Email đã được sử dụng'));

      // Act & Assert
      await expect(controller.register(registerDto)).rejects.toThrow('Email đã được sử dụng');
      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
    });
  });

  describe('verifyOtp', () => {
    it('should call authService.verifyOtp with correct parameters and set refresh token cookie', async () => {
      // Arrange
      const verifyOtpDto = {
        otp: '123456',
        otpToken: 'otp-token',
      };

      const mockResult = {
        result: {
          accessToken: 'access-token',
          user: { id: 1, email: '<EMAIL>' }
        },
        refreshToken: 'refresh-token'
      };

      mockAuthService.verifyOtp.mockResolvedValue(mockResult);

      // Act
      const result = await controller.verifyOtp(verifyOtpDto, mockResponse);

      // Assert
      expect(mockAuthService.verifyOtp).toHaveBeenCalledWith(verifyOtpDto);
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refresh_token',
        'refresh-token'
      );
      expect(result).toEqual(mockResult.result);
    });

    it('should handle invalid OTP', async () => {
      // Arrange
      const verifyOtpDto = {
        otp: 'invalid',
        otpToken: 'otp-token',
      };

      // Mock a bad request exception
      mockAuthService.verifyOtp.mockRejectedValue(new Error('OTP không chính xác'));

      // Act & Assert
      await expect(controller.verifyOtp(verifyOtpDto, mockResponse)).rejects.toThrow('OTP không chính xác');
      expect(mockAuthService.verifyOtp).toHaveBeenCalledWith(verifyOtpDto);
    });
  });

  describe('forgotPassword', () => {
    it('should call authService.forgotPassword with correct parameters', async () => {
      // Arrange
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      const mockResult = {
        code: 200,
        message: 'OTP sent',
        result: {
          otpToken: 'otp-token',
        }
      };

      mockAuthService.forgotPassword.mockResolvedValue(mockResult);

      // Act
      const result = await controller.forgotPassword(forgotPasswordDto);

      // Assert
      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith(forgotPasswordDto);
      expect(result).toEqual(mockResult);
    });

    it('should handle non-existent email', async () => {
      // Arrange
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      // Mock a not found exception
      mockAuthService.forgotPassword.mockRejectedValue(new Error('Email không tồn tại trong hệ thống'));

      // Act & Assert
      await expect(controller.forgotPassword(forgotPasswordDto)).rejects.toThrow('Email không tồn tại trong hệ thống');
      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith(forgotPasswordDto);
    });
  });

  describe('verifyForgotPassword', () => {
    it('should call authService.verifyForgotPassword with correct parameters', async () => {
      // Arrange
      const verifyForgotPasswordDto = {
        otp: '123456',
        otpToken: 'otp-token',
      };

      const mockResult = {
        code: 200,
        message: 'OTP verified',
        result: {
          changePasswordToken: 'change-password-token',
        }
      };

      mockAuthService.verifyForgotPassword.mockResolvedValue(mockResult);

      // Act
      const result = await controller.verifyForgotPassword(verifyForgotPasswordDto);

      // Assert
      expect(mockAuthService.verifyForgotPassword).toHaveBeenCalledWith(verifyForgotPasswordDto);
      expect(result).toEqual(mockResult);
    });

    it('should handle invalid OTP', async () => {
      // Arrange
      const verifyForgotPasswordDto = {
        otp: 'invalid',
        otpToken: 'otp-token',
      };

      // Mock a bad request exception
      mockAuthService.verifyForgotPassword.mockRejectedValue(new Error('OTP không chính xác'));

      // Act & Assert
      await expect(controller.verifyForgotPassword(verifyForgotPasswordDto)).rejects.toThrow('OTP không chính xác');
      expect(mockAuthService.verifyForgotPassword).toHaveBeenCalledWith(verifyForgotPasswordDto);
    });
  });

  describe('resetPassword', () => {
    it('should call authService.resetPassword with correct parameters', async () => {
      // Arrange
      const resetPasswordDto = {
        newPassword: 'newPassword123',
        changePasswordToken: 'change-password-token',
      };

      const mockResult = {
        code: 200,
        message: 'Password reset successful',
      };

      mockAuthService.resetPassword.mockResolvedValue(mockResult);

      // Act
      const result = await controller.resetPassword(resetPasswordDto);

      // Assert
      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(resetPasswordDto);
      expect(result).toEqual(mockResult);
    });

    it('should handle invalid token', async () => {
      // Arrange
      const resetPasswordDto = {
        newPassword: 'newPassword123',
        changePasswordToken: 'invalid-token',
      };

      // Mock a bad request exception
      mockAuthService.resetPassword.mockRejectedValue(new Error('Token không hợp lệ hoặc đã hết hạn'));

      // Act & Assert
      await expect(controller.resetPassword(resetPasswordDto)).rejects.toThrow('Token không hợp lệ hoặc đã hết hạn');
      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(resetPasswordDto);
    });
  });
});
