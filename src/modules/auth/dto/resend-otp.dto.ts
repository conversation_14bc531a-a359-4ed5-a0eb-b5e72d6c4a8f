import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc gửi lại OTP
 */
export class ResendOtpDto {
  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString({ message: 'Token OTP phải là chuỗi' })
  @IsNotEmpty({ message: 'Token OTP không được để trống' })
  otpToken: string;
}
