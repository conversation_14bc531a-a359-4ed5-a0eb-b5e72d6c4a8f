/**
 * Payload được mã hóa trong JWT token
 */
export interface JWTPayload {
  /**
   * ID của người dùng
   */
  id: string | number;

  /**
   * Địa chỉ email
   */
  email: string;

  /**
   * Vai trò của người dùng
   */
  role?: string;

  /**
   * Thời gian hết hạn của token (timestamp)
   */
  exp?: number;

  /**
   * Thời gian token được tạo (timestamp)
   */
  iat?: number;

  /**
   * Loại token (access, refresh)
   */
  type?: string;
}

export * from './jwt-payload-employee.interface';