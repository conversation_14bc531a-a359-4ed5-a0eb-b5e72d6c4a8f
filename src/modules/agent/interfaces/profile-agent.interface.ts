import { GenderEnum } from '@modules/agent/constants/gender.enum';

export interface ProfileAgent {
  gender?: GenderEnum; // <PERSON>i<PERSON><PERSON> t<PERSON> (ví dụ: "male", "female", "other")
  dateOfBirth?: string | Date; // <PERSON><PERSON><PERSON> (có thể là chuỗi ISO hoặc Date)
  position?: string; // Chức vụ (ví dụ: "Developer", "Manager")
  education?: string; // Trình độ học vấn (ví dụ: "Bachelor", "Master")
  skills?: string[]; // <PERSON><PERSON> năng (danh sách chuỗi, ví dụ: ["JavaScript", "Python"])
  personality?: string[]; // T<PERSON>h cách (danh sách chuỗi, ví dụ: ["Creative", "Team-player"])
  languages?: string[]; // Ngôn ngữ (danh sách chuỗi, ví dụ: ["English", "Vietnamese"])
  nations?: string; // Quốc gia (danh sách chuỗi, ví dụ: ["Vietnam", "USA"])
}
