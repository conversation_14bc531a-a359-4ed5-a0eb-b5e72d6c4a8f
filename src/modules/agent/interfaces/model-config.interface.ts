/**
 * Interface định nghĩa cấu hình model cho agent
 */
export interface ModelConfig {
  /**
   * ID của model sử dụng
   */
  modelId: string;

  /**
   * ID của nhà cung cấp model (null hoặc undefined nếu là model hệ thống)
   */
  provider_id?: string | null;

  /**
   * Giá trị temperature cho model (0-2)
   */
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  top_k?: number;

  /**
   * Số token tối đa cho kết quả
   */
  max_tokens?: number;

  /**
   * Tên của nhà cung cấp model
   */
  providerName?: string;
}
