import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentSystem } from '@modules/agent/entities';

/**
 * Repository cho AgentSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent hệ thống
 */
@Injectable()
export class AgentSystemRepository extends Repository<AgentSystem> {
  private readonly logger = new Logger(AgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentSystem
   * @returns SelectQueryBuilder cho AgentSystem
   */
  private createBaseQuery(): SelectQueryBuilder<AgentSystem> {
    return this.createQueryBuilder('agentSystem');
  }

  /**
   * Tìm agent hệ thống theo ID
   * @param id ID của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.id = :id', { id })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  /**
   * Tìm agent hệ thống theo mã định danh
   * @param nameCode Mã định danh của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByNameCode(nameCode: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.nameCode = :nameCode', { nameCode })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  async findAllActive(): Promise<AgentSystem[]> {
    return this.createBaseQuery()
      .where('agentSystem.deletedBy IS NULL')
      .getMany();
  }
}
