import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgent } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { CreateTypeAgentDto, TypeAgentQueryDto, UpdateTypeAgentDto } from '@modules/agent/user/dto';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho TypeAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến loại agent
 */
@Injectable()
export class TypeAgentRepository extends Repository<TypeAgent> {
  private readonly logger = new Logger(TypeAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(TypeAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho TypeAgent
   * @returns SelectQueryBuilder cho TypeAgent
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgent> {
    return this.createQueryBuilder('typeAgent');
  }

  /**
   * Tìm loại agent theo ID
   * @param id ID của loại agent
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number, includeDeleted: boolean = false): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.id = :id', { id });

    // Nếu không bao gồm các bản ghi đã bị xóa mềm
    if (!includeDeleted) {
      query.andWhere('typeAgent.deletedAt IS NULL');
    }

    return query.getOne();
  }

  /**
   * Tìm loại agent theo tên
   * @param name Tên của loại agent
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string, includeDeleted: boolean = false): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.name = :name', { name });

    // Nếu không bao gồm các bản ghi đã bị xóa mềm
    if (!includeDeleted) {
      query.andWhere('typeAgent.deletedAt IS NULL');
    }

    return query.getOne();
  }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái loại agent (tùy chọn)
   * @param userId ID của người dùng (tùy chọn, nếu cần lọc theo người dùng)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách loại agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: TypeAgentStatus,
    userId?: number,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy các bản ghi chưa bị xóa mềm
    qb.andWhere('typeAgent.deletedAt IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Đối với user, chỉ hiển thị các loại agent có status là APPROVED hoặc do chính user tạo
    if (userId) {
      qb.andWhere('(typeAgent.userId = :userId OR typeAgent.status = :approvedStatus)', {
        userId,
        approvedStatus: TypeAgentStatus.APPROVED
      });
    } else if (status) {
      // Nếu không phải user view (admin view) và có truyền status, thì lọc theo status
      qb.andWhere('typeAgent.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Lấy danh sách tất cả loại agent
   * @returns Danh sách loại agent
   */
  async findAll(): Promise<TypeAgent[]> {
    return this.createBaseQuery().getMany();
  }

  /**
   * Xóa mềm tùy chỉnh cho TypeAgent
   * @param id ID của loại agent cần xóa mềm
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa mềm
   */
  async customSoftDelete(id: number, employeeId: number): Promise<boolean> {
    try {
      // Cập nhật trường deletedAt và deletedBy
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          deletedAt: Date.now(),
          deletedBy: employeeId
        })
        .where('id = :id', { id })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Error in customSoftDelete: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách loại agent với phân trang theo query DTO
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách loại agent với phân trang
   */
  async findPaginatedByQuery(
    queryDto: TypeAgentQueryDto,
    userId: number,
  ): Promise<PaginatedResult<TypeAgent>> {
    try {
      const qb = this.createBaseQuery();

      // Thêm điều kiện tìm kiếm nếu có
      if (queryDto.search) {
        qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
          { search: `%${queryDto.search}%` });
      }

      // Chỉ lấy các bản ghi chưa bị xóa mềm
      qb.andWhere('typeAgent.deletedAt IS NULL');

      // Lọc theo loại agent (system hay user)
      if (queryDto.isSystem !== undefined) {
        if (!queryDto.isSystem) {
          // Đối với user, chỉ hiển thị các loại agent có status là APPROVED hoặc do chính user tạo
          qb.andWhere('(typeAgent.userId = :userId)', {
            userId,
          });
        } else {
          qb.andWhere('(typeAgent.status = :approvedStatus AND typeAgent.userId IS NULL)', {
            approvedStatus: TypeAgentStatus.APPROVED
          });
        }
      } else {
        // Đối với user, chỉ hiển thị các loại agent có status là APPROVED hoặc do chính user tạo
        qb.andWhere('((typeAgent.userId = :userId) OR (typeAgent.status = :approvedStatus AND typeAgent.userId IS NULL))', {
          userId,
          approvedStatus: TypeAgentStatus.APPROVED
        });
      }

      // Thêm phân trang và sắp xếp
      qb.skip((queryDto.page - 1) * queryDto.limit)
        .take(queryDto.limit)
        .orderBy(`typeAgent.${queryDto.sortBy}`, queryDto.sortDirection);

      const [items, total] = await qb.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách loại agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo loại agent mới
   * @param userId ID của người dùng
   * @param createDto Thông tin loại agent mới
   * @returns ID của loại agent mới
   */
  @Transactional()
  async createTypeAgent(
    userId: number,
    createDto: CreateTypeAgentDto,
  ): Promise<TypeAgent> {
    try {
      // Tạo loại agent mới
      const typeAgent = this.create({
        name: createDto.name,
        description: createDto.description,
        config: createDto.config,
        userId: userId,
        status: TypeAgentStatus.APPROVED, // Đặt mặc định là APPROVED cho user
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Lưu loại agent
      return await this.save(typeAgent);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo loại agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   */
  @Transactional()
  async updateTypeAgent(
    id: number,
    userId: number,
    updateDto: UpdateTypeAgentDto,
  ): Promise<void> {
    try {
      // Tạo đối tượng cập nhật
      const updateData: Partial<TypeAgent> = {
        updatedAt: Date.now(),
      };

      // Thêm các trường cần cập nhật
      if (updateDto.name) {
        updateData.name = updateDto.name;
      }
      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description;
      }
      if (updateDto.config) {
        updateData.config = updateDto.config;
      }

      // Cập nhật loại agent
      const qb = this.createQueryBuilder()
        .update(TypeAgent)
        .set(updateData)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .andWhere('deletedAt IS NULL');

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa mềm loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async softDeleteTypeAgent(id: number, userId: number): Promise<void> {
    try {
      // Xóa mềm loại agent
      const qb = this.createQueryBuilder()
        .update(TypeAgent)
        .set({ deletedAt: Date.now() })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .andWhere('deletedAt IS NULL');

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED, error.message);
    }
  }
}
