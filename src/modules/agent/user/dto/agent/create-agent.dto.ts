import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho cấu hình model
 */
export class ModelConfigDto {
  /**
   * ID của model
   */
  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4o',
  })
  @IsString()
  @IsNotEmpty()
  modelId: string;

  /**
   * ID của nhà cung cấp model (null hoặc undefined nếu là model hệ thống)
   */
  @ApiProperty({
    description: 'ID của nhà cung cấp model (null hoặc undefined nếu là model hệ thống)',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0',
  })
  @IsString()
  @IsOptional()
  provider_id?: string;

  /**
   * <PERSON><PERSON><PERSON><PERSON> độ (temperature)
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> độ (temperature)',
    example: 0.7,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number;

  /**
   * Top P
   */
  @ApiProperty({
    description: 'Top P',
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p: number;

  /**
   * Top K
   */
  @ApiProperty({
    description: 'Top K',
    example: 40,
  })
  @IsNumber()
  @Min(0)
  top_k: number;

  /**
   * Số token tối đa
   */
  @ApiProperty({
    description: 'Số token tối đa',
    example: 1000,
  })
  @IsNumber()
  @Min(1)
  max_tokens: number;
}

/**
 * DTO cho thông tin profile của agent
 */
export class ProfileDto {
  /**
   * Giới tính
   */
  @ApiProperty({
    description: 'Giới tính',
    example: 'MALE',
  })
  @IsString()
  gender: string;

  /**
   * Ngày sinh (timestamp millis)
   */
  @ApiProperty({
    description: 'Ngày sinh (timestamp millis)',
    example: 946684800000,
  })
  @IsNumber()
  dateOfBirth: number;

  /**
   * Vị trí
   */
  @ApiProperty({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  @IsString()
  position: string;

  /**
   * Học vấn
   */
  @ApiProperty({
    description: 'Học vấn',
    example: 'Đại học',
  })
  @IsString()
  education: string;

  /**
   * Kỹ năng
   */
  @ApiProperty({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  skills: string[];

  /**
   * Tính cách
   */
  @ApiProperty({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  personality: string[];

  /**
   * Ngôn ngữ
   */
  @ApiProperty({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  languages: string[];

  /**
   * Quốc gia
   */
  @ApiProperty({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  @IsString()
  nations: string;
}


/**
 * DTO cho việc tạo agent mới
 */
export class CreateAgentDto {
  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn (instruction)
   */
  @ApiProperty({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  instruction: string;

  /**
   * Thông tin profile
   */
  @ApiProperty({
    description: 'Thông tin profile',
    type: ProfileDto,
  })
  @ValidateNested()
  @Type(() => ProfileDto)
  profile: ProfileDto;

  /**
   * ID của vector store
   */
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;

  /**
   * Cấu hình strategy
   */
  @ApiPropertyOptional({
    description: 'Cấu hình strategy',
    example: { key1: 'value1', key2: 'value2' },
  })
  @IsObject()
  @IsOptional()
  strategyConfig?: Record<string, any>;

  /**
   * Danh sách ID của media
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của media',
    example: ['media-1', 'media-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

  /**
   * Danh sách ID của sản phẩm
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của sản phẩm',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  productIds?: string[];

  /**
   * Danh sách ID của URL
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URL',
    example: ['url-1', 'url-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];
}
