import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tham số truy vấn khi lấy thông tin strategy của agent
 */
export class GetAgentStrategyDto {
  /**
   * ID của phiên bản strategy (nếu không cung cấp, sẽ sử dụng phiên bản mới nhất)
   */
  @ApiPropertyOptional({
    description: 'ID của phiên bản strategy (nếu không cung cấp, sẽ sử dụng phiên bản mới nhất)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  versionId?: number;
}
