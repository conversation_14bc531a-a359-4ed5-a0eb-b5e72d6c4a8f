import { VectorStoreDto } from '../dto/common';

/**
 * Mapper cho việc chuyển đổi thông tin vector store sang DTO
 */
export class VectorStoreMapper {
  /**
   * Chuyển đổi thông tin vector store sang DTO
   * @param vectorStoreId ID của vector store
   * @param vectorStoreName Tên của vector store (nếu có)
   * @returns VectorStoreDto
   */
  static toDto(
    vectorStoreId: string,
    vectorStoreName?: string
  ): VectorStoreDto {
    return {
      vectorStoreId: vectorStoreId,
      vectorStoreName: vectorStoreName || 'Vector Store',
    };
  }
}
