import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { TypeAgentStatus } from '@modules/agent/constants';

/**
 * DTO cho việc cập nhật trạng thái loại agent
 */
export class UpdateTypeAgentStatusDto {
  /**
   * Trạng thái mới của loại agent
   */
  @ApiProperty({
    description: 'Trạng thái mới của loại agent',
    enum: TypeAgentStatus,
    example: TypeAgentStatus.DRAFT,
  })
  @IsEnum(TypeAgentStatus)
  @IsNotEmpty()
  status: TypeAgentStatus;
}
