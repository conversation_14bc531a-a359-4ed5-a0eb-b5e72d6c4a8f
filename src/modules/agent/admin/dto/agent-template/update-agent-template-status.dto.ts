import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { AgentTemplateStatus } from '@modules/agent/constants';

/**
 * DTO cho vi<PERSON>c cập nhật trạng thái agent template
 */
export class UpdateAgentTemplateStatusDto {
  /**
   * Trạng thái mới của agent template
   */
  @ApiProperty({
    description: 'Trạng thái mới của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.ACTIVE,
  })
  @IsEnum(AgentTemplateStatus)
  @IsNotEmpty()
  status: AgentTemplateStatus;
}
