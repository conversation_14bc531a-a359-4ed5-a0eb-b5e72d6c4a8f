import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { Transform } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp của agent base
 */
export enum AgentBaseSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  ACTIVE = 'active'
}

/**
 * DTO cho việc truy vấn danh sách agent base
 */
export class AgentBaseQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái active
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái active',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active?: boolean;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentBaseSortBy,
    example: AgentBaseSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentBaseSortBy)
  sortBy?: AgentBaseSortBy = AgentBaseSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
