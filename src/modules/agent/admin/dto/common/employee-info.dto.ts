import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin người tạo/cập nhật/xóa
 */
export class EmployeeInfoDto {
  /**
   * ID của nhân viên
   */
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1,
  })
  employeeId: number;

  /**
   * Tên của nhân viên
   */
  @ApiProperty({
    description: 'Tên của nhân viên',
    example: '<PERSON>',
  })
  name: string;

  /**
   * Avatar của nhân viên
   */
  @ApiPropertyOptional({
    description: 'Avatar của nhân viên',
    example: 'https://example.com/avatar.jpg',
  })
  avatar: string | null;

  /**
   * Thời gian tạo
   */
  @ApiPropertyOptional({
    description: 'Thời gian tạo',
    example: 1682506892000,
  })
  date?: number;
}
