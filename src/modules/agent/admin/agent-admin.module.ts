import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { ModelTrainingModule } from '@modules/model-training/model-training.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { BaseModelRepository } from '@modules/model-training/repositories';
import { Agent, AgentUser, TypeAgent } from '@modules/agent/entities';
import {
  AdminGroupToolMappingRepository,
  AdminGroupToolRepository,
  AdminGroupToolsTypeAgentRepository,
  AdminToolRepository,
  AdminToolVersionRepository,
  TypeAgentRepository as ToolsTypeAgentRepository,
  UserToolRepository,
} from '@/modules/tools/repositories';
import { ToolFunctionValidationHelper } from '@/modules/tools/helpers/tool-function-validation.helper';
import {
  AdminAgentSystemController,
  AdminTypeAgentController,
  AgentRankAdminController,
  AdminUserAgentController,
} from '@modules/agent/admin/controllers';
import {
  AdminAgentSystemService,
  AdminTypeAgentService,
  AgentRankAdminService,
  AdminUserAgentService,
} from '@modules/agent/admin/services';
import { MultiAgentsSystemController } from './controllers/multi-agents-system.controller';
import { MultiAgentsSystemService } from './services/multi-agents-system.service';
import { TypeAgentRepository, AgentRankRepository } from '@modules/agent/repositories';
import { AgentRank } from '@modules/agent/entities';
import { AdminAgentBaseController } from './controllers/admin-agent-base.controller';
import { AdminAgentBaseService } from './services/admin-agent-base.service';
import { AdminAgentTemplateController } from './controllers/admin-agent-template.controller';
import { AdminAgentTemplateService } from './services/admin-agent-template.service';
import { AdminAgentRoleController } from './controllers/admin-agent-role.controller';
import { AdminAgentRoleService } from './services/admin-agent-role.service';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentBaseRepository } from '@modules/agent/repositories/agent-base.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { AgentRoleRepository } from '@modules/agent/repositories/agent-role.repository';
import { MultiAgentsSystemRepository } from '@modules/agent/repositories/multi-agents-system.repository';
import { GroupToolHelper } from './helpers/group-tool.helper';
import {
  VectorStoreFileRepository,
  VectorStoreRepository,
} from '@modules/data/knowledge-files/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([AgentRank, Agent, AgentUser, TypeAgent]),
    ToolsModule,
    ModelTrainingModule,
    EmployeeModule,
    KnowledgeFilesModule,
  ],
  controllers: [
    AdminTypeAgentController,
    AdminAgentSystemController,
    AdminAgentBaseController,
    AdminAgentTemplateController,
    AdminAgentRoleController,
    MultiAgentsSystemController,
    AgentRankAdminController,
    AdminUserAgentController,
  ],
  providers: [
    S3Service,
    CdnService,
    ToolFunctionValidationHelper,
    AdminToolRepository,
    AdminToolVersionRepository,
    UserToolRepository,
    AdminGroupToolRepository,
    AdminGroupToolMappingRepository,
    AdminGroupToolsTypeAgentRepository,
    ToolsTypeAgentRepository,
    BaseModelRepository,
    AdminTypeAgentService,
    TypeAgentRepository,
    AdminAgentSystemService,
    AdminAgentBaseService,
    AdminAgentTemplateService,
    AgentRepository,
    AgentSystemRepository,
    AgentBaseRepository,
    AgentTemplateRepository,
    AgentRoleRepository,
    GroupToolHelper,
    VectorStoreRepository,
    VectorStoreFileRepository,
    AdminAgentRoleService,
    MultiAgentsSystemService,
    MultiAgentsSystemRepository,
    AgentRankAdminService,
    AgentRankRepository,
    AdminUserAgentService,
  ],
  exports: [
    AdminTypeAgentService,
    AdminAgentSystemService,
    AdminAgentBaseService,
    AdminAgentTemplateService,
    AdminAgentRoleService,
    MultiAgentsSystemService,
    AgentRankAdminService,
    AdminUserAgentService,
  ],
})
export class AgentAdminModule {}
