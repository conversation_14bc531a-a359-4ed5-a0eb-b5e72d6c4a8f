import { ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AdminAgentSystemService } from '@modules/agent/admin/services';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import {
  AgentSystemDetailDto,
  AgentSystemListItemDto,
  AgentSystemQueryDto,
  CreateAgentSystemDto,
  UpdateAgentSystemDto,
  UpdateAgentSystemStatusDto,
} from '../dto/agent-system';

/**
 * Controller xử lý các endpoint liên quan đến Agent System cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_SYSTEM)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agents/system')
@ApiExtraModels(
  AgentSystemListItemDto,
  AgentSystemDetailDto,
  ApiResponseDto,
  PaginatedResult,
)
export class AdminAgentSystemController {
  constructor(
    private readonly adminAgentSystemService: AdminAgentSystemService,
  ) { }

  /**
   * Lấy danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent system',
    description: 'Lấy danh sách agent system với phân trang và lọc',
  })
  @ApiOkResponse({
    description: 'Danh sách agent system',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAll(
    @Query() queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    const result = await this.adminAgentSystemService.findAll(queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết agent system',
    description: 'Lấy thông tin chi tiết agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết agent system',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<AgentSystemDetailDto>> {
    const result = await this.adminAgentSystemService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo agent system mới
   * @param createDto Dữ liệu tạo agent system
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent system đã tạo và URL tải lên avatar (nếu có)
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent system mới',
    description: 'Tạo agent system mới với thông tin cung cấp',
  })
  @ApiCreatedResponse({
    description: 'Agent system đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS,
    AGENT_ERROR_CODES.MODEL_NOT_FOUND,
    AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async create(
    @Body() createDto: CreateAgentSystemDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>> {
    const result = await this.adminAgentSystemService.create(
      createDto,
      employeeId,
    );
    return ApiResponseDto.success(result, 'Tạo agent system thành công');
  }

  /**
   * Cập nhật thông tin agent system
   * @param id ID của agent system
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns URL tải lên avatar mới (nếu có)
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật agent system',
    description: 'Cập nhật thông tin agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Agent system đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS,
    AGENT_ERROR_CODES.MODEL_NOT_FOUND,
    AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateAgentSystemDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ avatarUrlUpload?: string }>> {
    const result = await this.adminAgentSystemService.update(
      id,
      updateDto,
      employeeId,
    );
    return ApiResponseDto.success(result, 'Cập nhật agent system thành công');
  }

  /**
   * Cập nhật trạng thái agent system
   * @param id ID của agent system
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái agent system',
    description: 'Cập nhật trạng thái agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Trạng thái agent system đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_STATUS_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateAgentSystemStatusDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentSystemService.updateStatus(
      id,
      updateStatusDto,
      employeeId,
    );
    return ApiResponseDto.success(
      null,
      'Cập nhật trạng thái agent system thành công',
    );
  }

  /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent system',
    description: 'Xóa agent system theo ID (soft delete)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Agent system đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentSystemService.remove(id, employeeId);
    return ApiResponseDto.success(null, 'Xóa agent system thành công');
  }

  /**
   * Gán vai trò cho agent system
   * @param id ID của agent system
   * @param roleId ID của vai trò
   * @param employeeId ID của nhân viên thực hiện
   */
  @Post(':id/roles/:roleId')
  @ApiOperation({
    summary: 'Gán vai trò cho agent system',
    description: 'Gán vai trò cho agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiParam({
    name: 'roleId',
    description: 'ID của vai trò',
    type: String,
  })
  @ApiOkResponse({
    description: 'Vai trò đã được gán cho agent system thành công',
    type: ApiResponseDto,
  })
  async assignRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('roleId', ParseUUIDPipe) roleId: string,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentSystemService.assignRoleToAgentSystem(id, roleId);
    return ApiResponseDto.success(
      null,
      'Gán vai trò cho agent system thành công',
    );
  }

  /**
   * Xóa vai trò khỏi agent system
   * @param id ID của agent system
   * @param roleId ID của vai trò
   * @param employeeId ID của nhân viên thực hiện
   */
  @Delete(':id/roles/:roleId')
  @ApiOperation({
    summary: 'Xóa vai trò khỏi agent system',
    description: 'Xóa vai trò khỏi agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiParam({
    name: 'roleId',
    description: 'ID của vai trò',
    type: String,
  })
  @ApiOkResponse({
    description: 'Vai trò đã được xóa khỏi agent system thành công',
    type: ApiResponseDto,
  })
  async removeRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('roleId', ParseUUIDPipe) roleId: string,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentSystemService.removeRoleFromAgentSystem(id, roleId);
    return ApiResponseDto.success(
      null,
      'Xóa vai trò khỏi agent system thành công',
    );
  }
}
