import { Module } from '@nestjs/common';
import { S3Service } from '@/shared/services/s3.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Blog, BlogComment, BlogPurchase } from '../entities';
import { BlogRepository, BlogPurchaseRepository } from '../repositories';
import { BlogCommentUserController, BlogPurchaseUserController, BlogUserController } from './controllers';
import { BlogCommentUserService, BlogPurchaseUserService, BlogUserService } from './services';
import { User } from '@/modules/user/entities/user.entity';
import { Employee } from '@/modules/employee/entities/employee.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Blog, BlogComment, BlogPurchase, User, Employee])
  ],
  controllers: [
    BlogUserController,
    BlogCommentUserController,
    BlogPurchaseUserController,
  ],
  providers: [
    BlogUserService,
    BlogCommentUserService,
    BlogPurchaseUserService,
    BlogRepository,
    BlogPurchaseRepository,
    S3Service,
  ],
  exports: [
    BlogUserService,
    BlogCommentUserService,
    BlogPurchaseUserService,
  ],
})
export class BlogUserModule { }