import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Controller, Get, Param, ParseIntPipe, Post, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse as ApiResponseDoc, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { BlogPurchaseUserService } from '@modules/blog/user/services';
import { GetPurchaseListDto } from '@modules/blog/dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@/common/response';

@ApiTags(SWAGGER_API_TAGS.BLOG_PURCHASES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/blogs')
export class BlogPurchaseUserController {
  constructor(private readonly blogPurchaseUserService: BlogPurchaseUserService) { }

  /**
   * Mua bài viết
   */
  @Post(':blogId/purchase')
  @ApiOperation({
    summary: 'Mua bài viết',
    description: 'Mua bài viết bằng point',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 201,
    description: 'Bài viết đã được mua thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Blog purchased successfully' },
        result: { type: 'null' }
      },
    },
  })
  async purchaseBlog(
    @Param('blogId', ParseIntPipe) blogId: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.blogPurchaseUserService.purchaseBlog(userId, blogId);
    return ApiResponseDto.created(null, 'Blog purchased successfully');
  }

  /**
   * Kiểm tra người dùng đã mua bài viết chưa
   */
  @Get(':blogId/purchased')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái mua',
    description: 'Kiểm tra người dùng đã mua bài viết chưa',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Trạng thái mua bài viết.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            purchased: { type: 'boolean', example: true },
            purchased_at: { type: 'number', example: 1632474086123, nullable: true },
          },
        },
      },
    },
  })
  async checkPurchaseStatus(
    @Param('blogId', ParseIntPipe) blogId: number,
    @Request() request: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogPurchaseUserService.checkPurchaseStatus(request.user.id, blogId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách bài viết đã mua
   */
  @Get('purchased')
  @ApiOperation({
    summary: 'Lấy danh sách bài viết đã mua',
    description: 'Lấy danh sách tất cả bài viết đã mua của người dùng hiện tại',
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bài viết đã mua.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            content: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  user_id: { type: 'number', example: 10 },
                  blog_id: { type: 'number', example: 1 },
                  blog: {
                    type: 'object',
                    properties: {
                      id: { type: 'number', example: 1 },
                      title: { type: 'string', example: 'Tiêu đề bài viết' },
                      thumbnail_url: { type: 'string', example: 'URL thumbnail' },
                      tags: { type: 'array', items: { type: 'string' }, example: ['tag1', 'tag2'] },
                      created_at: { type: 'number', example: 1632474086123 },
                    },
                  },
                  point: { type: 'number', example: 100 },
                  purchased_at: { type: 'number', example: 1632474086123 },
                },
              },
            },
            totalItems: { type: 'number', example: 25 },
            itemCount: { type: 'number', example: 10 },
            itemsPerPage: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 3 },
            currentPage: { type: 'number', example: 1 },
          },
        },
      },
    },
  })
  async getPurchasedBlogs(
    @Request() request: any,
    @Query() query: GetPurchaseListDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogPurchaseUserService.getPurchasedBlogs(request.user.id, query);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy chi tiết giao dịch mua bài viết
   */
  @Get('purchases/:purchaseId')
  @ApiOperation({
    summary: 'Lấy chi tiết giao dịch mua bài viết'
  })
  @ApiParam({
    name: 'purchaseId',
    description: 'ID của giao dịch mua bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Chi tiết bài viết đã mua và thông tin giao dịch.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            userId: { type: 'number', example: 10 },
            blogId: { type: 'number', example: 1 },
            point: { type: 'number', example: 100 },
            purchasedAt: { type: 'number', example: 1632474086123 },
            platformFeePercent: { type: 'number', example: 5 },
            sellerReceivePrice: { type: 'number', example: 95 },
            blog: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                title: { type: 'string', example: 'Tiêu đề bài viết' },
                content: { type: 'string', example: 'URL file content trên CDN' },
                point: { type: 'number', example: 100 },
                viewCount: { type: 'number', example: 150 },
                thumbnailUrl: { type: 'string', example: 'URL thumbnail' },
                tags: { type: 'array', items: { type: 'string' }, example: ['tag1', 'tag2'] },
                createdAt: { type: 'number', example: 1632474086123 },
                updatedAt: { type: 'number', example: 1632474086123 },
                userId: { type: 'number', example: 1 },
                employeeId: { type: 'number', example: null },
                authorType: { type: 'string', example: 'USER' },
                author: {
                  type: 'object',
                  properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'Nguyễn Văn A' },
                    type: { type: 'string', example: 'USER' },
                    avatar: { type: 'string', example: 'https://cdn.example.com/avatars/user10.jpg' },
                  },
                },
                status: { type: 'string', example: 'APPROVED' },
                enable: { type: 'boolean', example: true },
                like: { type: 'number', example: 45 },
              },
            },
            buyer: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 10 },
                name: { type: 'string', example: 'Nguyễn Văn B' },
                avatar: { type: 'string', example: 'https://cdn.example.com/avatars/user20.jpg' },
              },
            },
          },
        },
      },
    },
  })
  async getPurchaseDetail(
    @Param('purchaseId', ParseIntPipe) purchaseId: number,
    @Request() request: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogPurchaseUserService.getPurchaseDetail(request.user.id, purchaseId);
    return ApiResponseDto.success(result);
  }
}
