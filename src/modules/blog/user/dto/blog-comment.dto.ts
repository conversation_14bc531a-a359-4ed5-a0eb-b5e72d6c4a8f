import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { AuthorTypeEnum } from '../../enums';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class BlogCommentAuthorDto {
  @Expose()
  @ApiProperty({
    description: 'ID của tác giả',
    example: 10,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên tác giả',
    example: 'Nguyễn Văn A',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Avatar của tác giả',
    example: 'https://cdn.example.com/avatars/user10.jpg',
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
  })
  type: AuthorTypeEnum;
}

export class BlogCommentDto {
  @Expose()
  @ApiProperty({
    description: 'ID của bình luận',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  blog_id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 10,
  })
  user_id: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1632474086123,
  })
  created_at: number;

  @Expose()
  @ApiProperty({
    description: 'Nội dung bình luận',
    example: 'Nội dung bình luận',
  })
  content: string;

  @Expose()
  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
  })
  author_type: AuthorTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'ID của nhân viên nếu là bình luận của hệ thống',
    example: null,
    nullable: true,
  })
  employee_id: number | null;

  @Expose()
  @ApiProperty({
    description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
    example: null,
    nullable: true,
  })
  parent_comment_id: number | null;

  @Expose()
  @Type(() => BlogCommentDto)
  @ApiProperty({
    description: 'Danh sách các bình luận phản hồi',
    type: [BlogCommentDto],
    nullable: true,
  })
  replies?: BlogCommentDto[];

  @Expose()
  @Type(() => BlogCommentAuthorDto)
  @ApiProperty({
    description: 'Thông tin tác giả',
    type: BlogCommentAuthorDto,
  })
  author?: BlogCommentAuthorDto;
}

export class CreateBlogCommentDto {
  @ApiProperty({
    description: 'Nội dung bình luận',
    example: 'Nội dung bình luận',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
    example: null,
    nullable: true,
    required: false,
  })
  @IsOptional()
  parent_comment_id?: number | null;
}

export class PaginatedBlogCommentDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách bình luận',
    type: [BlogCommentDto],
  })
  content: BlogCommentDto[];

  @Expose()
  @ApiProperty({
    description: 'Tổng số bình luận',
    example: 50,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bình luận trên trang hiện tại',
    example: 10,
  })
  itemCount: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bình luận trên mỗi trang',
    example: 10,
  })
  itemsPerPage: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số trang',
    example: 5,
  })
  totalPages: number;

  @Expose()
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  currentPage: number;
}
