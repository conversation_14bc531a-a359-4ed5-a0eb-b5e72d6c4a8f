import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateBlogCommentDto {
  @ApiProperty({
    description: 'Nội dung bình luận',
    example: 'Nội dung bình luận',
    maxLength: 1000,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(1000)
  content: string;

  @ApiProperty({
    description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  parent_comment_id?: number;
}
