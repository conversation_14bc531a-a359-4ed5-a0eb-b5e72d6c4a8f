import { ApiProperty } from '@nestjs/swagger';
import { BlogComment } from '@modules/blog/entities';
import { AuthorTypeEnum } from '../enums';

export class BlogCommentSchema {
  @ApiProperty({
    description: 'ID của bình luận',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
    nullable: true,
  })
  blogId: number;

  @ApiProperty({
    description: 'ID của người dùng bình luận',
    example: 1,
    nullable: true,
  })
  userId: number;

  @ApiProperty({
    description: 'Thời gian tạo bình luận (Unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Nội dung bình luận',
    example: 'Bài viết rất hay và bổ ích!',
    nullable: true,
  })
  content: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> tà<PERSON> khoản bình luận',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
    nullable: true,
  })
  authorType: AuthorTypeEnum;

  @ApiProperty({
    description: 'ID của nhân viên nếu là bình luận của hệ thống',
    example: 1,
    nullable: true,
  })
  employeeId: number;

  @ApiProperty({
    description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
    example: 1,
    nullable: true,
  })
  parentCommentId: number;

  @ApiProperty({
    description: 'Danh sách các bình luận phản hồi',
    type: [BlogCommentSchema],
    nullable: true,
  })
  replies: BlogCommentSchema[];

  constructor(partial: Partial<BlogComment>) {
    Object.assign(this, partial);
  }
}

export class BlogCommentListResponseSchema {
  @ApiProperty({
    description: 'Danh sách bình luận',
    type: [BlogCommentSchema],
  })
  items: BlogCommentSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số bình luận',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số bình luận trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số bình luận trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
