Dưới đây là file API Documentation đã được chỉnh sửa theo yêu cầu của bạn, trong đó:

- Endpoint `GET /user/blogs` được đổi thành `/blog/page`.
- Endpoint `GET /user/blogs/{id}` được đổi thành `/blog/detail/:id`.
- Các endpoint khác cũng được điều chỉnh để đảm bảo tính nhất quán và phù hợp với cấu trúc mới.
- Các endpoint của Admin API được giữ nguyên hoặc điều chỉnh nhẹ để phù hợp với ngữ cảnh.
- Cấu trúc response, params, và các phần khác được giữ nguyên trừ khi cần thay đổi để phù hợp với endpoint mới.

---

# API Documentation cho phần Blog

## Giới thiệu

API Blog cung cấp các endpoint để quản lý và tương tác với bài viết blog trong hệ thống. API này chia thành 2 loại endpoint:

- **User API**: Dành cho người dùng thông thường
- **Admin API**: Dành cho quản trị viên, bao gồm thêm chức năng kiểm duyệt blog

## Chuẩn Param Dùng Chung

### Param URL

| Param  | Kiểu dữ liệu | Mô tả                                          | Ví dụ                      |
| ------ | ------------ | ---------------------------------------------- | -------------------------- |
| id     | number       | ID của bài viết                                | /blog/detail/1             |
| blogId | number       | ID của bài viết (dùng trong các API liên quan) | /blog/comments/1           |

### Param Query

| Param       | Kiểu dữ liệu | Mô tả                              | Giá trị mặc định | Ví dụ                     |
| ----------- | ------------ | ---------------------------------- | ---------------- | ------------------------- |
| page        | number       | Trang hiện tại                     | 1                | ?page=2                   |
| limit       | number       | Số lượng bản ghi trên mỗi trang    | 10               | ?limit=20                 |
| status      | string       | Lọc theo trạng thái                | null             | ?status=APPROVED          |
| author_type | string       | Lọc theo loại tác giả              | null             | ?author_type=USER         |
| tags        | string[]     | Lọc theo tags                      | null             | ?tags=tag1,tag2           |
| search      | string       | Tìm kiếm theo tiêu đề              | null             | ?search=keyword           |
| start_date  | number       | Timestamp bắt đầu                  | null             | ?start_date=1632474086123 |
| end_date    | number       | Timestamp kết thúc                 | null             | ?end_date=1632574086123   |
| user_id     | number       | Lọc theo ID người dùng (chỉ admin) | null             | ?user_id=10               |
| sort        | string       | Sắp xếp theo trường                | created_at       | ?sort=title               |
| order       | string       | Thứ tự sắp xếp (ASC/DESC)          | DESC             | ?order=ASC                |

### Constants

#### Trạng thái bài viết

| Giá trị  | Mô tả                        |
| -------- | ---------------------------- |
| DRAFT    | Bài viết đang nháp           |
| PENDING  | Bài viết đang chờ kiểm duyệt |
| APPROVED | Bài viết đã được phê duyệt   |
| REJECTED | Bài viết bị từ chối          |

#### Loại tác giả

| Giá trị | Mô tả                   |
| ------- | ----------------------- |
| USER    | Người dùng thông thường |
| SYSTEM  | Nhân viên hệ thống      |

## Cấu trúc Response

### Cấu trúc Response Cơ bản

Tất cả các API endpoint đều trả về kết quả theo cấu trúc sau:

```json
{
  "code": number,
  "message": string,
  "result": any
}
```

Trong đó:

- **code**: Mã HTTP status
- **message**: Thông báo mô tả kết quả
- **result**: Dữ liệu trả về (có thể là object, array, hoặc null)

### Cấu trúc Response với Phân trang

Đối với các API trả về danh sách có phân trang, cấu trúc response sẽ như sau:

```json
{
  "code": number,
  "message": string,
  "result": {
    "content": any[],
    "totalItems": number,    // Tổng số bản ghi
    "itemCount": number,     // Số lượng bản ghi trên trang hiện tại
    "itemsPerPage": number,  // Số lượng bản ghi trên mỗi trang
    "totalPages": number,    // Tổng số trang
    "currentPage": number    // Trang hiện tại
  }
}
```

### Cấu trúc Response Lỗi

Khi xảy ra lỗi, API sẽ trả về response với cấu trúc:

```json
{
  "code": number,
  "message": string,
  "result": {
    "errors": [
      {
        "field": string,
        "message": string
      }
    ]
  }
}
```

### Mã Status và Thông báo lỗi

| Code | Description           | Thông báo                     |
| ---- | --------------------- | ----------------------------- |
| 200  | OK                    | Success                       |
| 201  | Created               | Resource created successfully |
| 400  | Bad Request           | Invalid input data            |
| 401  | Unauthorized          | Authentication required       |
| 403  | Forbidden             | Permission denied             |
| 404  | Not Found             | Resource not found            |
| 409  | Conflict              | Resource already exists       |
| 500  | Internal Server Error | Internal server error         |

### Cấu trúc Đối tượng Blog

```json
{
  "id": number,
  "title": string,
  "description": string,
  "content_final_url": string,
  "thumbnail_final_url": string,
  "point": number,
  "view_count": number,
  "tags": string[],
  "created_at": number,
  "updated_at": number,
  "author": {
    "id": number,
    "name": string,
    "type": "USER" | "SYSTEM",
    "avatar": string
  },
  "employee_moderator": number,
  "status": string,
  "enable": boolean,
  "like": number
}
```

### Cấu trúc Đối tượng Comment

```json
{
  "id": number,
  "blog_id": number,
  "user_id": number,
  "employee_id": number,
  "created_at": number,
  "content": string,
  "author_type": string,
  "parent_comment_id": number,
  "replies": Comment[]
}
```

### Cấu trúc Đối tượng Purchase

```json
{
  "id": number,
  "user_id": number,
  "blog_id": number,
  "point": number,
  "purchased_at": number,
  "platform_fee_percent": number,
  "seller_receive_price": number
}
```

## User API Endpoints

### 1. Lấy danh sách bài viết

**Endpoint**: GET /blog/page

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `status`: (string) Lọc theo trạng thái (APPROVED)
- `author_type`: (string) Loại tác giả (USER, SYSTEM)
- `tags`: (string[]) Lọc theo tags
- `search`: (string) Tìm kiếm theo tiêu đề

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "title": "Tiêu đề bài viết",
        "content": "URL file content trên CDN",
        "point": 100,
        "view_count": 150,
        "thumbnail_url": "URL thumbnail",
        "tags": ["tag1", "tag2"],
        "created_at": 1632474086123,
        "updated_at": 1632474086123,
        "author": {
          "id": 10,
          "name": "Nguyễn Văn A",
          "type": "USER",
          "avatar": "https://cdn.example.com/avatars/user10.jpg"
        },
        "status": "APPROVED",
        "enable": true,
        "like": 45
      }
    ],
    "totalItems": 100,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

### 2. Lấy chi tiết bài viết

**Endpoint**: GET /blog/detail/:id

**Params**:

- `id`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "title": "Tiêu đề bài viết",
    "content": "URL file content trên CDN",
    "point": 100,
    "view_count": 150,
    "thumbnail_url": "URL thumbnail",
    "tags": ["tag1", "tag2"],
    "updated_at": 1632474086123,
    "author": {
      "id": 10,
      "name": "Nguyễn Văn A",
      "type": "USER",
      "avatar": "https://cdn.example.com/avatars/user10.jpg"
    },
    "employee_moderator": null,
    "status": "APPROVED",
    "enable": true,
    "like": 45
  }
}
```

### 3. Lấy danh sách bài viết của user hiện tại

**Endpoint**: GET /blog/my-blogs

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `status`: (string) Lọc theo trạng thái (DRAFT, PENDING, APPROVED)
- `tags`: (string[]) Lọc theo tags
- `search`: (string) Tìm kiếm theo tiêu đề

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "title": "Tiêu đề bài viết",
        "content": "URL file content trên CDN",
        "point": 100,
        "view_count": 150,
        "thumbnail_url": "URL thumbnail",
        "tags": ["tag1", "tag2"],
        "created_at": 1632474086123,
        "updated_at": 1632474086123,
        "author": {
          "id": 10,
          "name": "Nguyễn Văn A",
          "type": "USER",
          "avatar": "https://cdn.example.com/avatars/user10.jpg"
        },
        "employee_moderator": null,
        "status": "APPROVED",
        "enable": true,
        "like": 45
      }
    ],
    "totalItems": 20,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 2,
    "currentPage": 1
  }
}
```

### 4. Tạo bài viết mới

**Endpoint**: POST /blog

**Request Body**:

```json
{
  "title": "Tiêu đề bài viết",
  "description": "Mô tả ngắn về bài viết",
  "content_media_type": "text/html",
  "thumbnail_media_type": "image/jpeg",
  "point": 100,
  "tags": ["tag1", "tag2"],
  "status": "DRAFT"
}
```

**Response**:

```json
{
  "code": 201,
  "message": "Blog created successfully",
  "result": {
    "content_upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...",
    "thumbnail_upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456..."
  }
}
```

Frontend sẽ sử dụng các URL tạm thời (`content_upload_url` và `thumbnail_upload_url`) để đẩy file lên CDN, sau đó khi hiển thị bài viết, hệ thống sẽ sử dụng các URL cuối cùng (`content_final_url` và `thumbnail_final_url`).

### 5. Cập nhật media cho bài viết

**Endpoint**: PUT /blog/:id/media

**Params**:

- `id`: (number) ID của bài viết

**Request Body**:

```json
{
  "media_type": "content | thumbnail",
  "media_content_type": "text/html | image/jpeg | image/png"
}
```

**Response**:

```json
{
  "code": 200,
  "message": "Media URLs generated successfully",
  "result": {
    "upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123..."
  }
}
```

### 6. Gửi bài viết để kiểm duyệt

**Endpoint**: PUT /blog/:id/submit

**Params**:

- `id`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Blog submitted for review"
}
```

### 7. Hủy gửi kiểm duyệt bài viết

**Endpoint**: PUT /blog/:id/cancel-submit

**Params**:

- `id`: (number) ID của bài viết

**Description**:
API này cho phép người dùng hủy bỏ trạng thái chờ kiểm duyệt (PENDING) và chuyển bài viết về trạng thái nháp (DRAFT). Chỉ áp dụng cho các bài viết đang ở trạng thái PENDING và chưa được kiểm duyệt.

**Response**:

```json
{
  "code": 200,
  "message": "Blog review cancelled"
}
```

### 8. Xóa bài viết

**Endpoint**: DELETE /blog/:id

**Params**:

- `id`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Blog deleted successfully",
  "result": null
}
```

### 9. Tạo bình luận mới

**Endpoint**: POST /blog/comments/:blogId

**Params**:

- `blogId`: (number) ID của bài viết

**Request Body**:

```json
{
  "content": "Nội dung bình luận",
  "parent_comment_id": null
}
```

**Response**:

```json
{
  "code": 201,
  "message": "Comment created successfully"
}
```

### 10. Xóa bình luận

**Endpoint**: DELETE /blog/comments/:id

**Params**:

- `id`: (number) ID của bình luận

**Response**:

```json
{
  "code": 200,
  "message": "Comment deleted successfully",
  "result": null
}
```

### 11. Lấy danh sách bình luận của bài viết

**Endpoint**: GET /blog/comments/:blogId

**Params**:

- `blogId`: (number) ID của bài viết
- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "blog_id": 1,
        "user_id": 10,
        "created_at": 1632474086123,
        "content": "Nội dung bình luận",
        "author_type": "USER",
        "employee_id": null,
        "parent_comment_id": null,
        "replies": [
          {
            "id": 2,
            "blog_id": 1,
            "user_id": 15,
            "created_at": 1632475086123,
            "content": "Nội dung trả lời",
            "author_type": "USER",
            "employee_id": null,
            "parent_comment_id": 1
          }
        ]
      }
    ],
    "totalItems": 50,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

### 12. Mua bài viết

**Endpoint**: POST /blog/purchase/:blogId

**Params**:

- `blogId`: (number) ID của bài viết

**Response**:

```json
{
  "code": 201,
  "message": "Blog purchased successfully"
}
```

### 13. Kiểm tra người dùng đã mua bài viết chưa

**Endpoint**: GET /blog/purchased/:blogId

**Params**:

- `blogId`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "purchased": true,
    "purchased_at": 1632474086123
  }
}
```

### 14. Lấy danh sách bài viết đã mua

**Endpoint**: GET /blog/purchased

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "user_id": 10,
        "blog_id": 1,
        "blog": {
          "id": 1,
          "title": "Tiêu đề bài viết",
          "thumbnail_url": "URL thumbnail",
          "tags": ["tag1", "tag2"],
          "created_at": 1632474086123
        },
        "point": 100,
        "purchased_at": 1632474086123
      }
    ],
    "totalItems": 25,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 3,
    "currentPage": 1
  }
}
```

## Admin API Endpoints

### 1. Lấy danh sách tất cả bài viết

**Endpoint**: GET /admin/blog/page

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `status`: (string) Lọc theo trạng thái (DRAFT, PENDING, APPROVED)
- `author_type`: (string) Loại tác giả (USER, SYSTEM)
- `tags`: (string[]) Lọc theo tags
- `search`: (string) Tìm kiếm theo tiêu đề
- `user_id`: (number) Lọc theo ID của người dùng
- `employee_id`: (number) Lọc theo ID của nhân viên

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "title": "Tiêu đề bài viết",
        "content": "URL file content trên CDN",
        "point": 100,
        "view_count": 150,
        "thumbnail_url": "URL thumbnail",
        "tags": ["tag1", "tag2"],
        "created_at": 1632474086123,
        "updated_at": 1632474086123,
        "author": {
          "id": 10,
          "name": "Nguyễn Văn A",
          "type": "USER",
          "avatar": "https://cdn.example.com/avatars/user10.jpg"
        },
        "employee_id": null,
        "employee_moderator": null,
        "status": "PENDING",
        "enable": true,
        "like": 45
      }
    ],
    "totalItems": 150,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 15,
    "currentPage": 1
  }
}
```

### 2. Lấy danh sách bài viết chờ kiểm duyệt

**Endpoint**: GET /admin/blog/pending

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "title": "Tiêu đề bài viết",
        "content": "URL file content trên CDN",
        "point": 100,
        "view_count": 150,
        "thumbnail_url": "URL thumbnail",
        "tags": ["tag1", "tag2"],
        "created_at": 1632474086123,
        "updated_at": 1632474086123,
        "author": {
          "id": 10,
          "name": "Nguyễn Văn A",
          "type": "USER",
          "avatar": "https://cdn.example.com/avatars/user10.jpg"
        },
        "employee_id": null,
        "employee_moderator": null,
        "status": "PENDING",
        "enable": true,
        "like": 45
      }
    ],
    "totalItems": 30,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 3,
    "currentPage": 1
  }
}
```

### 3. Lấy chi tiết bài viết

**Endpoint**: GET /admin/blog/detail/:id

**Params**:

- `id`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "title": "Tiêu đề bài viết",
    "content": "URL file content trên CDN",
    "point": 100,
    "view_count": 150,
    "thumbnail_url": "URL thumbnail",
    "tags": ["tag1", "tag2"],
    "created_at": 1632474086123,
    "updated_at": 1632474086123,
    "author": {
      "id": 10,
      "name": "Nguyễn Văn A",
      "type": "USER",
      "avatar": "https://cdn.example.com/avatars/user10.jpg"
    },
    "employee_id": null,
    "employee_moderator": null,
    "status": "PENDING",
    "enable": true,
    "like": 45
  }
}
```

### 4. Tạo bài viết mới (Bài viết hệ thống)

**Endpoint**: POST /admin/blog

**Request Body**:

```json
{
  "title": "Tiêu đề bài viết",
  "description": "Mô tả ngắn về bài viết",
  "content_media_type": "text/html",
  "thumbnail_media_type": "image/jpeg",
  "point": 100,
  "tags": ["tag1", "tag2"],
  "author_type": "SYSTEM",
  "status": "APPROVED"
}
```

**Response**:

```json
{
  "code": 201,
  "message": "Blog created successfully",
  "result": {
    "id": 1,
    "title": "Tiêu đề bài viết",
    "description": "Mô tả ngắn về bài viết",
    "point": 100,
    "tags": ["tag1", "tag2"],
    "created_at": 1632474086123,
    "updated_at": 1632474086123,
    "author": {
      "id": null,
      "name": null,
      "type": "SYSTEM",
      "avatar": null
    },
    "employee_id": 5,
    "employee_moderator": null,
    "status": "APPROVED",
    "enable": true,
    "like": 0,
    "view_count": 0,
    "content_upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...",
    "thumbnail_upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456..."
  }
}
```

### 5. Cập nhật media cho bài viết (Admin)

**Endpoint**: PUT /admin/blog/:id/media

**Params**:

- `id`: (number) ID của bài viết

**Request Body**:

```json
{
  "media_type": "content | thumbnail",
  "media_content_type": "text/html | image/jpeg | image/png"
}
```

**Response**:

```json
{
  "code": 200,
  "message": "Media URLs generated successfully",
  "result": {
    "upload_url": "https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...",
    "final_url": "https://cdn-storage.example.com/blogs/content/123456789/content.html"
  }
}
```

### 6. Xóa bài viết

**Endpoint**: DELETE /admin/blog/:id

**Params**:

- `id`: (number) ID của bài viết

**Response**:

```json
{
  "code": 200,
  "message": "Blog deleted successfully",
  "result": null
}
```

### 7. Phê duyệt/Từ chối bài viết

**Endpoint**: PUT /admin/blog/:id/moderate

**Params**:

- `id`: (number) ID của bài viết

**Request Body**:

```json
{
  "status": "APPROVED", // hoặc "REJECTED"
  "feedback": "Bài viết đã được phê duyệt" // Phản hồi cho người dùng
}
```

**Response**:

```json
{
  "code": 200,
  "message": "Blog moderated successfully",
  "result": {
    "id": 1,
    "status": "APPROVED",
    "employee_moderator": 5
  }
}
```

### 8. Lấy danh sách bình luận của bài viết

**Endpoint**: GET /admin/blog/comments/:blogId

**Params**:

- `blogId`: (number) ID của bài viết
- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "blog_id": 1,
        "user_id": 10,
        "created_at": 1632474086123,
        "content": "Nội dung bình luận",
        "author_type": "USER",
        "employee_id": null,
        "parent_comment_id": null,
        "replies": [
          {
            "id": 2,
            "blog_id": 1,
            "user_id": 15,
            "created_at": 1632475086123,
            "content": "Nội dung trả lời",
            "author_type": "USER",
            "employee_id": null,
            "parent_comment_id": 1
          }
        ]
      }
    ],
    "totalItems": 50,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

### 9. Tạo bình luận từ hệ thống

**Endpoint**: POST /admin/blog/comments/:blogId

**Params**:

- `blogId`: (number) ID của bài viết

**Request Body**:

```json
{
  "content": "Nội dung bình luận từ hệ thống",
  "parent_comment_id": null
}
```

**Response**:

```json
{
  "code": 201,
  "message": "Comment created successfully",
  "result": {
    "id": 1,
    "blog_id": 1,
    "user_id": null,
    "employee_id": 5,
    "created_at": 1632474086123,
    "content": "Nội dung bình luận từ hệ thống",
    "author_type": "SYSTEM",
    "parent_comment_id": null
  }
}
```

### 10. Xóa bình luận

**Endpoint**: DELETE /admin/blog/comments/:id

**Params**:

- `id`: (number) ID của bình luận

**Response**:

```json
{
  "code": 200,
  "message": "Comment deleted successfully",
  "result": null
}
```

### 11. Lấy thống kê giao dịch mua bài viết

**Endpoint**: GET /admin/blog/purchases/statistics

**Params**:

- `start_date`: (number) Timestamp bắt đầu
- `end_date`: (number) Timestamp kết thúc

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "total_purchases": 120,
    "total_points": 12500,
    "total_platform_fee": 625,
    "total_to_sellers": 11875
  }
}
```

### 12. Lấy danh sách giao dịch mua bài viết

**Endpoint**: GET /admin/blog/purchases

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `blog_id`: (number) Lọc theo ID của bài viết
- `user_id`: (number) Lọc theo ID của người dùng
- `start_date`: (number) Timestamp bắt đầu
- `end_date`: (number) Timestamp kết thúc

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "user_id": 10,
        "blog_id": 1,
        "blog": {
          "id": 1,
          "title": "Tiêu đề bài viết",
          "author_type": "USER"
        },
        "user": {
          "id": 10,
          "name": "Nguyễn Văn A"
        },
        "point": 100,
        "purchased_at": 1632474086123,
        "platform_fee_percent": 0.05,
        "seller_receive_price": 95
      }
    ],
    "totalItems": 120,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 12,
    "currentPage": 1
  }
}
```

---

### Các thay đổi chính

1. **User API**:
    - `GET /user/blogs` → `GET /blog/page`
    - `GET /user/blogs/{id}` → `GET /blog/detail/:id`
    - Các endpoint liên quan đến bình luận, mua bài viết, và quản lý bài viết của user được điều chỉnh để sử dụng `/blog` thay vì `/user/blogs`.
    - Định dạng `:id` và `:blogId` được sử dụng để nhất quán với yêu cầu.

2. **Admin API**:
    - Các endpoint được điều chỉnh để sử dụng `/admin/blog` thay vì `/admin/blogs` cho phù hợp với cấu trúc mới.
    - Endpoint lấy chi tiết bài viết được đổi thành `/admin/blog/detail/:id`.

3. **Cấu trúc URL**:
    - Các endpoint sử dụng định dạng `:param` (ví dụ: `:id`, `:blogId`) để biểu thị tham số động.
    - Đảm bảo tính nhất quán trong cách đặt tên endpoint (ví dụ: `/blog/comments/:blogId` thay vì `/user/blogs/{blogId}/comments`).

4. **Giữ nguyên**:
    - Cấu trúc response, params query, và các constants không thay đổi.
    - Các mô tả, ví dụ response, và logic của API được giữ nguyên để đảm bảo tính đầy đủ.

Nếu bạn cần thêm bất kỳ chỉnh sửa nào hoặc muốn bổ sung tính năng, hãy cho tôi biết!