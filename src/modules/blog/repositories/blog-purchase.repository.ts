import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BlogPurchase } from '../entities/blog-purchase.entity';

@Injectable()
export class BlogPurchaseRepository extends Repository<BlogPurchase> {
  constructor(private dataSource: DataSource) {
    super(BlogPurchase, dataSource.createEntityManager());
  }

  /**
   * <PERSON><PERSON>y thống kê doanh thu theo tháng
   * Sử dụng SQL thuần
   */
  async getRevenueByMonth(year: number): Promise<any[]> {
    const query = `
      SELECT 
        EXTRACT(MONTH FROM TO_TIMESTAMP(purchased_at / 1000)) as month,
        SUM(point) as total_revenue,
        SUM(point - seller_receive_price) as platform_revenue,
        COUNT(*) as purchase_count
      FROM blog_purchases
      WHERE EXTRACT(YEAR FROM TO_TIMESTAMP(purchased_at / 1000)) = $1
      GROUP BY month
      ORDER BY month
    `;
    
    return this.query(query, [year]);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách bài viết được mua nhiều nhất
   * Sử dụng SQL thuần
   */
  async getMostPurchasedBlogs(limit: number = 10): Promise<any[]> {
    const query = `
      SELECT 
        b.id,
        b.title,
        b.thumbnail_url,
        b.point,
        COUNT(p.id) as purchase_count,
        SUM(p.point) as total_revenue
      FROM blog_purchases p
      JOIN blogs b ON p.blog_id = b.id
      WHERE b.enable = true
      GROUP BY b.id, b.title, b.thumbnail_url, b.point
      ORDER BY purchase_count DESC
      LIMIT $1
    `;
    
    return this.query(query, [limit]);
  }

  /**
   * Kiểm tra người dùng đã mua bài viết chưa
   * Sử dụng SQL thuần
   */
  async hasUserPurchasedBlog(userId: number, blogId: number): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count
      FROM blog_purchases
      WHERE user_id = $1 AND blog_id = $2
    `;
    
    const result = await this.query(query, [userId, blogId]);
    return parseInt(result[0]?.count || '0') > 0;
  }

  /**
   * Lấy thống kê doanh thu của người dùng
   * Sử dụng SQL thuần
   */
  async getUserRevenueStatistics(userId: number): Promise<any> {
    const query = `
      SELECT 
        SUM(seller_receive_price) as total_revenue,
        COUNT(*) as total_sales,
        MAX(purchased_at) as last_sale_date
      FROM blog_purchases p
      JOIN blogs b ON p.blog_id = b.id
      WHERE b.user_id = $1
    `;
    
    const result = await this.query(query, [userId]);
    return result[0];
  }

  /**
   * Thực hiện giao dịch mua bài viết trong một transaction
   * Sử dụng SQL thuần và transaction
   */
  async purchaseBlogWithTransaction(
    userId: number, 
    blogId: number, 
    point: number,
    platformFeePercent: number
  ): Promise<BlogPurchase> {
    // Tính toán số point người bán nhận được
    const sellerReceivePrice = Math.floor(point * (1 - platformFeePercent / 100));
    
    // Bắt đầu transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // 1. Tạo giao dịch mua bài viết
      const insertResult = await queryRunner.query(`
        INSERT INTO blog_purchases(
          user_id, 
          blog_id, 
          point, 
          purchased_at, 
          platform_fee_percent, 
          seller_receive_price
        )
        VALUES($1, $2, $3, $4, $5, $6)
        RETURNING *
      `, [
        userId,
        blogId,
        point,
        Date.now(),
        platformFeePercent,
        sellerReceivePrice
      ]);
      
      // 2. Lấy thông tin người bán
      const blogResult = await queryRunner.query(`
        SELECT user_id FROM blogs WHERE id = $1
      `, [blogId]);
      
      const sellerId = blogResult[0]?.user_id;
      
      // 3. Trừ point của người mua
      await queryRunner.query(`
        UPDATE users
        SET points_balance = points_balance - $1
        WHERE id = $2
      `, [point, userId]);
      
      // 4. Cộng point cho người bán (nếu có)
      if (sellerId) {
        await queryRunner.query(`
          UPDATE users
          SET points_balance = points_balance + $1
          WHERE id = $2
        `, [sellerReceivePrice, sellerId]);
      }
      
      // Commit transaction
      await queryRunner.commitTransaction();
      
      return insertResult[0];
    } catch (error) {
      // Rollback transaction nếu có lỗi
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }
}
