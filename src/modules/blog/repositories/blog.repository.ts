import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Blog } from '@modules/blog/entities';
import { BlogStatusEnum } from '../enums';

@Injectable()
export class BlogRepository extends Repository<Blog> {
  constructor(private dataSource: DataSource) {
    super(Blog, dataSource.createEntityManager());
  }

  /**
   * L<PERSON>y danh sách bài viết phổ biến nhất (nhiều lượt xem nhất)
   * Sử dụng SQL thuần để tối ưu hiệu suất
   */
  async findMostPopularBlogs(limit: number = 10): Promise<Blog[]> {
    const query = `
      SELECT b.*
      FROM blogs b
      WHERE b.enable = true AND b.status = $1
      ORDER BY b.view_count DESC
      LIMIT $2
    `;
    
    return this.query(query, [BlogStatusEnum.APPROVED, limit]);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách bài viết mới nhất
   * Sử dụng SQL thuần
   */
  async findLatestBlogs(limit: number = 10): Promise<Blog[]> {
    const query = `
      SELECT b.*
      FROM blogs b
      WHERE b.enable = true AND b.status = $1
      ORDER BY b.created_at DESC
      LIMIT $2
    `;
    
    return this.query(query, [BlogStatusEnum.APPROVED, limit]);
  }

  /**
   * Tìm kiếm bài viết theo từ khóa trong tiêu đề và nội dung
   * Sử dụng SQL thuần với full-text search
   */
  async searchBlogs(keyword: string, page: number = 1, limit: number = 10): Promise<{ items: Blog[], total: number }> {
    const offset = (page - 1) * limit;
    
    const searchQuery = `
      SELECT b.*
      FROM blogs b
      WHERE b.enable = true 
        AND b.status = $1
        AND (
          b.title ILIKE $2
          OR b.content ILIKE $2
        )
      ORDER BY b.created_at DESC
      LIMIT $3 OFFSET $4
    `;
    
    const countQuery = `
      SELECT COUNT(*) as total
      FROM blogs b
      WHERE b.enable = true 
        AND b.status = $1
        AND (
          b.title ILIKE $2
          OR b.content ILIKE $2
        )
    `;
    
    const searchPattern = `%${keyword}%`;
    
    const items = await this.query(searchQuery, [
      BlogStatusEnum.APPROVED,
      searchPattern,
      limit,
      offset
    ]);
    
    const totalResult = await this.query(countQuery, [
      BlogStatusEnum.APPROVED,
      searchPattern
    ]);
    
    const total = parseInt(totalResult[0]?.total || '0');
    
    return { items, total };
  }

  /**
   * Lấy thống kê số lượng bài viết theo trạng thái và loại tác giả
   * Sử dụng SQL thuần với GROUP BY
   */
  async getBlogStatistics(): Promise<any> {
    const query = `
      SELECT 
        status,
        author_type,
        COUNT(*) as count,
        SUM(view_count) as total_views,
        SUM("like") as total_likes
      FROM blogs
      WHERE enable = true
      GROUP BY status, author_type
    `;
    
    return this.query(query);
  }

  /**
   * Lấy thống kê số lượng bài viết theo tháng
   * Sử dụng SQL thuần với date functions
   */
  async getBlogCountByMonth(year: number): Promise<any> {
    const query = `
      SELECT 
        EXTRACT(MONTH FROM TO_TIMESTAMP(created_at / 1000)) as month,
        COUNT(*) as count
      FROM blogs
      WHERE 
        enable = true AND
        EXTRACT(YEAR FROM TO_TIMESTAMP(created_at / 1000)) = $1
      GROUP BY month
      ORDER BY month
    `;
    
    return this.query(query, [year]);
  }

  /**
   * Cập nhật số lượt xem của bài viết
   * Sử dụng SQL thuần để tránh race condition
   */
  async incrementViewCount(id: number): Promise<void> {
    const query = `
      UPDATE blogs
      SET view_count = view_count + 1
      WHERE id = $1
    `;
    
    await this.query(query, [id]);
  }

  /**
   * Lấy danh sách bài viết liên quan dựa trên tags
   * Sử dụng SQL thuần với JSONB
   */
  async findRelatedBlogs(blogId: number, limit: number = 5): Promise<Blog[]> {
    const query = `
      WITH blog_tags AS (
        SELECT tags FROM blogs WHERE id = $1
      )
      SELECT b.*
      FROM blogs b, blog_tags bt
      WHERE b.id != $1
        AND b.enable = true
        AND b.status = $2
        AND b.tags @> bt.tags
      ORDER BY b.created_at DESC
      LIMIT $3
    `;
    
    return this.query(query, [blogId, BlogStatusEnum.APPROVED, limit]);
  }
}
