import { Test, TestingModule } from '@nestjs/testing';
import { BlogCommentAdminController } from '../controllers/blog-comment-admin.controller';
import { BlogCommentAdminService } from '../services/blog-comment-admin.service';
import { CreateBlogCommentDto } from '../../dto/create-blog-comment.dto';
import { GetBlogCommentsDto } from '../../dto/get-blog-comments.dto';
import { NotFoundException } from '@nestjs/common';
import { AuthorTypeEnum } from '../../enums';

// Mock the SWAGGER_API_TAGS import
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    ADMIN_BLOGS: 'admin-blogs'
  }
}));

// Mock the ErrorResponseSchema import
jest.mock('@/common/swagger', () => ({
  ErrorResponseSchema: {}
}));

// Mock ConfigModule
jest.mock('@nestjs/config', () => {
  const originalModule = jest.requireActual('@nestjs/config');
  return {
    ...originalModule,
    ConfigModule: {
      forRoot: jest.fn().mockReturnValue({
        module: class ConfigModule {},
        providers: [],
      }),
    },
  };
});

// Mock S3Service
jest.mock('@/shared/services/s3.service', () => ({
  S3Service: jest.fn().mockImplementation(() => ({
    createPresignedWithID: jest.fn(),
  })),
}));

// Mock JwtAuthGuard
jest.mock('@/modules/auth/guards', () => ({
  JwtAuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

// Mock CurrentUser decorator
jest.mock('@/common/decorators/current-user.decorator', () => ({
  CurrentUser: () => () => 1, // Always return user ID 1
}));

describe('BlogCommentAdminController', () => {
  let controller: BlogCommentAdminController;
  let service: BlogCommentAdminService;

  const mockBlogCommentAdminService = {
    createSystemComment: jest.fn(),
    deleteComment: jest.fn(),
    getComments: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BlogCommentAdminController],
      providers: [
        {
          provide: BlogCommentAdminService,
          useValue: mockBlogCommentAdminService,
        },
      ],
    })
    .overrideGuard(jest.requireMock('@/modules/auth/guards').JwtAuthGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<BlogCommentAdminController>(BlogCommentAdminController);
    service = module.get<BlogCommentAdminService>(BlogCommentAdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createSystemComment', () => {
    it('should create a comment and return success response', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test admin comment',
      };
      const req = { user: { id: employeeId } };
      mockBlogCommentAdminService.createSystemComment.mockResolvedValue({
        id: 1,
        blogId: 1,
        userId: null,
        employeeId: 5,
        content: 'Test admin comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.SYSTEM,
        parentCommentId: null,
      });

      // Act
      const result = await controller.createSystemComment(blogId, createCommentDto, req);

      // Assert
      expect(service.createSystemComment).toHaveBeenCalledWith(blogId, employeeId, createCommentDto);
      expect(result).toEqual({
        code: 201,
        message: 'Comment created successfully',
        result: expect.objectContaining({
          id: 1,
          content: 'Test admin comment',
        }),
      });
    });

    it('should create a reply comment and return success response', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test admin reply comment',
        parent_comment_id: 1,
      };
      const req = { user: { id: employeeId } };
      mockBlogCommentAdminService.createSystemComment.mockResolvedValue({
        id: 2,
        blogId: 1,
        userId: null,
        employeeId: 5,
        content: 'Test admin reply comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.SYSTEM,
        parentCommentId: 1,
      });

      // Act
      const result = await controller.createSystemComment(blogId, createCommentDto, req);

      // Assert
      expect(service.createSystemComment).toHaveBeenCalledWith(blogId, employeeId, createCommentDto);
      expect(result).toEqual({
        code: 201,
        message: 'Comment created successfully',
        result: expect.objectContaining({
          id: 2,
          content: 'Test admin reply comment',
          parentCommentId: 1,
        }),
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const employeeId = 5;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test admin comment',
      };
      const req = { user: { id: employeeId } };
      mockBlogCommentAdminService.createSystemComment.mockRejectedValue(new NotFoundException(`Blog with ID ${blogId} not found`));

      // Act & Assert
      await expect(controller.createSystemComment(blogId, createCommentDto, req)).rejects.toThrow(NotFoundException);
    });

    it('should handle parent comment not found error', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test admin reply comment',
        parent_comment_id: 999,
      };
      const req = { user: { id: employeeId } };
      mockBlogCommentAdminService.createSystemComment.mockRejectedValue(
        new NotFoundException(`Parent comment with ID ${createCommentDto.parent_comment_id} not found`)
      );

      // Act & Assert
      await expect(controller.createSystemComment(blogId, createCommentDto, req)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment and return success response', async () => {
      // Arrange
      const commentId = 1;
      mockBlogCommentAdminService.deleteComment.mockResolvedValue(null);

      // Act
      const result = await controller.deleteComment(commentId);

      // Assert
      expect(service.deleteComment).toHaveBeenCalledWith(commentId);
      expect(result).toEqual({
        code: 200,
        message: 'Comment deleted successfully',
        result: null,
      });
    });

    it('should handle comment not found error', async () => {
      // Arrange
      const commentId = 999;
      mockBlogCommentAdminService.deleteComment.mockRejectedValue(new NotFoundException(`Comment with ID ${commentId} not found`));

      // Act & Assert
      await expect(controller.deleteComment(commentId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getComments', () => {
    it('should return blog comments', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockComments = {
        content: [
          {
            id: 1,
            blog_id: 1,
            user_id: null,
            employee_id: 5,
            content: 'Test admin comment',
            created_at: 1625097600000,
            author_type: AuthorTypeEnum.SYSTEM,
            parent_comment_id: null,
            replies: [],
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogCommentAdminService.getComments.mockResolvedValue(mockComments);

      // Act
      const result = await controller.getComments(blogId, query);

      // Assert
      expect(service.getComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockComments,
      });
    });

    it('should return comments with replies', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockComments = {
        content: [
          {
            id: 1,
            blog_id: 1,
            user_id: null,
            employee_id: 5,
            content: 'Test admin comment',
            created_at: 1625097600000,
            author_type: AuthorTypeEnum.SYSTEM,
            parent_comment_id: null,
            replies: [
              {
                id: 2,
                blog_id: 1,
                user_id: null,
                employee_id: 5,
                content: 'Test admin reply comment',
                created_at: 1625097700000,
                author_type: AuthorTypeEnum.SYSTEM,
                parent_comment_id: 1,
              },
            ],
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogCommentAdminService.getComments.mockResolvedValue(mockComments);

      // Act
      const result = await controller.getComments(blogId, query);

      // Assert
      expect(service.getComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockComments,
      });
    });

    it('should handle empty comments list', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockEmptyComments = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogCommentAdminService.getComments.mockResolvedValue(mockEmptyComments);

      // Act
      const result = await controller.getComments(blogId, query);

      // Assert
      expect(service.getComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyComments,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      mockBlogCommentAdminService.getComments.mockRejectedValue(new NotFoundException(`Blog with ID ${blogId} not found`));

      // Act & Assert
      await expect(controller.getComments(blogId, query)).rejects.toThrow(NotFoundException);
    });
  });
});
