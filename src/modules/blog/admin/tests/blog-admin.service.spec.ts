import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogAdminService } from '../services/blog-admin.service';
import { Blog } from '../../entities/blog.entity';
import { User } from '@/modules/user/entities/user.entity';
import { GetAdminBlogsDto, CreateBlogDto, ModerateBlogDto, UpdateBlogMediaDto, MediaTypeEnum } from '../../dto';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { SqlHelper } from '@common/helpers/sql.helper';
import { S3Service } from '@/shared/services/s3.service';
import { NotFoundException } from '@nestjs/common';

// Mock data
const mockBlog = {
  id: 1,
  title: 'Test Blog',
  description: 'Test Description',
  content: 'https://cdn.example.com/blogs/content-123.html',
  point: 100,
  viewCount: 1000,
  thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
  tags: ['nestjs', 'typescript'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userId: 1,
  authorType: AuthorTypeEnum.SYSTEM,
  status: BlogStatusEnum.APPROVED,
  enable: true,
  like: 500,
  employeeModerator: null,
  employeeId: null,
  author: {
    id: 1,
    name: 'System Admin',
    type: AuthorTypeEnum.SYSTEM,
    avatar: 'https://cdn.example.com/avatars/system.jpg',
  },
};

describe('BlogAdminService', () => {
  let service: BlogAdminService;
  let blogRepository: Repository<Blog>;
  let userRepository: Repository<User>;
  let sqlHelper: SqlHelper;
  let s3Service: S3Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogAdminService,
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            getPaginatedData: jest.fn(),
            select: jest.fn(),
            insert: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            exists: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
            getDownloadUrl: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BlogAdminService>(BlogAdminService);
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    sqlHelper = module.get<SqlHelper>(SqlHelper);
    s3Service = module.get<S3Service>(S3Service);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated blogs', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockPaginatedResult = {
        items: [mockBlog],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(sqlHelper, 'getPaginatedData').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findAll(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
      expect(result.currentPage).toBe(1);
    });

    it('should handle filtering by status', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = {
        page: 1,
        limit: 10,
        status: BlogStatusEnum.APPROVED
      };
      const mockPaginatedResult = {
        items: [mockBlog],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(sqlHelper, 'getPaginatedData').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findAll(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.content[0].status).toBe(BlogStatusEnum.APPROVED);
    });

    it('should handle empty result', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockEmptyPaginatedResult = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(sqlHelper, 'getPaginatedData').mockResolvedValue(mockEmptyPaginatedResult);

      // Act
      const result = await service.findAll(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(0);
      expect(result.totalItems).toBe(0);
    });

    it('should throw NotFoundException when error occurs', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };

      jest.spyOn(sqlHelper, 'getPaginatedData').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.findAll(dto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findPendingBlogs', () => {
    it('should return pending blogs', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockPendingBlog = {
        ...mockBlog,
        status: BlogStatusEnum.PENDING
      };
      const mockPaginatedResult = {
        items: [mockPendingBlog],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(sqlHelper, 'getPaginatedData').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findPendingBlogs(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.content[0].status).toBe(BlogStatusEnum.PENDING);
    });
  });

  describe('findOne', () => {
    it('should return a blog by id', async () => {
      // Arrange
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockBlog]);

      // Act
      const result = await service.findOne(blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(result.id).toBe(blogId);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.findOne(blogId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    it('should create a new blog and return it', async () => {
      // Arrange
      const createBlogDto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.SYSTEM,
      };

      jest.spyOn(sqlHelper, 'insert').mockResolvedValue({ id: 1 });
      jest.spyOn(service, 'findOne').mockResolvedValue(mockBlog);

      // Act
      const result = await service.create(createBlogDto);

      // Assert
      expect(sqlHelper.insert).toHaveBeenCalled();
      expect(service.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockBlog);
    });

    it('should throw NotFoundException when blog creation fails', async () => {
      // Arrange
      const createBlogDto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.SYSTEM,
      };

      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createBlogDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateMedia', () => {
    it('should update blog media and return upload URLs', async () => {
      // Arrange
      const blogId = 1;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.mediaType = MediaTypeEnum.CONTENT;
      updateMediaDto.mediaContentType = 'text/html';

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue('upload-url');
      jest.spyOn(s3Service, 'getDownloadUrl').mockReturnValue('final-url');

      // Act
      const result = await service.updateMedia(blogId, updateMediaDto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(s3Service.createPresignedWithID).toHaveBeenCalled();
      expect(s3Service.getDownloadUrl).toHaveBeenCalled();
      expect(result).toEqual({
        upload_url: 'upload-url',
        final_url: 'final-url',
      });
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.mediaType = MediaTypeEnum.CONTENT;
      updateMediaDto.mediaContentType = 'text/html';

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.updateMedia(blogId, updateMediaDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('moderate', () => {
    it('should moderate a blog and return updated status', async () => {
      // Arrange
      const blogId = 1;
      const moderateBlogDto: ModerateBlogDto = {
        status: BlogStatusEnum.APPROVED,
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'update').mockResolvedValue({ affected: 1 } as any);

      // Act
      const result = await service.moderate(blogId, moderateBlogDto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.update).toHaveBeenCalled();
      expect(result).toEqual({
        id: blogId,
        status: BlogStatusEnum.APPROVED,
        employeeModerator: null,
      });
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const moderateBlogDto: ModerateBlogDto = {
        status: BlogStatusEnum.APPROVED,
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.moderate(blogId, moderateBlogDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('delete', () => {
    it('should delete a blog', async () => {
      // Arrange
      const blogId = 1;

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'delete').mockResolvedValue({ affected: 1 } as any);

      // Act
      await service.delete(blogId);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.delete).toHaveBeenCalled();
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.delete(blogId)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when deletion fails', async () => {
      // Arrange
      const blogId = 1;

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'delete').mockResolvedValue({ affected: 0 } as any);

      // Act & Assert
      await expect(service.delete(blogId)).rejects.toThrow(NotFoundException);
    });
  });
});
