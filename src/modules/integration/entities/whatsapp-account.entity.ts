import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Agent } from '@modules/agent/entities';

/**
 * Entity đại diện cho bảng whatsapp_accounts trong cơ sở dữ liệu
 * Lưu trữ thông tin về các tài khoản WhatsApp Business được kết nối
 */
@Entity('whatsapp_accounts')
export class WhatsAppAccount {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng sở hữu tài khoản
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID số điện thoại WhatsApp Business
   */
  @Column({ name: 'phone_number_id', length: 255 })
  phoneNumberId: string;

  /**
   * Số điện thoại WhatsApp Business
   */
  @Column({ name: 'phone_number', length: 20 })
  phoneNumber: string;

  /**
   * ID tài khoản doanh nghiệp WhatsApp
   */
  @Column({ name: 'business_account_id', length: 255 })
  businessAccountId: string;

  /**
   * Tên hiển thị của tài khoản
   */
  @Column({ name: 'display_name', length: 255 })
  displayName: string;

  /**
   * Access token để gọi API
   */
  @Column({ name: 'access_token', type: 'text' })
  accessToken: string;

  /**
   * Trạng thái hoạt động của tài khoản
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * ID agent được kết nối với tài khoản WhatsApp
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId?: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;

  /**
   * Quan hệ với bảng agents
   */
  @ManyToOne(() => Agent)
  @JoinColumn({ name: 'agent_id' })
  agent: Agent;
}
