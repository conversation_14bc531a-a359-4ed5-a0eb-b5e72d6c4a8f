import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng ai_provider_configs trong cơ sở dữ liệu
 * Lưu trữ cấu hình API riêng cho từng người dùng hoặc hệ thống
 */
@Entity('ai_provider_configs')
export class AiProviderConfig {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Người dùng sở hữu cấu hình (NULL nếu là system)
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  /**
   * ID nhà cung cấp tương ứng
   */
  @Column({ name: 'provider_id' })
  providerId: number;

  /**
   * Tên cấu hình do người dùng đặt
   */
  @Column({ name: 'name', type: 'text' })
  name: string;

  /**
   * Đ<PERSON>h dấu đây là cấu hình mặc định hay không
   */
  @Column({ name: 'is_default', type: 'boolean', default: false })
  isDefault: boolean;

  /**
   * API key (nên được mã hóa trước khi lưu)
   */
  @Column({ name: 'api_key', type: 'text' })
  apiKey: string;

  /**
   * Trường bổ sung (dùng cho OpenAI)
   */
  @Column({ name: 'organization_id', type: 'text', nullable: true })
  organizationId: string;

  /**
   * Trường bổ sung (dùng cho Google, etc)
   */
  @Column({ name: 'project_id', type: 'text', nullable: true })
  projectId: string;

  /**
   * Các cấu hình bổ sung đặc thù của provider
   */
  @Column({ name: 'additional_config', type: 'jsonb', default: '{}' })
  additionalConfig: Record<string, any>;

  /**
   * Thời điểm tạo (epoch time)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (epoch time)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
