import { Body, Controller, Delete, Get, HttpStatus, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UserKeyUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import {
  AiProviderResponseDto,
  CreateUserKeyDto,
  TestUserKeyDto,
  UpdateUserKeyDto,
  UserKeyQueryDto,
  UserKeyResponseDto
} from '../dto';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('integration/user-key')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserKeyUserController {
  constructor(private readonly userKeyUserService: UserKeyUserService) {}

  /**
   * Lấy danh sách API key của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách API key của người dùng' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang hiện tại (bắt đầu từ 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng bản ghi trên mỗi trang',
  })
  @ApiQuery({
    name: 'providerKey',
    required: false,
    type: String,
    description: 'Tìm kiếm theo tên nhà cung cấp',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách API key thành công',
  })
  async findUserKeys(
    @Query() queryDto: UserKeyQueryDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<PaginatedResult<UserKeyResponseDto>>> {
    const userKeys = await this.userKeyUserService.findUserKeys(user.id, queryDto);
    return ApiResponseDto.success(userKeys, 'Lấy danh sách API key thành công');
  }

  /**
   * Lấy thông tin chi tiết API key
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết API key' })
  @ApiParam({ name: 'id', description: 'ID của API key' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết API key thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy API key',
  })
  async findUserKeyById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<UserKeyResponseDto>> {
    const userKey = await this.userKeyUserService.findUserKeyById(user.id, id);
    return ApiResponseDto.success(userKey, 'Lấy thông tin chi tiết API key thành công');
  }

  /**
   * Tạo API key mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo API key mới' })
  @ApiBody({ type: CreateUserKeyDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo API key mới thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  async createUserKey(
    @Body() createUserKeyDto: CreateUserKeyDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<UserKeyResponseDto>> {
    const userKey = await this.userKeyUserService.createUserKey(user.id, createUserKeyDto);
    return ApiResponseDto.success(userKey, 'Tạo API key mới thành công');
  }

  /**
   * Cập nhật API key
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật API key' })
  @ApiParam({ name: 'id', description: 'ID của API key' })
  @ApiBody({ type: UpdateUserKeyDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật API key thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy API key',
  })
  async updateUserKey(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserKeyDto: UpdateUserKeyDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<UserKeyResponseDto>> {
    const userKey = await this.userKeyUserService.updateUserKey(user.id, id, updateUserKeyDto);
    return ApiResponseDto.success(userKey, 'Cập nhật API key thành công');
  }

  /**
   * Xóa API key
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa API key' })
  @ApiParam({ name: 'id', description: 'ID của API key' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa API key thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy API key',
  })
  async deleteUserKey(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<null>> {
    await this.userKeyUserService.deleteUserKey(user.id, id);
    return ApiResponseDto.success(null, 'Xóa API key thành công');
  }

  /**
   * Lấy danh sách nhà cung cấp AI
   */
  @Get('providers/list')
  @ApiOperation({ summary: 'Lấy danh sách nhà cung cấp AI' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách nhà cung cấp AI thành công',
  })
  async findAiProviders(): Promise<ApiResponseDto<AiProviderResponseDto[]>> {
    const providers = await this.userKeyUserService.findAiProviders();
    return ApiResponseDto.success(providers, 'Lấy danh sách nhà cung cấp AI thành công');
  }

  /**
   * Kiểm tra API key
   */
  @Post('test')
  @ApiOperation({ summary: 'Kiểm tra API key' })
  @ApiBody({ type: TestUserKeyDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kiểm tra API key thành công',
  })
  async testUserKey(
    @Body() testUserKeyDto: TestUserKeyDto
  ): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    const result = await this.userKeyUserService.testUserKey(testUserKeyDto);
    return ApiResponseDto.success(result, 'Kiểm tra API key thành công');
  }
}
