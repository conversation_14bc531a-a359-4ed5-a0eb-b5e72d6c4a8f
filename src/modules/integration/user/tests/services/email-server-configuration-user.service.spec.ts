import { Test, TestingModule } from '@nestjs/testing';
import { EmailServerConfigurationUserService } from '../../services/email-server-configuration-user.service';
import { EmailServerConfigurationRepository } from '../../../repositories';
import { EncryptionService } from '@shared/services/encryption.service';
import { EmailServerConfiguration } from '@modules/integration/entities';
import { CreateEmailServerDto, TestEmailServerDto, TestEmailServerWithConfigDto, UpdateEmailServerDto } from '../../dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

// Mock nodemailer
jest.mock('nodemailer');
const mockNodemailer = nodemailer as jest.Mocked<typeof nodemailer>;

describe('EmailServerConfigurationUserService', () => {
  let service: EmailServerConfigurationUserService;
  let repository: EmailServerConfigurationRepository;
  let encryptionService: EncryptionService;

  // Mock data
  const mockEmailServer: EmailServerConfiguration = {
    id: 1,
    userId: 1,
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: 'encrypted-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  const mockCreateDto: CreateEmailServerDto = {
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: 'test-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
  };

  const mockTestDto: TestEmailServerDto = {
    recipientEmail: '<EMAIL>',
    subject: 'Test Email',
  };

  const mockTestWithConfigDto: TestEmailServerWithConfigDto = {
    emailServerConfig: {
      serverName: 'Test SMTP Server',
      host: 'smtp.test.com',
      port: 587,
      username: '<EMAIL>',
      password: 'test-password',
      useSsl: true,
      additionalSettings: { auth: 'login' },
    },
    testInfo: {
      recipientEmail: '<EMAIL>',
      subject: 'Test Email with Config',
    },
  };

  const mockTransporter = {
    verify: jest.fn().mockResolvedValue(true),
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailServerConfigurationUserService,
        {
          provide: EmailServerConfigurationRepository,
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: EncryptionService,
          useValue: {
            encrypt: jest.fn().mockReturnValue('encrypted-password'),
            decrypt: jest.fn().mockReturnValue('decrypted-password'),
          },
        },
      ],
    }).compile();

    service = module.get<EmailServerConfigurationUserService>(EmailServerConfigurationUserService);
    repository = module.get<EmailServerConfigurationRepository>(EmailServerConfigurationRepository);
    encryptionService = module.get<EncryptionService>(EncryptionService);

    // Setup nodemailer mock
    mockNodemailer.createTransporter.mockReturnValue(mockTransporter as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('testConnectionWithConfig', () => {
    it('should test email server connection successfully with direct configuration', async () => {
      const result = await service.testConnectionWithConfig(mockTestWithConfigDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBe('Kết nối thành công! Email kiểm tra đã được gửi.');
      expect(mockNodemailer.createTransporter).toHaveBeenCalledWith({
        host: mockTestWithConfigDto.emailServerConfig.host,
        port: mockTestWithConfigDto.emailServerConfig.port,
        secure: mockTestWithConfigDto.emailServerConfig.useSsl,
        auth: {
          user: mockTestWithConfigDto.emailServerConfig.username,
          pass: mockTestWithConfigDto.emailServerConfig.password,
        },
        ...mockTestWithConfigDto.emailServerConfig.additionalSettings,
      });
      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: mockTestWithConfigDto.emailServerConfig.username,
        to: mockTestWithConfigDto.testInfo.recipientEmail,
        subject: mockTestWithConfigDto.testInfo.subject,
        text: expect.stringContaining('Đây là email kiểm tra kết nối từ máy chủ'),
        html: expect.stringContaining('<p>Đây là email kiểm tra kết nối từ máy chủ'),
      });
    });

    it('should use default subject when not provided', async () => {
      const testDtoWithoutSubject = {
        ...mockTestWithConfigDto,
        testInfo: {
          recipientEmail: '<EMAIL>',
        },
      };

      const result = await service.testConnectionWithConfig(testDtoWithoutSubject);

      expect(result.success).toBe(true);
      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: mockTestWithConfigDto.emailServerConfig.username,
        to: testDtoWithoutSubject.testInfo.recipientEmail,
        subject: 'Kiểm tra kết nối máy chủ email',
        text: expect.stringContaining('Đây là email kiểm tra kết nối từ máy chủ'),
        html: expect.stringContaining('<p>Đây là email kiểm tra kết nối từ máy chủ'),
      });
    });

    it('should handle connection verification failure', async () => {
      const verifyError = new Error('Connection failed');
      mockTransporter.verify.mockRejectedValueOnce(verifyError);

      const result = await service.testConnectionWithConfig(mockTestWithConfigDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.message).toBe('Kết nối thất bại!');
      expect(result.details).toBe('Connection failed');
      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(mockTransporter.sendMail).not.toHaveBeenCalled();
    });

    it('should handle email sending failure', async () => {
      const sendError = new Error('Failed to send email');
      mockTransporter.sendMail.mockRejectedValueOnce(sendError);

      const result = await service.testConnectionWithConfig(mockTestWithConfigDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.message).toBe('Kết nối thất bại!');
      expect(result.details).toBe('Failed to send email');
      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(mockTransporter.sendMail).toHaveBeenCalled();
    });

    it('should handle transporter creation failure', async () => {
      const transporterError = new Error('Invalid configuration');
      mockNodemailer.createTransporter.mockImplementationOnce(() => {
        throw transporterError;
      });

      const result = await service.testConnectionWithConfig(mockTestWithConfigDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.message).toBe('Kết nối thất bại!');
      expect(result.details).toBe('Invalid configuration');
    });

    it('should include additional settings in transporter configuration', async () => {
      const configWithAdditionalSettings = {
        ...mockTestWithConfigDto,
        emailServerConfig: {
          ...mockTestWithConfigDto.emailServerConfig,
          additionalSettings: {
            auth: 'login',
            tls: { rejectUnauthorized: false },
            connectionTimeout: 5000,
          },
        },
      };

      await service.testConnectionWithConfig(configWithAdditionalSettings);

      expect(mockNodemailer.createTransporter).toHaveBeenCalledWith({
        host: configWithAdditionalSettings.emailServerConfig.host,
        port: configWithAdditionalSettings.emailServerConfig.port,
        secure: configWithAdditionalSettings.emailServerConfig.useSsl,
        auth: {
          user: configWithAdditionalSettings.emailServerConfig.username,
          pass: configWithAdditionalSettings.emailServerConfig.password,
        },
        auth: 'login',
        tls: { rejectUnauthorized: false },
        connectionTimeout: 5000,
      });
    });

    it('should work without additional settings', async () => {
      const configWithoutAdditionalSettings = {
        ...mockTestWithConfigDto,
        emailServerConfig: {
          ...mockTestWithConfigDto.emailServerConfig,
          additionalSettings: undefined,
        },
      };

      const result = await service.testConnectionWithConfig(configWithoutAdditionalSettings);

      expect(result.success).toBe(true);
      expect(mockNodemailer.createTransporter).toHaveBeenCalledWith({
        host: configWithoutAdditionalSettings.emailServerConfig.host,
        port: configWithoutAdditionalSettings.emailServerConfig.port,
        secure: configWithoutAdditionalSettings.emailServerConfig.useSsl,
        auth: {
          user: configWithoutAdditionalSettings.emailServerConfig.username,
          pass: configWithoutAdditionalSettings.emailServerConfig.password,
        },
      });
    });
  });
});
