import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho việc tạo mới công ty
 */
export class CreateCompanyDto {
  @ApiProperty({
    description: 'Tên đầy đủ của công ty',
    example: 'Công ty TNHH Thương mại Dịch vụ ABC'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  fullName: string;

  @ApiProperty({
    description: 'Tên viết tắt của công ty',
    example: 'ABC'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  shortName: string;
}
