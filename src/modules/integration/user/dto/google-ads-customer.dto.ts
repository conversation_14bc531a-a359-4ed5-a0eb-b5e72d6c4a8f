import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho response thông tin customer Google Ads
 */
export class GoogleAdsCustomerResponseDto {
  @ApiProperty({
    description: 'Customer ID của tài khoản Google Ads',
    example: '**********',
  })
  @IsString()
  customerId: string;

  @ApiProperty({
    description: 'Tên tài khoản',
    example: 'My Google Ads Account',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    example: 'ENABLED',
  })
  @IsString()
  status: string;
}

/**
 * DTO cho request chọn customer Google Ads
 */
export class SelectGoogleAdsCustomerDto {
  @ApiProperty({
    description: 'Customer ID của tài khoản Google Ads',
    example: '**********',
  })
  @IsNotEmpty()
  @IsString()
  customerId: string;

  @ApiProperty({
    description: 'Tên tài khoản (tùy chọn)',
    example: 'T<PERSON><PERSON> khoản quảng cáo chính',
  })
  @IsNotEmpty()
  @IsString()
  name: string;
}
