import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho việc truy vấn danh sách tài khoản WhatsApp
 */
export class WhatsAppAccountQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  isActive?: boolean;

  @ApiProperty({
    description: 'Lọc theo tài khoản đã kết nối với agent',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  hasAgent?: boolean;
}
