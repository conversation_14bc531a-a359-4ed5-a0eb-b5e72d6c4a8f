import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, Length, Matches } from 'class-validator';

/**
 * DTO cho việc tạo mới tài khoản WhatsApp Business
 */
export class CreateWhatsAppAccountDto {
  @ApiProperty({
    description: 'ID số điện thoại WhatsApp Business',
    example: '***************',
  })
  @IsNotEmpty()
  @IsString()
  phoneNumberId: string;

  @ApiProperty({
    description: 'Số điện thoại WhatsApp Business (định dạng quốc tế)',
    example: '+***********',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Số điện thoại phải ở định dạng quốc tế (bắt đầu bằng dấu +)',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'ID tài khoản doanh nghiệp WhatsApp',
    example: '***************',
  })
  @IsNotEmpty()
  @IsString()
  businessAccountId: string;

  @ApiProperty({
    description: 'Tên hiển thị của tài khoản',
    example: 'RedAI Support',
  })
  @IsNotEmpty()
  @IsString()
  @Length(3, 255)
  displayName: string;

  @ApiProperty({
    description: 'Access token để gọi API WhatsApp',
    example: 'EAABZCqZAZCZCZCZC...',
  })
  @IsNotEmpty()
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'ID agent được kết nối với tài khoản WhatsApp (nếu có)',
    example: '550e8400-e29b-41d4-a716-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;
}
