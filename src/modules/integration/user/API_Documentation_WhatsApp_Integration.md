# API Documentation - WhatsApp Integration

## Tổng quan

Module WhatsApp Integration cung cấp các API cho phép người dùng quản lý tích hợp với WhatsApp Business API, bao gồm:
- Quản lý tài khoản WhatsApp Business
- Kết nối tài khoản WhatsApp với agent
- Quản lý mẫu tin nhắn WhatsApp
- Gửi tin nhắn WhatsApp

Tất cả các API trong module này đều yêu cầu xác thực JWT và được bảo vệ bởi `JwtUserGuard`.

## Cấu trúc phản hồi chung

Tất cả các API đều trả về cấu trúc phản hồi thống nhất theo định dạng `ApiResponseDto`:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    // Dữ liệu trả về
  }
}
```

## Danh sách API

### 1. <PERSON><PERSON><PERSON>n lý tài khoản WhatsApp

#### 1.1. <PERSON><PERSON><PERSON> danh sách tài khoản WhatsApp

```
GET /integration/whatsapp/accounts
```

**Tham số truy vấn:**
- `page`: Số trang (mặc định: 1)
- `limit`: Số lượng kết quả mỗi trang (mặc định: 10)
- `search`: Từ khóa tìm kiếm (tìm theo tên hiển thị hoặc số điện thoại)
- `isActive`: Lọc theo trạng thái hoạt động (true/false)
- `hasAgent`: Lọc theo kết nối với agent (true/false)

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Lấy danh sách tài khoản WhatsApp thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "phoneNumberId": "***************",
        "phoneNumber": "+***********",
        "businessAccountId": "***************",
        "displayName": "RedAI Support",
        "isActive": true,
        "agentId": "550e8400-e29b-41d4-a716-************",
        "createdAt": **********,
        "updatedAt": **********
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

#### 1.2. Lấy thông tin tài khoản WhatsApp theo ID

```
GET /integration/whatsapp/accounts/:id
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Lấy thông tin tài khoản WhatsApp thành công",
  "result": {
    "id": 1,
    "phoneNumberId": "***************",
    "phoneNumber": "+***********",
    "businessAccountId": "***************",
    "displayName": "RedAI Support",
    "isActive": true,
    "agentId": "550e8400-e29b-41d4-a716-************",
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

#### 1.3. Tạo mới tài khoản WhatsApp

```
POST /integration/whatsapp/accounts
```

**Tham số body:**
```json
{
  "phoneNumberId": "***************",
  "phoneNumber": "+***********",
  "businessAccountId": "***************",
  "displayName": "RedAI Support",
  "accessToken": "EAABZCqZAZCZCZC...",
  "agentId": "550e8400-e29b-41d4-a716-************" // Tùy chọn
}
```

**Phản hồi thành công:**
```json
{
  "code": 201,
  "message": "Tạo tài khoản WhatsApp thành công",
  "result": {
    "id": 1,
    "phoneNumberId": "***************",
    "phoneNumber": "+***********",
    "businessAccountId": "***************",
    "displayName": "RedAI Support",
    "isActive": true,
    "agentId": "550e8400-e29b-41d4-a716-************",
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

#### 1.4. Cập nhật tài khoản WhatsApp

```
PUT /integration/whatsapp/accounts/:id
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Tham số body:**
```json
{
  "displayName": "RedAI Customer Support",
  "accessToken": "EAABZCqZAZCZCZC...",
  "isActive": true
}
```

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Cập nhật tài khoản WhatsApp thành công",
  "result": {
    "id": 1,
    "phoneNumberId": "***************",
    "phoneNumber": "+***********",
    "businessAccountId": "***************",
    "displayName": "RedAI Customer Support",
    "isActive": true,
    "agentId": "550e8400-e29b-41d4-a716-************",
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

#### 1.5. Xóa tài khoản WhatsApp

```
DELETE /integration/whatsapp/accounts/:id
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Xóa tài khoản WhatsApp thành công",
  "result": null
}
```

### 2. Kết nối tài khoản WhatsApp với agent

#### 2.1. Kết nối tài khoản WhatsApp với agent

```
POST /integration/whatsapp/accounts/:id/connect-agent
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Tham số body:**
```json
{
  "agentId": "550e8400-e29b-41d4-a716-************"
}
```

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Kết nối tài khoản WhatsApp với agent thành công",
  "result": {
    "id": 1,
    "phoneNumberId": "***************",
    "phoneNumber": "+***********",
    "businessAccountId": "***************",
    "displayName": "RedAI Support",
    "isActive": true,
    "agentId": "550e8400-e29b-41d4-a716-************",
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

#### 2.2. Ngắt kết nối tài khoản WhatsApp với agent

```
DELETE /integration/whatsapp/accounts/:id/disconnect-agent
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Ngắt kết nối tài khoản WhatsApp với agent thành công",
  "result": {
    "id": 1,
    "phoneNumberId": "***************",
    "phoneNumber": "+***********",
    "businessAccountId": "***************",
    "displayName": "RedAI Support",
    "isActive": true,
    "agentId": null,
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

### 3. Gửi tin nhắn WhatsApp

#### 3.1. Gửi tin nhắn WhatsApp

```
POST /integration/whatsapp/accounts/:id/send
```

**Tham số đường dẫn:**
- `id`: ID của tài khoản WhatsApp

**Tham số body (tin nhắn văn bản):**
```json
{
  "phoneNumber": "+***********",
  "messageType": "text",
  "content": "Xin chào! Cảm ơn bạn đã liên hệ với RedAI."
}
```

**Tham số body (tin nhắn mẫu):**
```json
{
  "phoneNumber": "+***********",
  "messageType": "template",
  "template": {
    "name": "welcome_message",
    "language": {
      "code": "vi"
    },
    "components": [
      {
        "type": "body",
        "parameters": [
          {
            "type": "text",
            "text": "Nguyễn Văn A"
          }
        ]
      }
    ]
  }
}
```

**Tham số body (tin nhắn hình ảnh):**
```json
{
  "phoneNumber": "+***********",
  "messageType": "image",
  "media": {
    "link": "https://example.com/image.jpg",
    "caption": "Hình ảnh sản phẩm"
  }
}
```

**Phản hồi thành công:**
```json
{
  "code": 200,
  "message": "Gửi tin nhắn WhatsApp thành công",
  "result": {
    "id": 1,
    "whatsappAccountId": 1,
    "messageId": "wamid.abcdefghijklmnopqrstuvwxyz",
    "phoneNumber": "+***********",
    "contactName": null,
    "content": "Xin chào! Cảm ơn bạn đã liên hệ với RedAI.",
    "messageType": "text",
    "metadata": {},
    "direction": "outgoing",
    "status": "sent",
    "sentAt": **********,
    "updatedAt": **********
  }
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 11500 | Không tìm thấy tài khoản WhatsApp |
| 11501 | Lỗi khi lấy danh sách tài khoản WhatsApp |
| 11502 | Lỗi khi lấy thông tin tài khoản WhatsApp |
| 11503 | Lỗi khi tạo tài khoản WhatsApp |
| 11504 | Lỗi khi cập nhật tài khoản WhatsApp |
| 11505 | Lỗi khi xóa tài khoản WhatsApp |
| 11506 | Tài khoản WhatsApp đã tồn tại |
| 11507 | Không có quyền truy cập tài khoản WhatsApp này |
| 11508 | Lỗi khi kết nối tài khoản WhatsApp với agent |
| 11509 | Lỗi khi ngắt kết nối tài khoản WhatsApp với agent |
| 11514 | Lỗi khi gửi tin nhắn WhatsApp |
| 11515 | Tin nhắn WhatsApp không hợp lệ |
| 11900 | Không tìm thấy agent |
| 11901 | Không có quyền truy cập agent này |
