import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WhatsAppMessage } from '../entities';

/**
 * Repository quản lý tin nhắn WhatsApp
 */
@Injectable()
export class WhatsAppMessageRepository {
  constructor(
    @InjectRepository(WhatsAppMessage)
    private readonly repository: Repository<WhatsAppMessage>,
  ) {}

  /**
   * Tìm tin nhắn WhatsApp theo ID
   * @param id ID của tin nhắn
   * @returns Thông tin tin nhắn
   */
  async findById(id: number): Promise<WhatsAppMessage | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['whatsappAccount'],
    });
  }

  /**
   * Tìm tin nhắn WhatsApp theo ID tin nhắn trên WhatsApp
   * @param messageId ID tin nhắn trên WhatsApp
   * @returns Thông tin tin nhắn
   */
  async findByMessageId(messageId: string): Promise<WhatsAppMessage | null> {
    return this.repository.findOne({
      where: { messageId },
    });
  }

  /**
   * Tìm tin nhắn WhatsApp theo ID tài khoản và số điện thoại
   * @param whatsappAccountId ID của tài khoản WhatsApp
   * @param phoneNumber Số điện thoại người nhận/gửi
   * @param limit Số lượng tin nhắn tối đa
   * @returns Danh sách tin nhắn
   */
  async findByAccountAndPhone(
    whatsappAccountId: number,
    phoneNumber: string,
    limit: number = 50,
  ): Promise<WhatsAppMessage[]> {
    return this.repository.find({
      where: { whatsappAccountId, phoneNumber },
      order: { sentAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Tạo mới tin nhắn WhatsApp
   * @param data Dữ liệu tin nhắn
   * @returns Tin nhắn đã tạo
   */
  async create(data: Partial<WhatsAppMessage>): Promise<WhatsAppMessage> {
    const message = this.repository.create(data);
    return this.repository.save(message);
  }

  /**
   * Cập nhật tin nhắn WhatsApp
   * @param id ID của tin nhắn
   * @param data Dữ liệu cập nhật
   * @returns Tin nhắn đã cập nhật
   */
  async update(id: number, data: Partial<WhatsAppMessage>): Promise<WhatsAppMessage | null> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Math.floor(Date.now() / 1000),
    });
    return this.findById(id);
  }

  /**
   * Cập nhật trạng thái tin nhắn WhatsApp
   * @param messageId ID tin nhắn trên WhatsApp
   * @param status Trạng thái mới
   * @returns Tin nhắn đã cập nhật
   */
  async updateStatus(messageId: string, status: string): Promise<WhatsAppMessage | null> {
    const message = await this.findByMessageId(messageId);
    if (!message) return null;

    return this.update(message.id, { status });
  }

  /**
   * Lấy lịch sử tin nhắn theo tài khoản WhatsApp
   * @param whatsappAccountId ID của tài khoản WhatsApp
   * @param page Số trang
   * @param limit Số lượng tin nhắn mỗi trang
   * @returns Danh sách tin nhắn và tổng số
   */
  async getMessageHistory(
    whatsappAccountId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<[WhatsAppMessage[], number]> {
    return this.repository.findAndCount({
      where: { whatsappAccountId },
      order: { sentAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });
  }
}
