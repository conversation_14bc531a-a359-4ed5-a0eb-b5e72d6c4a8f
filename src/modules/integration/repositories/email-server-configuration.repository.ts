import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { EmailServerConfiguration } from '../entities/email-server-configuration.entity';

@Injectable()
export class EmailServerConfigurationRepository extends Repository<EmailServerConfiguration> {
  constructor(private dataSource: DataSource) {
    super(EmailServerConfiguration, dataSource.createEntityManager());
  }
}
