import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp
 */
export enum BankSortField {
  BANK_CODE = 'bankCode',
  BANK_NAME = 'bankName',
}

/**
 * Enum cho thứ tự sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters khi lấy danh sách ngân hàng
 */
export class BankQueryDto {
  /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Trang phải là số nguyên' })
  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số lượng item phải là số nguyên' })
  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Tìm kiếm theo tên ngân hàng
   * @example "Vietcombank"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên ngân hàng',
    example: 'Vietcombank',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên ngân hàng phải là chuỗi' })
  bankName?: string;

  /**
   * Tìm kiếm theo mã ngân hàng
   * @example "VCB"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo mã ngân hàng',
    example: 'VCB',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã ngân hàng phải là chuỗi' })
  bankCode?: string;

  /**
   * Sắp xếp theo trường
   * @example "bankName"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: BankSortField,
    example: BankSortField.BANK_NAME,
    default: BankSortField.BANK_NAME,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(BankSortField).join(', ')}`,
  })
  sortBy?: BankSortField = BankSortField.BANK_NAME;

  /**
   * Thứ tự sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortOrder,
    example: SortOrder.ASC,
    default: SortOrder.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`,
  })
  sortOrder?: SortOrder = SortOrder.ASC;
}
