import { Test, TestingModule } from '@nestjs/testing';
// Mock classes and interfaces
class MockAppException extends Error {
  constructor(public code: number, message: string) {
    super(message);
  }
}

// Mock DTOs
interface AvatarUploadDto {
  imageType: string;
  maxSize: number;
}

interface UpdateAvatarDto {
  avatarKey: string;
}

// Mock User entity
interface User {
  id: number;
  avatar?: string;
  email?: string;
  fullName?: string;
  updatedAt?: number;
}

// Mock repositories
class MockUserRepository {
  findOne = jest.fn();
  save = jest.fn();
}

// Mock S3Service
class MockS3Service {
  createPresignedWithID = jest.fn();
}

// Constants
const TIME_INTERVAL_FIVE_MINUTES = 300;

// Service implementation
class AvatarService {
  private readonly AVATAR_FOLDER = 'avatars';

  constructor(
    private userRepository: MockUserRepository,
    private s3Service: MockS3Service
  ) {}

  async createAvatarUploadUrl(userId: number, avatarUploadDto: AvatarUploadDto) {
    try {
      // Find user by ID
      const user = await this.userRepository.findOne({ where: { id: userId } });
      
      // If user not found, throw exception
      if (!user) {
        throw new MockAppException(404, 'User not found');
      }

      // Create key for avatar on S3
      const fileName = `avatar-${userId}`;
      const avatarKey = `${this.AVATAR_FOLDER}/user-${userId}/${fileName}-${Date.now()}.jpg`;

      // Create temporary URL for uploading avatar
      const uploadUrl = await this.s3Service.createPresignedWithID(
        avatarKey,
        TIME_INTERVAL_FIVE_MINUTES,
        avatarUploadDto.imageType,
        avatarUploadDto.maxSize,
      );

      return {
        uploadUrl,
        avatarKey,
        expiresIn: TIME_INTERVAL_FIVE_MINUTES,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateAvatar(userId: number, updateAvatarDto: UpdateAvatarDto): Promise<User> {
    try {
      // Find user by ID
      const user = await this.userRepository.findOne({ where: { id: userId } });
      
      // If user not found, throw exception
      if (!user) {
        throw new MockAppException(404, 'User not found');
      }

      // Update avatar
      user.avatar = updateAvatarDto.avatarKey;
      user.updatedAt = Date.now();

      // Save updated user to database
      return this.userRepository.save(user);
    } catch (error) {
      throw error;
    }
  }
}

describe('AvatarService', () => {
  let service: AvatarService;
  let userRepository: MockUserRepository;
  let s3Service: MockS3Service;

  beforeEach(async () => {
    userRepository = new MockUserRepository();
    s3Service = new MockS3Service();
    service = new AvatarService(userRepository, s3Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAvatarUploadUrl', () => {
    it('should create avatar upload URL successfully', async () => {
      // Arrange
      const userId = 1;
      const avatarUploadDto: AvatarUploadDto = {
        imageType: 'image/jpeg',
        maxSize: 2097152, // 2MB
      };
      
      const mockUser: User = {
        id: userId,
        email: '<EMAIL>',
        fullName: 'Test User',
      };

      const mockPresignedUrl = 'https://example.com/presigned-url';
      
      userRepository.findOne.mockResolvedValue(mockUser);
      s3Service.createPresignedWithID.mockResolvedValue(mockPresignedUrl);

      // Act
      const result = await service.createAvatarUploadUrl(userId, avatarUploadDto);

      // Assert
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(s3Service.createPresignedWithID).toHaveBeenCalledWith(
        expect.stringContaining('avatars'),
        TIME_INTERVAL_FIVE_MINUTES,
        avatarUploadDto.imageType,
        avatarUploadDto.maxSize,
      );
      expect(result).toHaveProperty('uploadUrl', mockPresignedUrl);
      expect(result).toHaveProperty('avatarKey');
      expect(result).toHaveProperty('expiresIn', TIME_INTERVAL_FIVE_MINUTES);
    });

    it('should throw exception when user not found', async () => {
      // Arrange
      const userId = 999;
      const avatarUploadDto: AvatarUploadDto = {
        imageType: 'image/jpeg',
        maxSize: 2097152,
      };

      userRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.createAvatarUploadUrl(userId, avatarUploadDto)).rejects.toThrow('User not found');
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(s3Service.createPresignedWithID).not.toHaveBeenCalled();
    });
  });

  describe('updateAvatar', () => {
    it('should update avatar successfully', async () => {
      // Arrange
      const userId = 1;
      const updateAvatarDto: UpdateAvatarDto = {
        avatarKey: 'avatars/user-1/avatar-1234567890.jpg',
      };
      
      const mockUser: User = {
        id: userId,
        email: '<EMAIL>',
        fullName: 'Test User',
        avatar: 'old-avatar.jpg',
        updatedAt: Date.now() - 1000,
      };

      const expectedUpdatedUser: User = {
        ...mockUser,
        avatar: updateAvatarDto.avatarKey,
        updatedAt: expect.any(Number),
      };

      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(expectedUpdatedUser);

      // Act
      const result = await service.updateAvatar(userId, updateAvatarDto);

      // Assert
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(userRepository.save).toHaveBeenCalled();
      expect(result).toEqual(expectedUpdatedUser);
      expect(result.avatar).toBe(updateAvatarDto.avatarKey);
    });

    it('should throw exception when user not found', async () => {
      // Arrange
      const userId = 999;
      const updateAvatarDto: UpdateAvatarDto = {
        avatarKey: 'avatars/user-999/avatar-1234567890.jpg',
      };

      userRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateAvatar(userId, updateAvatarDto)).rejects.toThrow('User not found');
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(userRepository.save).not.toHaveBeenCalled();
    });
  });
});
