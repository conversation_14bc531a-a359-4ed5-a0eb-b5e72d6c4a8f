import { Test, TestingModule } from '@nestjs/testing';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Bank } from '@modules/user/entities/bank.entity';
import { Repository } from 'typeorm';

describe('BankRepository', () => {
  let repository: BankRepository;
  let typeOrmRepository: Repository<Bank>;

  // Mock data
  const mockBanks = [
    {
      bankCode: 'VCB',
      bankName: 'Vietcombank',
      logoPath: '/images/banks/vcb-logo.png',
      fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
      iconPath: '/images/banks/vcb-icon.png',
      bin: '970436',
    },
    {
      bankCode: 'TCB',
      bankName: 'Techcombank',
      logoPath: '/images/banks/tcb-logo.png',
      fullName: '<PERSON>ân hàng TMC<PERSON> thương Việt Nam',
      iconPath: '/images/banks/tcb-icon.png',
      bin: '970407',
    },
  ];

  // Mock TypeORM repository
  const mockTypeOrmRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    count: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BankRepository,
        {
          provide: getRepositoryToken(Bank),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<BankRepository>(BankRepository);
    typeOrmRepository = module.get<Repository<Bank>>(getRepositoryToken(Bank));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all banks', async () => {
      // Arrange
      mockTypeOrmRepository.find.mockResolvedValue(mockBanks);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(typeOrmRepository.find).toHaveBeenCalled();
      expect(result).toEqual(mockBanks);
    });
  });

  describe('find', () => {
    it('should find banks with options', async () => {
      // Arrange
      const options = {
        where: { bankName: 'Vietcombank' },
        order: { bankName: 'ASC' as const },
        skip: 0,
        take: 10,
      };
      mockTypeOrmRepository.find.mockResolvedValue([mockBanks[0]]);

      // Act
      const result = await repository.find(options);

      // Assert
      expect(typeOrmRepository.find).toHaveBeenCalledWith(options);
      expect(result).toEqual([mockBanks[0]]);
    });
  });

  describe('count', () => {
    it('should count banks with options', async () => {
      // Arrange
      const options = {
        where: { bankName: 'Vietcombank' },
      };
      mockTypeOrmRepository.count.mockResolvedValue(1);

      // Act
      const result = await repository.count(options);

      // Assert
      expect(typeOrmRepository.count).toHaveBeenCalledWith(options);
      expect(result).toEqual(1);
    });
  });

  describe('findByCode', () => {
    it('should find a bank by code', async () => {
      // Arrange
      const bankCode = 'VCB';
      mockTypeOrmRepository.findOne.mockResolvedValue(mockBanks[0]);

      // Act
      const result = await repository.findByCode(bankCode);

      // Assert
      expect(typeOrmRepository.findOne).toHaveBeenCalledWith({
        where: { bankCode },
      });
      expect(result).toEqual(mockBanks[0]);
    });

    it('should return null if bank does not exist', async () => {
      // Arrange
      const bankCode = 'INVALID';
      mockTypeOrmRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await repository.findByCode(bankCode);

      // Assert
      expect(typeOrmRepository.findOne).toHaveBeenCalledWith({
        where: { bankCode },
      });
      expect(result).toBeNull();
    });
  });
});
