import { Test, TestingModule } from '@nestjs/testing';
import { BankController } from '../controller/bank.controller';
import { BankService } from '../service/bank.service';
import { BankQueryDto, BankResponseDto } from '../dto/bank';

describe('BankController', () => {
  let controller: BankController;
  let service: BankService;

  // Mock service
  const mockBankService = {
    findAll: jest.fn(),
    findAllBanks: jest.fn(),
    findByCode: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BankController],
      providers: [
        {
          provide: BankService,
          useValue: mockBankService,
        },
      ],
    }).compile();

    controller = module.get<BankController>(BankController);
    service = module.get<BankService>(BankService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated banks', async () => {
      // Arrange
      const query: BankQueryDto = { page: 1, limit: 10 };
      const expectedResult = {
        data: [
          {
            bankCode: 'VCB',
            bankName: 'Vietcombank',
            logoPath: '/images/banks/vcb-logo.png',
            fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
            iconPath: '/images/banks/vcb-icon.png',
            bin: '970436',
          },
        ],
        meta: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
          hasPreviousPage: false,
          hasNextPage: false,
        },
      };

      mockBankService.findAll.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findAllBanks', () => {
    it('should return all banks', async () => {
      // Arrange
      const expectedResult: BankResponseDto[] = [
        {
          bankCode: 'VCB',
          bankName: 'Vietcombank',
          logoPath: '/images/banks/vcb-logo.png',
          fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
          iconPath: '/images/banks/vcb-icon.png',
          bin: '970436',
        },
        {
          bankCode: 'TCB',
          bankName: 'Techcombank',
          logoPath: '/images/banks/tcb-logo.png',
          fullName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
          iconPath: '/images/banks/tcb-icon.png',
          bin: '970407',
        },
      ];

      mockBankService.findAllBanks.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findAllBanks();

      // Assert
      expect(service.findAllBanks).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCode', () => {
    it('should return a bank by code', async () => {
      // Arrange
      const bankCode = 'VCB';
      const expectedResult: BankResponseDto = {
        bankCode: 'VCB',
        bankName: 'Vietcombank',
        logoPath: '/images/banks/vcb-logo.png',
        fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
        iconPath: '/images/banks/vcb-icon.png',
        bin: '970436',
      };

      mockBankService.findByCode.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findByCode(bankCode);

      // Assert
      expect(service.findByCode).toHaveBeenCalledWith(bankCode);
      expect(result).toEqual(expectedResult);
    });
  });
});
