import { Body, Controller, Get, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserAdminService } from '../services';
import { BlockUserDto, UnblockUserDto, UserDetailDto, UserListItemDto, UserQueryDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { UserStatusLog } from '@modules/user/entities';

@ApiTags(SWAGGER_API_TAGS.ADMIN_USER)
@Controller('admin/users')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class UserAdminController {
  constructor(private readonly userAdminService: UserAdminService) {}

  /**
   * Lấy danh sách người dùng với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách người dùng với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách người dùng với phân trang',
    type: ApiResponseDto
  })
  async findAll(@Query() queryDto: UserQueryDto): Promise<ApiResponseDto<PaginatedResult<UserListItemDto>>> {
    const users = await this.userAdminService.findAll(queryDto);
    return ApiResponseDto.success(users, 'Lấy danh sách người dùng thành công');
  }

  /**
   * Lấy thông tin chi tiết người dùng
   * @param id ID của người dùng
   * @returns Thông tin chi tiết người dùng
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết người dùng' })
  @ApiParam({ name: 'id', description: 'ID của người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết người dùng',
    type: ApiResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
    type: ApiResponseDto
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<UserDetailDto>> {
    const user = await this.userAdminService.findOne(id);
    return ApiResponseDto.success(user, 'Lấy thông tin người dùng thành công');
  }

  /**
   * Khóa tài khoản người dùng
   * @param id ID của người dùng
   * @param blockUserDto Thông tin khóa tài khoản
   * @param employee Thông tin admin thực hiện hành động
   * @returns Thông tin người dùng đã khóa
   */
  @Post(':id/block')
  @ApiOperation({ summary: 'Khóa tài khoản người dùng' })
  @ApiParam({ name: 'id', description: 'ID của người dùng', type: 'number' })
  @ApiBody({ type: BlockUserDto })
  @ApiResponse({
    status: 200,
    description: 'Khóa tài khoản người dùng thành công',
    type: ApiResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
    type: ApiResponseDto
  })
  async blockUser(
    @Param('id', ParseIntPipe) id: number,
    @Body() blockUserDto: BlockUserDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<UserDetailDto>> {
    const user = await this.userAdminService.blockUser(id, blockUserDto, employeeId);
    return ApiResponseDto.success(user, 'Khóa tài khoản người dùng thành công');
  }

  /**
   * Mở khóa tài khoản người dùng
   * @param id ID của người dùng
   * @param unblockUserDto Thông tin mở khóa tài khoản
   * @param employee Thông tin admin thực hiện hành động
   * @returns Thông tin người dùng đã mở khóa
   */
  @Post(':id/unblock')
  @ApiOperation({ summary: 'Mở khóa tài khoản người dùng' })
  @ApiParam({ name: 'id', description: 'ID của người dùng', type: 'number' })
  @ApiBody({ type: UnblockUserDto })
  @ApiResponse({
    status: 200,
    description: 'Mở khóa tài khoản người dùng thành công',
    type: ApiResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
    type: ApiResponseDto
  })
  async unblockUser(
    @Param('id', ParseIntPipe) id: number,
    @Body() unblockUserDto: UnblockUserDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<UserDetailDto>> {
    const user = await this.userAdminService.unblockUser(id, unblockUserDto, employeeId);
    return ApiResponseDto.success(user, 'Mở khóa tài khoản người dùng thành công');
  }

  /**
   * Lấy lịch sử thay đổi trạng thái của người dùng
   * @param id ID của người dùng
   * @returns Danh sách log thay đổi trạng thái
   */
  @Get(':id/status-logs')
  @ApiOperation({ summary: 'Lấy lịch sử thay đổi trạng thái của người dùng' })
  @ApiParam({ name: 'id', description: 'ID của người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử thay đổi trạng thái thành công',
    type: ApiResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
    type: ApiResponseDto
  })
  async getUserStatusLogs(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<UserStatusLog[]>> {
    const logs = await this.userAdminService.getUserStatusLogs(id);
    return ApiResponseDto.success(logs, 'Lấy lịch sử thay đổi trạng thái thành công');
  }
}
