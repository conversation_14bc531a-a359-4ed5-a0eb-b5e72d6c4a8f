import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminStepService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến các bước trong task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@Controller('admin/steps')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminStepController {
  /**
   * Constructor
   * @param adminStepService Service xử lý logic liên quan đến các bước trong task của admin
   */
  constructor(private readonly adminStepService: AdminStepService) {}
}
