import { TaskStatus } from '../enums/task-status.enum';

/**
 * Interface cho dữ liệu task
 */
export interface ITask {
  /**
   * ID của task
   */
  taskId: string;
  
  /**
   * ID của người dùng sở hữu task
   */
  userId?: number;
  
  /**
   * ID của agent l<PERSON><PERSON><PERSON> kết với task
   */
  agentId: string;
  
  /**
   * Tên của task
   */
  taskName: string;
  
  /**
   * Mô tả của task
   */
  taskDescription?: string;
  
  /**
   * Trạng thái của task
   */
  status: TaskStatus;
  
  /**
   * Trạng thái hoạt động của task
   */
  active: boolean;
  
  /**
   * Thời gian tạo task
   */
  createdAt: number;
  
  /**
   * Thời gian cập nhật task
   */
  updatedAt: number;
  
  /**
   * Thời gian xóa task (soft delete)
   */
  deletedAt?: number;
}
