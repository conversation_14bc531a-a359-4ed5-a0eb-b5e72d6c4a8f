# Tài liệu luồng Task cho Admin

## <PERSON><PERSON><PERSON> lụ<PERSON>
1. [<PERSON><PERSON><PERSON><PERSON> thiệu](#giới-thiệu)
2. [Qu<PERSON>n lý Task](#quản-lý-task)
   - [Xem danh sách Task](#xem-danh-sách-task)
   - [Tạo Task mới](#tạo-task-mới)
   - [Xem chi tiết Task](#xem-chi-tiết-task)
   - [Cập nhật Task](#cập-nhật-task)
   - [Xóa Task](#xóa-task)
3. [Quản lý Step](#quản-lý-step)
   - [Xem danh sách Step](#xem-danh-sách-step)
   - [Thêm Step mới](#thêm-step-mới)
   - [Cập nhật Step](#cập-nhật-step)
   - [Sắp xếp thứ tự Step](#sắp-xếp-thứ-tự-step)
   - [Xóa Step](#xóa-step)
4. [Quản lý Connection](#quản-lý-connection)
   - [Xem danh sách Connection](#xem-danh-sách-connection)
   - [Tạo Connection mới](#tạo-connection-mới)
   - [Cập nhật Connection](#cập-nhật-connection)
   - [Xóa Connection](#xóa-connection)
5. [Quản lý Execution](#quản-lý-execution)
   - [Xem lịch sử Execution](#xem-lịch-sử-execution)
   - [Xem chi tiết Execution](#xem-chi-tiết-execution)


## Giới thiệu

Tài liệu này mô tả chi tiết các luồng xử lý liên quan đến quản lý Task trong hệ thống dành cho Admin. Admin có quyền quản lý toàn bộ Task, Step, Connection và Execution trong hệ thống.

## Quản lý Task

### Xem danh sách Task

**Endpoint:** GET /admin/tasks

**Mô tả:** Lấy danh sách tất cả các Task trong hệ thống với khả năng lọc và phân trang.

**Luồng xử lý:**
1. Admin truy cập trang quản lý Task
2. Hệ thống gửi request GET đến endpoint /admin/tasks với các tham số lọc và phân trang
3. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn dữ liệu từ bảng admin_tasks với các điều kiện lọc
   - Áp dụng phân trang và sắp xếp
4. Backend trả về danh sách Task kèm thông tin phân trang
5. Frontend hiển thị danh sách Task cho Admin

**Tham số:**
- page: Số trang (mặc định: 1)
- limit: Số lượng Task trên mỗi trang (mặc định: 10)
- sortBy: Trường sắp xếp (mặc định: createdAt)
- sortOrder: Thứ tự sắp xếp (ASC/DESC, mặc định: DESC)
- agentId: Lọc theo Agent ID
- search: Tìm kiếm theo tên Task

### Tạo Task mới

**Endpoint:** POST /admin/tasks

**Mô tả:** Tạo một Task mới trong hệ thống.

**Luồng xử lý:**
1. Admin truy cập trang tạo Task mới
2. Admin nhập thông tin Task:
   - Tên Task (bắt buộc)
   - Mô tả Task (tùy chọn)
   - Chọn Agent (bắt buộc)
3. Admin nhấn nút "Tạo Task"
4. Frontend gửi request POST đến endpoint /admin/tasks với dữ liệu Task
5. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Tạo bản ghi mới trong bảng admin_tasks
   - Lưu thông tin người tạo (createdBy)
6. Backend trả về thông tin Task vừa tạo
7. Frontend chuyển hướng đến trang chi tiết Task

**Dữ liệu đầu vào:**
```json
{
  "taskName": "Tên Task",
  "taskDescription": "Mô tả chi tiết về Task",
  "agentId": "uuid-của-agent"
}
```

### Xem chi tiết Task

**Endpoint:** GET /admin/tasks/{taskId}

**Mô tả:** Xem thông tin chi tiết của một Task cụ thể.

**Luồng xử lý:**
1. Admin nhấp vào một Task trong danh sách
2. Frontend gửi request GET đến endpoint /admin/tasks/{taskId}
3. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn thông tin Task từ bảng admin_tasks
   - Truy vấn danh sách Step của Task từ bảng admin_steps
   - Truy vấn danh sách Connection của Task từ bảng admin_step_connections
4. Backend trả về thông tin chi tiết Task
5. Frontend hiển thị thông tin chi tiết Task, danh sách Step và Connection

### Cập nhật Task

**Endpoint:** PUT /admin/tasks/{taskId}

**Mô tả:** Cập nhật thông tin của một Task.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin nhấn nút "Chỉnh sửa"
3. Admin cập nhật thông tin Task
4. Admin nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /admin/tasks/{taskId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng admin_tasks
   - Lưu thông tin người cập nhật (updatedBy)
7. Backend trả về thông tin Task đã cập nhật
8. Frontend hiển thị thông tin Task đã cập nhật

**Dữ liệu đầu vào:**
```json
{
  "taskName": "Tên Task đã cập nhật",
  "taskDescription": "Mô tả chi tiết đã cập nhật",
  "active": true
}
```

### Xóa Task

**Endpoint:** DELETE /admin/tasks/{taskId}

**Mô tả:** Xóa một Task khỏi hệ thống (soft delete).

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. Admin xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /admin/tasks/{taskId}
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Cập nhật trường deletedAt và deletedBy trong bảng admin_tasks
7. Backend trả về thông báo xóa thành công
8. Frontend chuyển hướng đến trang danh sách Task

## Quản lý Step

### Xem danh sách Step

**Endpoint:** GET /admin/tasks/{taskId}/steps

**Mô tả:** Lấy danh sách các Step của một Task cụ thể.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Frontend gửi request GET đến endpoint /admin/tasks/{taskId}/steps
3. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn danh sách Step từ bảng admin_steps với điều kiện taskId
   - Sắp xếp theo orderIndex
4. Backend trả về danh sách Step
5. Frontend hiển thị danh sách Step theo thứ tự

### Thêm Step mới

**Endpoint:** POST /admin/tasks/{taskId}/steps

**Mô tả:** Thêm một Step mới vào Task.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin nhấn nút "Thêm Step"
3. Admin nhập thông tin Step:
   - Tên Step (bắt buộc)
   - Mô tả Step (tùy chọn)
   - Loại Step (bắt buộc): PROMPT, TRIGGER, ACTION, MEDIA
   - Cấu hình Step (tùy thuộc vào loại Step)
4. Admin nhấn nút "Lưu"
5. Frontend gửi request POST đến endpoint /admin/tasks/{taskId}/steps với dữ liệu Step
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Xác định orderIndex mới (lớn nhất hiện tại + 1)
   - Tạo bản ghi mới trong bảng admin_steps
7. Backend trả về thông tin Step vừa tạo
8. Frontend cập nhật danh sách Step

**Dữ liệu đầu vào:**
```json
{
  "stepName": "Tên Step",
  "stepDescription": "Mô tả chi tiết về Step",
  "stepType": "PROMPT",
  "stepConfig": {
    "content": "Nội dung prompt",
    "variables": ["variable1", "variable2"],
    "options": {
      "temperature": 0.7
    }
  }
}
```

### Cập nhật Step

**Endpoint:** PUT /admin/tasks/{taskId}/steps/{stepId}

**Mô tả:** Cập nhật thông tin của một Step.

**Luồng xử lý:**
1. Admin nhấp vào một Step trong danh sách
2. Admin nhấn nút "Chỉnh sửa"
3. Admin cập nhật thông tin Step
4. Admin nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /admin/tasks/{taskId}/steps/{stepId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng admin_steps
7. Backend trả về thông tin Step đã cập nhật
8. Frontend cập nhật hiển thị Step

**Dữ liệu đầu vào:**
```json
{
  "stepName": "Tên Step đã cập nhật",
  "stepDescription": "Mô tả chi tiết đã cập nhật",
  "stepConfig": {
    "content": "Nội dung prompt đã cập nhật",
    "variables": ["variable1", "variable2", "variable3"],
    "options": {
      "temperature": 0.8
    }
  }
}
```

### Sắp xếp thứ tự Step

**Endpoint:** PUT /admin/tasks/{taskId}/steps/reorder

**Mô tả:** Sắp xếp lại thứ tự các Step trong Task.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin kéo thả các Step để sắp xếp lại thứ tự
3. Admin nhấn nút "Lưu thứ tự"
4. Frontend gửi request PUT đến endpoint /admin/tasks/{taskId}/steps/reorder với danh sách ID Step theo thứ tự mới
5. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Cập nhật trường orderIndex cho từng Step trong bảng admin_steps
6. Backend trả về danh sách Step đã cập nhật thứ tự
7. Frontend cập nhật hiển thị danh sách Step

**Dữ liệu đầu vào:**
```json
{
  "stepIds": ["step-id-1", "step-id-2", "step-id-3", "step-id-4"]
}
```

### Xóa Step

**Endpoint:** DELETE /admin/tasks/{taskId}/steps/{stepId}

**Mô tả:** Xóa một Step khỏi Task.

**Luồng xử lý:**
1. Admin nhấp vào một Step trong danh sách
2. Admin nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. Admin xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /admin/tasks/{taskId}/steps/{stepId}
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Xóa bản ghi từ bảng admin_steps
   - Xóa các Connection liên quan đến Step này
   - Cập nhật lại orderIndex của các Step còn lại
7. Backend trả về thông báo xóa thành công
8. Frontend cập nhật danh sách Step

## Quản lý Connection

### Xem danh sách Connection

**Endpoint:** GET /admin/tasks/{taskId}/connections

**Mô tả:** Lấy danh sách các Connection của một Task cụ thể.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Frontend gửi request GET đến endpoint /admin/tasks/{taskId}/connections
3. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn danh sách Connection từ bảng admin_step_connections với điều kiện taskId
4. Backend trả về danh sách Connection
5. Frontend hiển thị danh sách Connection hoặc biểu diễn dưới dạng đồ thị

### Tạo Connection mới

**Endpoint:** POST /admin/tasks/{taskId}/connections

**Mô tả:** Tạo một Connection mới giữa hai Step.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin nhấn nút "Thêm Connection" hoặc kéo thả để tạo kết nối giữa hai Step
3. Admin cấu hình Connection:
   - Chọn Step nguồn (fromStepId)
   - Chọn Step đích (toStepId)
   - Nhập tên trường output từ Step nguồn (outputField)
   - Nhập tên trường input của Step đích (inputField)
4. Admin nhấn nút "Lưu"
5. Frontend gửi request POST đến endpoint /admin/tasks/{taskId}/connections với dữ liệu Connection
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Kiểm tra tính hợp lệ của Connection (không tạo vòng lặp, không trùng lặp)
   - Tạo bản ghi mới trong bảng admin_step_connections
7. Backend trả về thông tin Connection vừa tạo
8. Frontend cập nhật hiển thị Connection

**Dữ liệu đầu vào:**
```json
{
  "fromStepId": "step-id-1",
  "toStepId": "step-id-2",
  "outputField": "result",
  "inputField": "prompt_variable"
}
```

### Cập nhật Connection

**Endpoint:** PUT /admin/tasks/{taskId}/connections/{connectionId}

**Mô tả:** Cập nhật thông tin của một Connection.

**Luồng xử lý:**
1. Admin nhấp vào một Connection trong danh sách hoặc trên đồ thị
2. Admin nhấn nút "Chỉnh sửa"
3. Admin cập nhật thông tin Connection
4. Admin nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /admin/tasks/{taskId}/connections/{connectionId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng admin_step_connections
7. Backend trả về thông tin Connection đã cập nhật
8. Frontend cập nhật hiển thị Connection

**Dữ liệu đầu vào:**
```json
{
  "outputField": "updated_result",
  "inputField": "updated_prompt_variable"
}
```

### Xóa Connection

**Endpoint:** DELETE /admin/tasks/{taskId}/connections/{connectionId}

**Mô tả:** Xóa một Connection khỏi Task.

**Luồng xử lý:**
1. Admin nhấp vào một Connection trong danh sách hoặc trên đồ thị
2. Admin nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. Admin xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /admin/tasks/{taskId}/connections/{connectionId}
6. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Xóa bản ghi từ bảng admin_step_connections
7. Backend trả về thông báo xóa thành công
8. Frontend cập nhật hiển thị Connection

## Quản lý Execution

### Xem lịch sử Execution

**Endpoint:** GET /admin/tasks/{taskId}/executions

**Mô tả:** Lấy danh sách các lần thực thi (Execution) của một Task cụ thể.

**Luồng xử lý:**
1. Admin truy cập trang chi tiết Task
2. Admin nhấn tab "Lịch sử thực thi"
3. Frontend gửi request GET đến endpoint /admin/tasks/{taskId}/executions với các tham số lọc và phân trang
4. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn danh sách Execution từ bảng admin_task_executions với điều kiện taskId
   - Áp dụng phân trang và sắp xếp
5. Backend trả về danh sách Execution kèm thông tin phân trang
6. Frontend hiển thị danh sách Execution

**Tham số:**
- page: Số trang (mặc định: 1)
- limit: Số lượng Execution trên mỗi trang (mặc định: 10)
- sortBy: Trường sắp xếp (mặc định: startTime)
- sortOrder: Thứ tự sắp xếp (ASC/DESC, mặc định: DESC)
- status: Lọc theo trạng thái (RUNNING, SUCCESS, FAILED, CANCELLED, PAUSED)

### Xem chi tiết Execution

**Endpoint:** GET /admin/tasks/{taskId}/executions/{executionId}

**Mô tả:** Xem thông tin chi tiết của một lần thực thi cụ thể.

**Luồng xử lý:**
1. Admin nhấp vào một Execution trong danh sách
2. Frontend gửi request GET đến endpoint /admin/tasks/{taskId}/executions/{executionId}
3. Backend xử lý request:
   - Kiểm tra quyền truy cập của Admin
   - Truy vấn thông tin Execution từ bảng admin_task_executions
4. Backend trả về thông tin chi tiết Execution, bao gồm executionDetails
5. Frontend hiển thị thông tin chi tiết Execution, bao gồm:
   - Thời gian bắt đầu và kết thúc
   - Trạng thái tổng thể
   - Chi tiết thực thi từng Step
   - Dữ liệu đầu vào/đầu ra
   - Thông tin lỗi (nếu có)