import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserStep } from '../entities/user-step.entity';

/**
 * Repository cho entity UserStep
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_steps
 */
@Injectable()
export class UserStepRepository extends Repository<UserStep> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserStep, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserStep
   * @returns SelectQueryBuilder cho UserStep
   */
  private createBaseQuery(): SelectQueryBuilder<UserStep> {
    return this.createQueryBuilder('step');
  }
}
