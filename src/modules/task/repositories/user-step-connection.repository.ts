import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserStepConnection } from '../entities/user-step-connection.entity';

/**
 * Repository cho entity UserStepConnection
 * X<PERSON> lý các thao tác truy vấn dữ liệu liên quan đến bảng user_step_connections
 */
@Injectable()
export class UserStepConnectionRepository extends Repository<UserStepConnection> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserStepConnection, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserStepConnection
   * @returns SelectQueryBuilder cho UserStepConnection
   */
  private createBaseQuery(): SelectQueryBuilder<UserStepConnection> {
    return this.createQueryBuilder('connection');
  }
}
