import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserTask } from '../entities/user-task.entity';

/**
 * Repository cho entity UserTask
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_tasks
 */
@Injectable()
export class UserTaskRepository extends Repository<UserTask> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserTask, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserTask
   * @returns SelectQueryBuilder cho UserTask
   */
  private createBaseQuery(): SelectQueryBuilder<UserTask> {
    return this.createQueryBuilder('task');
  }
}
