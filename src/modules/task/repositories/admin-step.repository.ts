import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminStep } from '../entities/admin-step.entity';

/**
 * Repository cho entity AdminStep
 * X<PERSON> lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_steps
 */
@Injectable()
export class AdminStepRepository extends Repository<AdminStep> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminStep, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AdminStep
   * @returns SelectQueryBuilder cho AdminStep
   */
  private createBaseQuery(): SelectQueryBuilder<AdminStep> {
    return this.createQueryBuilder('step');
  }
}
