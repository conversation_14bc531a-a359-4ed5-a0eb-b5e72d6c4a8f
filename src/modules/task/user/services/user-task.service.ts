import { Injectable, Logger } from '@nestjs/common';
import { UserTaskRepository, UserTaskExecutionRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến task của người dùng
 */
@Injectable()
export class UserTaskService {
  /**
   * Logger cho UserTaskService
   */
  private readonly logger = new Logger(UserTaskService.name);

  /**
   * Constructor
   * @param userTaskRepository Repository xử lý dữ liệu task của người dùng
   * @param userTaskExecutionRepository Repository xử lý dữ liệu thực thi task của người dùng
   */
  constructor(
    private readonly userTaskRepository: UserTaskRepository,
    private readonly userTaskExecutionRepository: UserTaskExecutionRepository,
  ) {}
}
