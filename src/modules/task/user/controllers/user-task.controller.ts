import { JwtUserGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserTaskService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK)
@Controller('user/tasks')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserTaskController {
  /**
   * Constructor
   * @param userTaskService Service xử lý logic liên quan đến task của người dùng
   */
  constructor(private readonly userTaskService: UserTaskService) {}
}
