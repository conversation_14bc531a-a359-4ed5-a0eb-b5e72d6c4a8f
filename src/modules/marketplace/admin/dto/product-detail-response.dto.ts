import { ApiProperty } from '@nestjs/swagger';
import { ProductResponseDto } from './product-response.dto';
import { ProductStatus } from '@modules/marketplace/enums';

/**
 * DTO cho response trả về chi tiết sản phẩm
 */
export class ProductDetailResponseDto extends ProductResponseDto {
  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'https://example.com/manual.pdf',
    nullable: true,
  })
  userManual: string | null;

  @ApiProperty({
    description: 'Thông tin chi tiết',
    example: 'https://example.com/detail.pdf',
    nullable: true,
  })
  detail: string | null;

  @ApiProperty({
    description: 'ID nguồn sản phẩm',
    example: 'src_456',
    nullable: true,
  })
  sourceId: string | null;
}
