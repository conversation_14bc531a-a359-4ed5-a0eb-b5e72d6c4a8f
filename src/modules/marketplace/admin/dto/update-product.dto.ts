import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, <PERSON><PERSON>ength, Min, <PERSON><PERSON><PERSON>th, ValidateNested } from 'class-validator';

/**
 * DTO cho thông tin cơ bản của sản phẩm khi cập nhật
 */
export class ProductInfoDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Gi<PERSON> niêm yết',
    example: 1200,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountedPrice: number;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'A ready-to-use chatbot template for customer service',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

/**
 * Enum cho các loại thao tác với ảnh
 */
export enum ImageOperationType {
  ADD = 'ADD',
  DELETE = 'DELETE',
}

/**
 * DTO cho thao tác thêm ảnh mới
 */
export class AddImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác: ADD - thêm ảnh mới',
    enum: [ImageOperationType.ADD],
    example: ImageOperationType.ADD,
  })
  @IsEnum(ImageOperationType)
  operation: ImageOperationType.ADD;

  @ApiProperty({
    description: 'MIME type của ảnh',
    example: 'image/png',
  })
  @IsString()
  mimeType: string;

  @ApiProperty({
    description: 'Kích thước của ảnh',
    example: 82928,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  size?: number;

  @ApiProperty({
    description: 'Tên file ảnh',
    example: 'example.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;
}

/**
 * DTO cho thao tác xóa ảnh
 */
export class DeleteImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác: DELETE - xóa ảnh hiện tại',
    enum: [ImageOperationType.DELETE],
    example: ImageOperationType.DELETE,
  })
  @IsEnum(ImageOperationType)
  operation: ImageOperationType.DELETE;

  @ApiProperty({
    description: 'Key của ảnh cần xóa',
    example: 'marketplace/KNOWLEDGEFILE/2025/4/1/12-wqe.png',
  })
  @IsString()
  key: string;
}

/**
 * DTO cho thao tác với ảnh (Union type)
 */
export type ImageOperationDto = AddImageOperationDto | DeleteImageOperationDto;

/**
 * DTO cho việc cập nhật sản phẩm
 */
export class UpdateProductDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm',
    type: ProductInfoDto,
  })
  @ValidateNested()
  @Type(() => ProductInfoDto)
  productInfo: ProductInfoDto;

  @ApiProperty({
    description: 'Danh sách thao tác ảnh',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/AddImageOperationDto' },
        { $ref: '#/components/schemas/DeleteImageOperationDto' }
      ]
    },
    example: [
      {
        "operation": "ADD",
        "mimeType": "image/png"
      },
      {
        "operation": "DELETE",
        "key": "marketplace/IMAGE/2025/05/product-image-0-1746588308827-1746588308827-0d3cedb1-0a96-426e-b3b5-49c43bd44435"
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    discriminator: {
      property: 'operation',
      subTypes: [
        { value: AddImageOperationDto, name: ImageOperationType.ADD },
        { value: DeleteImageOperationDto, name: ImageOperationType.DELETE }
      ]
    },
    keepDiscriminatorProperty: true
  })
  images: (AddImageOperationDto | DeleteImageOperationDto)[];

  @ApiProperty({
    description: 'Cờ chỉ định chỉnh sửa chi tiết',
    example: true,
  })
  @IsBoolean()
  detailEdited: boolean;

  @ApiProperty({
    description: 'Cờ chỉ định cập nhật hướng dẫn sử dụng',
    example: true,
  })
  @IsBoolean()
  userManual: boolean;

  @ApiProperty({
    description: 'Cờ chỉ định đăng bán sản phẩm ngay sau khi cập nhật',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  publishAfterUpdate?: boolean;
}
