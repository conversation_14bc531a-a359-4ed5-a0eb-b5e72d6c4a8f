import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { OrderAdminService } from '../services/order-admin.service';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { OrderQueryDto, OrderResponseDto } from '../dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';

/**
 * Controller xử lý các API liên quan đến đơn hàng cho admin
 */
@ApiTags(SwaggerApiTag.ADMIN_MARKETPLACE_ORDERS)
@ApiExtraModels(ApiResponseDto, OrderResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/marketplace/orders')
export class OrderAdminController {
  constructor(private readonly orderAdminService: OrderAdminService) {}

  /**
   * Lấy danh sách lịch sử mua hàng của tất cả người dùng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên
   * @param queryDto DTO truy vấn
   * @returns Danh sách lịch sử mua hàng phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng của tất cả người dùng',
    description: 'Lấy danh sách đơn hàng của tất cả người dùng (bao gồm cả admin và user) với phân trang, tìm kiếm, lọc và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách đơn hàng phân trang',
    schema: ApiResponseDto.getPaginatedSchema(OrderResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.GENERAL_ERROR
  )
  async getOrders(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: OrderQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<OrderResponseDto>>> {
    const result = await this.orderAdminService.getOrders(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách đơn hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết đơn hàng theo ID
   * @param employeeId ID của nhân viên
   * @param orderId ID của đơn hàng
   * @returns Thông tin chi tiết đơn hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết đơn hàng theo ID',
    description: 'Lấy thông tin chi tiết của một đơn hàng cụ thể bao gồm thông tin người mua và các sản phẩm đã mua'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(OrderResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.GENERAL_ERROR
  )
  async getOrderById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) orderId: number
  ): Promise<ApiResponseDto<OrderResponseDto>> {
    const result = await this.orderAdminService.getOrderById(employeeId, orderId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết đơn hàng thành công');
  }
}