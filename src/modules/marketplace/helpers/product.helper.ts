import { Injectable, Logger } from '@nestjs/common';
import { Product } from '../entities/product.entity';
import { ProductResponseDto, UserProductResponseDto, SellerInfoDto, ProductDetailResponseDto, UserProductDetailResponseDto } from '../user/dto';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { ProductCategory, ProductStatus } from '../enums';

/**
 * Helper class cho việc xử lý và chuyển đổi dữ liệu sản phẩm
 */
@Injectable()
export class ProductHelper {
  private readonly logger = new Logger(ProductHelper.name);

  constructor(private readonly cdnService: CdnService) {}
  /**
   * Chuyển đổi entity Product thành DTO ProductResponseDto
   * @param product Entity Product
   * @returns ProductResponseDto
   */
  mapToProductResponseDto(product: Product | null): ProductResponseDto {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const sellerInfo = this.extractSellerInfo(product);

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      listedPrice: Number(product.listedPrice),
      discountedPrice: Number(product.discountedPrice),
      category: product.category as ProductCategory,
      images: product.images?.map((img, index) => {
        // Đảm bảo rằng mỗi hình ảnh đều có trường position
        // Nếu không có, sử dụng index của mảng
        const position = typeof img.position === 'number' ? img.position : index;

        return {
          key: img.key,
          position: position,
          url: (() => {
            const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
            return url ? `${url}?t=${Date.now()}` : '';
          })()
        };
      }) || [],
      seller: sellerInfo,
      createdAt: Number(product.createdAt),
      soldCount: Number(product.soldCount) || 0,
      canPurchase: Boolean(product.canPurchase),
    };
  }

  /**
   * Chuyển đổi entity Product thành DTO UserProductResponseDto
   * @param product Entity Product
   * @returns UserProductResponseDto
   */
  mapToUserProductResponseDto(product: Product | null): UserProductResponseDto {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const sellerInfo = this.extractSellerInfo(product);

    // Tạo URL cho userManual và detail
    const userManualUrl = product.userManual
      ? (() => {
          const url = this.cdnService.generateUrlView(product.userManual, TimeIntervalEnum.ONE_HOUR);
          return url ? `${url}?t=${Date.now()}` : null;
        })()
      : null;

    const detailUrl = product.detail
      ? (() => {
          const url = this.cdnService.generateUrlView(product.detail, TimeIntervalEnum.ONE_HOUR);
          return url ? `${url}?t=${Date.now()}` : null;
        })()
      : null;

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      listedPrice: Number(product.listedPrice),
      discountedPrice: Number(product.discountedPrice),
      category: product.category as ProductCategory,
      images: product.images?.map((img, index) => {
        // Đảm bảo rằng mỗi hình ảnh đều có trường position
        // Nếu không có, sử dụng index của mảng
        const position = typeof img.position === 'number' ? img.position : index;

        return {
          key: img.key,
          position: position,
          url: (() => {
            const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
            return url ? `${url}?t=${Date.now()}` : '';
          })()
        };
      }) || [],
      seller: sellerInfo,
      createdAt: Number(product.createdAt),
      status: product.status,
      userManual: userManualUrl,
      detail: detailUrl,
      soldCount: Number(product.soldCount) || 0,
      canPurchase: Boolean(product.canPurchase),
    };
  }

  /**
   * Chuyển đổi entity Product thành DTO ProductDetailResponseDto
   * @param product Entity Product
   * @returns ProductDetailResponseDto
   */
  mapToProductDetailResponseDto(product: Product | null): ProductDetailResponseDto {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const sellerInfo = this.extractSellerInfo(product);

    // Tạo URL cho userManual và detail - chỉ lấy URL cơ bản
    const userManualUrl = product.userManual
      ? `https://cdn.redai.vn/${product.userManual}`
      : null;

    const detailUrl = product.detail
      ? `https://cdn.redai.vn/${product.detail}`
      : null;

    this.logger.debug(`Generated basic URLs - userManual: ${userManualUrl}, detail: ${detailUrl}`);

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      images: product.images?.map((img, index) => {
        // Đảm bảo rằng mỗi hình ảnh đều có trường position
        // Nếu không có, sử dụng index của mảng
        const position = typeof img.position === 'number' ? img.position : index;

        // Tạo URL cho ảnh sản phẩm - chỉ lấy URL cơ bản
        let imageUrl = '';
        try {
          // Tạo URL cơ bản: https://cdn.redai.vn/ + key
          imageUrl = `https://cdn.redai.vn/${img.key}`;
          this.logger.debug(`Generated basic URL for image: ${imageUrl}`);
        } catch (error) {
          this.logger.error(`Error generating URL for image ${img.key}: ${error.message}`);
        }

        return {
          key: img.key,
          position: position,
          url: imageUrl
        };
      }) || [],
      listedPrice: Number(product.listedPrice),
      discountedPrice: Number(product.discountedPrice),
      category: product.category as ProductCategory,
      userManual: userManualUrl,
      detail: detailUrl,
      sourceId: product.sourceId || null,
      createdAt: Number(product.createdAt),
      updatedAt: Number(product.updatedAt),
      seller: sellerInfo,
      soldCount: Number(product.soldCount) || 0,
      canPurchase: Boolean(product.canPurchase),
    };
  }

  /**
   * Chuyển đổi entity Product thành DTO ProductDetailResponseDto cho admin
   * @param product Entity Product
   * @returns ProductDetailResponseDto với trường status
   */
  mapToAdminProductDetailResponseDto(product: Product | null): any {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const baseDto = this.mapToProductDetailResponseDto(product);

    // Thêm trường status cho admin
    return {
      ...baseDto,
      status: product.status,
    };
  }

  /**
   * Chuyển đổi entity Product thành DTO UserProductDetailResponseDto
   * @param product Entity Product
   * @returns UserProductDetailResponseDto
   */
  mapToUserProductDetailResponseDto(product: Product | null): UserProductDetailResponseDto {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const sellerInfo = this.extractSellerInfo(product);

    // Tạo URL cho userManual và detail
    const userManualUrl = product.userManual
      ? (() => {
          const url = this.cdnService.generateUrlView(product.userManual, TimeIntervalEnum.ONE_HOUR);
          return url ? `${url}?t=${Date.now()}` : null;
        })()
      : null;

    const detailUrl = product.detail
      ? (() => {
          const url = this.cdnService.generateUrlView(product.detail, TimeIntervalEnum.ONE_HOUR);
          return url ? `${url}?t=${Date.now()}` : null;
        })()
      : null;

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      images: product.images?.map((img, index) => {
        // Đảm bảo rằng mỗi hình ảnh đều có trường position
        // Nếu không có, sử dụng index của mảng
        const position = typeof img.position === 'number' ? img.position : index;

        return {
          key: img.key,
          position: position,
          url: (() => {
            const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
            return url ? `${url}?t=${Date.now()}` : '';
          })()
        };
      }) || [],
      listedPrice: Number(product.listedPrice),
      discountedPrice: Number(product.discountedPrice),
      category: product.category as ProductCategory,
      userManual: userManualUrl,
      detail: detailUrl,
      sourceId: product.sourceId || null,
      createdAt: Number(product.createdAt),
      updatedAt: Number(product.updatedAt),
      seller: sellerInfo,
      status: product.status,
      soldCount: Number(product.soldCount) || 0,
      canPurchase: Boolean(product.canPurchase),
    };
  }

  /**
   * Chuyển đổi entity Product thành DTO ProductResponseDto cho admin
   * @param product Entity Product
   * @returns ProductResponseDto của admin module
   */
  mapToAdminProductResponseDto(product: Product | null): any {
    if (!product) {
      throw new Error('Cannot map null product to DTO');
    }

    const sellerInfo = this.extractSellerInfo(product);

    return {
      id: Number(product.id),
      name: product.name,
      description: product.description,
      listedPrice: Number(product.listedPrice),
      discountedPrice: Number(product.discountedPrice),
      category: product.category as ProductCategory,
      status: product.status as ProductStatus,
      images: product.images?.map(img => img.key) || [],
      seller: sellerInfo,
      createdAt: Number(product.createdAt),
      updatedAt: Number(product.updatedAt),
      soldCount: Number(product.soldCount) || 0,
      canPurchase: Boolean(product.canPurchase),
    };
  }

  /**
   * Trích xuất thông tin người bán từ entity Product
   * @param product Entity Product
   * @returns SellerInfoDto
   */
  extractSellerInfo(product: Product | null): SellerInfoDto {
    if (!product) {
      return {
        id: 0,
        name: 'Unknown',
        avatar: null,
        email: null,
        phoneNumber: null,
        type: 'user',
      };
    }

    // Mặc định
    let sellerInfo: SellerInfoDto = {
      id: 0,
      name: 'Unknown',
      avatar: null,
      email: null,
      phoneNumber: null,
      type: 'user',
    };

    // Chuyển đổi product thành any để truy cập các trường raw data
    const productAny = product as any;

    // Log thông tin để debug
    this.logger.debug(`Product data for seller extraction: ${JSON.stringify({
      id: product.id,
      userId: product.userId,
      employeeId: product.employeeId,
      // Kiểm tra các thuộc tính trong raw data
      hasUserData: !!productAny.users_id || !!productAny.users_fullname,
      hasEmployeeData: !!productAny.employees_id || !!productAny.employees_fullname
    })}`);

    try {
      // Kiểm tra xem có thông tin user không
      if (product.userId) {
        // Kiểm tra thông tin raw data của user - lưu ý lowercase!
        if (productAny.users_id || productAny.users_fullname || productAny.users_email) {
          // Tạo URL cho avatar người dùng nếu có - chỉ lấy URL cơ bản
          let avatarUrl: string | null = null;
          if (productAny.users_avatar) {
            avatarUrl = `https://cdn.redai.vn/${productAny.users_avatar}`;
            this.logger.debug(`Generated basic URL for user avatar: ${avatarUrl}`);
          }

          sellerInfo = {
            id: product.userId,
            name: productAny.users_fullname || productAny.users_email || 'Unknown',
            avatar: avatarUrl,
            email: productAny.users_email || null,
            phoneNumber: productAny.users_phonenumber || null,
            type: 'user',
          };

          this.logger.debug(`Extracted user seller info: ${JSON.stringify(sellerInfo)}`);
        } else {
          // Nếu không có thông tin join, chỉ có userId
          sellerInfo = {
            id: product.userId,
            name: 'User',
            avatar: null,
            email: null,
            phoneNumber: null,
            type: 'user',
          };

          this.logger.debug(`Using default user seller info with ID: ${product.userId}`);
        }
      }
      // Kiểm tra xem có thông tin employee không
      else if (product.employeeId) {
        // Kiểm tra thông tin raw data của employee - lưu ý lowercase!
        if (productAny.employees_id || productAny.employees_fullname || productAny.employees_email) {
          // Tạo URL cho avatar nhân viên nếu có - chỉ lấy URL cơ bản
          let avatarUrl: string | null = null;
          if (productAny.employees_avatar) {
            this.logger.debug(`Found employee avatar: ${productAny.employees_avatar}`);
            avatarUrl = `https://cdn.redai.vn/${productAny.employees_avatar}`;
            this.logger.debug(`Generated basic URL for employee avatar: ${avatarUrl}`);
          } else {
            this.logger.debug(`No avatar found for employee ID: ${product.employeeId}`);
          }

          sellerInfo = {
            id: product.employeeId,
            name: productAny.employees_fullname || productAny.employees_email || 'Unknown',
            avatar: avatarUrl,
            email: productAny.employees_email || null,
            phoneNumber: productAny.employees_phonenumber || null,
            type: 'employee',
          };

          this.logger.debug(`Extracted employee seller info: ${JSON.stringify(sellerInfo)}`);
        } else {
          // Nếu không có thông tin join, chỉ có employeeId
          sellerInfo = {
            id: product.employeeId,
            name: 'Employee',
            avatar: null,
            email: null,
            phoneNumber: null,
            type: 'employee',
          };

          this.logger.debug(`Using default employee seller info with ID: ${product.employeeId}`);
        }
      }

      // Kiểm tra dữ liệu user từ join thủ công
      if (productAny['user.id']) {
        sellerInfo = {
          id: productAny['user.id'],
          name: productAny['user.full_name'] || productAny['user.email'] || 'Unknown',
          avatar: productAny['user.avatar'] || null,
          email: productAny['user.email'] || null,
          phoneNumber: productAny['user.phone_number'] || null,
          type: 'user',
        };

        this.logger.debug(`Using user info from manual join: ${JSON.stringify(sellerInfo)}`);
      }
      // Kiểm tra dữ liệu employee từ join thủ công
      else if (productAny['employee.id']) {
        sellerInfo = {
          id: productAny['employee.id'],
          name: productAny['employee.full_name'] || productAny['employee.email'] || 'Unknown',
          avatar: productAny['employee.avatar'] || null,
          email: productAny['employee.email'] || null,
          phoneNumber: productAny['employee.phone_number'] || null,
          type: 'employee',
        };

        this.logger.debug(`Using employee info from manual join: ${JSON.stringify(sellerInfo)}`);
      }

      // Kiểm tra lần cuối cùng nếu vẫn chưa có thông tin người bán
      if (sellerInfo.id === 0 && product.userId) {
        // Thử tìm thông tin user từ các trường khác
        sellerInfo = {
          id: product.userId,
          name: 'User ' + product.userId,
          avatar: null,
          email: null,
          phoneNumber: null,
          type: 'user',
        };

        this.logger.debug(`Using fallback user info with ID: ${product.userId}`);
      } else if (sellerInfo.id === 0 && product.employeeId) {
        // Thử tìm thông tin employee từ các trường khác
        sellerInfo = {
          id: product.employeeId,
          name: 'Employee ' + product.employeeId,
          avatar: null,
          email: null,
          phoneNumber: null,
          type: 'employee',
        };

        this.logger.debug(`Using fallback employee info with ID: ${product.employeeId}`);
      }
    } catch (error) {
      this.logger.error('Error extracting seller info:', error);
      // Fallback nếu có lỗi
      if (product.userId) {
        sellerInfo = {
          id: product.userId,
          name: 'User',
          avatar: null,
          email: null,
          phoneNumber: null,
          type: 'user',
        };
      } else if (product.employeeId) {
        sellerInfo = {
          id: product.employeeId,
          name: 'Employee',
          avatar: null,
          email: null,
          phoneNumber: null,
          type: 'employee',
        };
      }
    }

    return sellerInfo;
  }
}
