import { Injectable } from '@nestjs/common';
import { MarketOrderLine } from '../entities';
import { PurchaseHistoryItemDto, PurchaseHistorySellerDto } from '../user/dto';
import { MarketOrderExtended } from '../interfaces';
import { PaginatedResult } from '@common/response/api-response-dto';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';

/**
 * Helper class cho việc xử lý và chuyển đổi dữ liệu lịch sử mua hàng
 */
@Injectable()
export class PurchaseHistoryHelper {
  /**
   * Chuyển đổi danh sách MarketOrder thành danh sách PurchaseHistoryItemDto
   * @param orders Danh sách MarketOrder với OrderLines đã được join
   * @returns Danh sách PurchaseHistoryItemDto
   */
  mapToPurchaseHistoryItems(orders: MarketOrderExtended[]): PurchaseHistoryItemDto[] {
    const result: PurchaseHistoryItemDto[] = [];

    for (const order of orders) {
      if (!order.orderLines || order.orderLines.length === 0) {
        continue;
      }

      for (const orderLine of order.orderLines) {
        result.push(
          this.mapOrderLineToPurchaseHistoryItem(order.id, orderLine),
        );
      }
    }

    return result;
  }

  /**
   * Chuyển đổi thông tin người dùng thành DTO thông tin người bán
   * @param user Thông tin người dùng
   * @returns DTO thông tin người bán
   */
  mapUserToSellerDto(user: User | null): PurchaseHistorySellerDto {
    if (!user) {
      return {
        id: 0,
        name: 'Unknown',
        avatar: null,
        email: null,
        phoneNumber: null,
      };
    }

    // Đảm bảo các trường có giá trị hợp lệ
    const id = typeof user.id === 'number' ? user.id : 0;
    const name = user.fullName || 'Unknown';
    const avatar = user.avatar || null;
    const email = user.email || null;
    const phoneNumber = user.phoneNumber || null;

    console.log(`Mapping user to seller: id=${id}, name=${name}, email=${email}`);

    return {
      id,
      name,
      avatar,
      email,
      phoneNumber,
    };
  }

  /**
   * Chuyển đổi thông tin nhân viên thành DTO thông tin người bán
   * @param employee Thông tin nhân viên
   * @returns DTO thông tin người bán
   */
  mapEmployeeToSellerDto(employee: Employee | null): PurchaseHistorySellerDto {
    if (!employee) {
      return {
        id: 0,
        name: 'Unknown',
        avatar: null,
        email: null,
        phoneNumber: null,
      };
    }

    // Đảm bảo các trường có giá trị hợp lệ
    const id = typeof employee.id === 'number' ? employee.id : 0;
    const name = employee.fullName || 'Unknown';
    const avatar = employee.avatar || null;
    const email = employee.email || null;
    const phoneNumber = employee.phoneNumber || null;

    console.log(`Mapping employee to seller: id=${id}, name=${name}, email=${email}`);

    return {
      id,
      name,
      avatar,
      email,
      phoneNumber,
    };
  }

  /**
   * Chuyển đổi MarketOrderLine thành PurchaseHistoryItemDto
   * @param orderId ID của đơn hàng
   * @param orderLine Chi tiết đơn hàng
   * @returns PurchaseHistoryItemDto
   */
  mapOrderLineToPurchaseHistoryItem(
    orderId: number,
    orderLine: MarketOrderLine | null,
  ): PurchaseHistoryItemDto {
    if (!orderLine) {
      throw new Error('Cannot map null order line to DTO');
    }

    console.log(`Mapping order line ${orderLine.id} for order ${orderId}`);
    console.log(`Order line data: productId=${orderLine.productId}, productName=${orderLine.productName}, point=${orderLine.point}, quantity=${orderLine.quantity}`);

    if (orderLine.orderInfo) {
      console.log(`Order line ${orderLine.id} has orderInfo:`, typeof orderLine.orderInfo === 'string' ? orderLine.orderInfo : JSON.stringify(orderLine.orderInfo));
    } else {
      console.log(`Order line ${orderLine.id} does not have orderInfo`);
    }

    // Lấy thông tin người bán từ sản phẩm hoặc từ order_info
    let sellerDto: PurchaseHistorySellerDto;

    // Thử lấy thông tin người bán từ order_info trước
    const seller = this.extractSellerFromOrderInfo(orderLine.orderInfo);

    if (seller) {
      console.log('Found seller info in order_info:', JSON.stringify(seller));
      sellerDto = {
        id: seller.id || 0,
        name: seller.name || 'Unknown',
        avatar: seller.avatar || null,
        email: seller.email || null,
        phoneNumber: seller.phoneNumber || null,
      };
    }
    // Nếu không có thông tin trong order_info, thử lấy từ product
    else if (orderLine.product?.employee) {
      // Nếu người bán là nhân viên
      console.log('Using employee info from product:', JSON.stringify(orderLine.product.employee));
      sellerDto = this.mapEmployeeToSellerDto(orderLine.product.employee);
    } else if (orderLine.product?.user) {
      // Nếu người bán là người dùng
      console.log('Using user info from product:', JSON.stringify(orderLine.product.user));
      sellerDto = this.mapUserToSellerDto(orderLine.product.user);
    } else {
      // Kiểm tra xem product có userId hoặc employeeId không
      if (orderLine.product) {
        console.log('Product info:', JSON.stringify(orderLine.product));

        // Kiểm tra xem có thông tin employee đầy đủ không
        if (orderLine.product.employee && orderLine.product.employee.id) {
          console.log(`Product has employee object: ${JSON.stringify(orderLine.product.employee)}`);
          sellerDto = this.mapEmployeeToSellerDto(orderLine.product.employee);
        }
        // Kiểm tra xem có thông tin user đầy đủ không
        else if (orderLine.product.user && orderLine.product.user.id) {
          console.log(`Product has user object: ${JSON.stringify(orderLine.product.user)}`);
          sellerDto = this.mapUserToSellerDto(orderLine.product.user);
        }
        // Nếu không có thông tin đầy đủ, sử dụng userId hoặc employeeId
        else if (orderLine.product.userId) {
          console.log(`Product has userId: ${orderLine.product.userId}, but no user object`);
          // Tạo thông tin người bán từ userId
          sellerDto = {
            id: orderLine.product.userId,
            name: 'User ' + orderLine.product.userId,
            avatar: null,
            email: null,
            phoneNumber: null,
          };
        } else if (orderLine.product.employeeId) {
          console.log(`Product has employeeId: ${orderLine.product.employeeId}, but no employee object`);
          // Tạo thông tin người bán từ employeeId
          sellerDto = {
            id: orderLine.product.employeeId,
            name: 'Employee ' + orderLine.product.employeeId,
            avatar: null,
            email: null,
            phoneNumber: null,
          };
        } else {
          console.log('Product has no userId or employeeId');
          // Nếu không có thông tin người bán
          sellerDto = {
            id: 0,
            name: 'Unknown',
            avatar: null,
            email: null,
            phoneNumber: null,
          };
        }
      } else {
        console.log('No product info available');
        // Nếu không có thông tin người bán
        sellerDto = {
          id: 0,
          name: 'Unknown',
          avatar: null,
          email: null,
          phoneNumber: null,
        };
      }
    }

    // Nếu không có thông tin người bán, sử dụng giá trị mặc định
    if (!sellerDto) {
      sellerDto = {
        id: 0,
        name: 'Unknown',
        avatar: null,
        email: null,
        phoneNumber: null,
      };
    }

    // Đảm bảo createdAt không bị null
    const createdAt = orderLine.createdAt || Math.floor(Date.now());

    return {
      orderId,
      productId: orderLine.productId,
      productName: orderLine.productName,
      discountedPrice: orderLine.point,
      quantity: orderLine.quantity,
      platformFeePercent: orderLine.platformFeePercent,
      sellerReceivePrice: orderLine.sellerReceivePrice,
      seller: sellerDto,
      createdAt,
    };
  }

  /**
   * Trích xuất thông tin người bán từ trường order_info
   * @param orderInfo Thông tin đơn hàng dạng JSON
   * @returns Thông tin người bán hoặc null nếu không tìm thấy
   */
  extractSellerFromOrderInfo(orderInfo: any): any {
    if (!orderInfo) {
      console.log('orderInfo is null or undefined');
      return null;
    }

    // Nếu orderInfo là số hoặc chuỗi số, không phải là đối tượng JSON
    if (typeof orderInfo === 'number' || (typeof orderInfo === 'string' && !isNaN(Number(orderInfo)))) {
      console.log('orderInfo is a number or numeric string:', orderInfo);
      return null;
    }

    // Nếu orderInfo là chuỗi, thử chuyển đổi thành đối tượng
    let orderInfoObj = orderInfo;
    if (typeof orderInfo === 'string') {
      try {
        orderInfoObj = JSON.parse(orderInfo);
        console.log('Parsed orderInfo from string:', JSON.stringify(orderInfoObj));
      } catch (error) {
        console.log('Failed to parse orderInfo string:', error);
        return null;
      }
    }

    console.log('Extracting seller from orderInfo:', JSON.stringify(orderInfoObj));

    // Trường hợp 1: order_info.product.seller
    if (orderInfoObj.product && orderInfoObj.product.seller) {
      console.log('Found seller in orderInfo.product.seller:', JSON.stringify(orderInfoObj.product.seller));
      return orderInfoObj.product.seller;
    }

    // Trường hợp 2: order_info.seller
    if (orderInfoObj.seller) {
      console.log('Found seller in orderInfo.seller:', JSON.stringify(orderInfoObj.seller));
      return orderInfoObj.seller;
    }

    console.log('No seller found in orderInfo');
    return null;
  }

  /**
   * Chuyển đổi PaginatedResult<MarketOrderExtended> thành PaginatedResult<PurchaseHistoryItemDto>
   * @param paginatedOrders Kết quả phân trang của MarketOrderExtended
   * @returns Kết quả phân trang của PurchaseHistoryItemDto
   */
  mapToPaginatedPurchaseHistory(
    paginatedOrders: PaginatedResult<MarketOrderExtended>,
  ): PaginatedResult<PurchaseHistoryItemDto> {
    const items = this.mapToPurchaseHistoryItems(paginatedOrders.items);

    return {
      items,
      meta: paginatedOrders.meta,
    };
  }
}
