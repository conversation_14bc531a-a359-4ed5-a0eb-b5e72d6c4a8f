import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsNotEmpty, IsOptional, Min } from 'class-validator';

/**
 * DTO cho việc cập nhật số lượng sản phẩm trong giỏ hàng
 * Chỉ cần truyền số lượng cần thêm, không cần truyền productId
 * Số lượng mới sẽ được cộng thêm vào số lượng hiện có
 */
export class UpdateCartItemDto {

  @ApiProperty({
    description: 'Số lượng cần thêm vào giỏ hàng (sẽ được cộng vào số lượng hiện có)',
    example: 3,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  quantity: number;
}
