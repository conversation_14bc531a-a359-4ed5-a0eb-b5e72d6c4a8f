import { Injectable, Logger } from '@nestjs/common';
import { ProductStatus } from '@modules/marketplace/enums/product-status.enum';
import { CartRepository, CartItemRepository, ProductRepository } from '@modules/marketplace/repositories';
import { AddToCartDto, CartResponseDto, QueryCartDto, UpdateCartItemDto } from '../dto';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { CartHelper, ValidationHelper } from '@modules/marketplace/helpers';
import { Cart } from '@modules/marketplace/entities';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class CartUserService {
  private readonly logger = new Logger(CartUserService.name);

  constructor(
    private readonly cartRepository: CartRepository,
    private readonly cartItemRepository: CartItemRepository,
    private readonly productRepository: ProductRepository,
    private readonly cartHelper: CartHelper,
    private readonly validationHelper: ValidationHelper
  ) {}

  /**
   * Lấy giỏ hàng của người dùng
   * @param userId ID người dùng
   * @param queryDto DTO truy vấn
   * @returns Thông tin giỏ hàng
   */
  @Transactional()
  async getCart(userId: number, queryDto: QueryCartDto): Promise<CartResponseDto> {
    try {
      // Tìm giỏ hàng với các bộ lọc (chỉ lấy các sản phẩm có status=APPROVED)
      const cart = await this.cartRepository.findCartWithFilters(userId, queryDto);

      // Nếu không có giỏ hàng, trả về giỏ hàng trống
      if (!cart) {
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      // Chuyển đổi từ entity sang DTO
      return this.cartHelper.mapToCartResponseDto(cart);
    } catch (error) {
      this.logger.error(`Failed to get cart: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED,
        `Không thể lấy thông tin giỏ hàng: ${error.message}`
      );
    }
  }

  /**
   * Thêm sản phẩm vào giỏ hàng
   * @param userId ID người dùng
   * @param addToCartDto DTO thêm vào giỏ hàng
   * @returns Thông tin giỏ hàng sau khi thêm
   */
  @Transactional()
  async addToCart(userId: number, addToCartDto: AddToCartDto): Promise<CartResponseDto> {
    try {
      const { productId, quantity } = addToCartDto;

      // Tìm sản phẩm
      const product = await this.productRepository.findById(productId);

      // Kiểm tra sản phẩm có hợp lệ không (status=APPROVED và không phải sản phẩm của chính mình)
      this.validationHelper.validateProductForCart(product, userId);

      // Tìm hoặc tạo giỏ hàng
      const cart = await this.cartRepository.findOrCreateCart(userId);

      // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
      const existingItem = await this.cartItemRepository.findByCartIdAndProductId(cart.id, productId);

      // Nếu sản phẩm chưa có trong giỏ hàng, kiểm tra số lượng sản phẩm khác nhau
      if (!existingItem) {
        // Đếm số lượng sản phẩm khác nhau trong giỏ hàng
        const itemCount = await this.cartItemRepository.countCartItems(cart.id);
        this.logger.debug(`Số lượng sản phẩm khác nhau trong giỏ hàng: ${itemCount}`);

        // Nếu đã đạt giới hạn 20 sản phẩm khác nhau, báo lỗi
        if (itemCount >= 20) {
          throw new AppException(
            MARKETPLACE_ERROR_CODES.CART_PRODUCT_LIMIT_EXCEEDED,
            'Giỏ hàng đã đạt giới hạn tối đa 20 sản phẩm khác nhau'
          );
        }
      }

      // Thêm sản phẩm vào giỏ hàng (sử dụng phương thức mới trong CartItemRepository)
      await this.cartItemRepository.addToCart(cart.id, productId, quantity);

      // Lấy giỏ hàng đã cập nhật (chỉ lấy các sản phẩm có status=APPROVED)
      const updatedCart = await this.cartRepository.findByUserId(userId);

      // Chuyển đổi từ entity sang DTO
      if (!updatedCart) {
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      return this.cartHelper.mapToCartResponseDto(updatedCart);
    } catch (error) {
      this.logger.error(`Failed to add to cart: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
        `Không thể thêm sản phẩm vào giỏ hàng: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật số lượng sản phẩm trong giỏ hàng
   * @param userId ID người dùng
   * @param cartItemId ID của cart item
   * @param updateCartItemDto DTO cập nhật cart item
   * @returns Thông tin giỏ hàng sau khi cập nhật
   */
  @Transactional()
  async updateCartItem(userId: number, cartItemId: number, updateCartItemDto: UpdateCartItemDto): Promise<CartResponseDto> {
    try {
      this.logger.debug(`Cập nhật cart item với ID: ${cartItemId}, userId: ${userId}, data: ${JSON.stringify(updateCartItemDto)}`);

      // Tìm giỏ hàng của người dùng trước
      const cart = await this.cartRepository.findByUserId(userId);

      if (!cart) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CART_NOT_FOUND,
          'Không tìm thấy giỏ hàng của bạn'
        );
      }

      // Tìm cart item với thông tin sản phẩm
      const cartItem = await this.cartItemRepository.findByIdWithProduct(cartItemId);

      if (!cartItem) {
        // Nếu không tìm thấy cart item với ID đã cho, báo lỗi
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND,
          `Không tìm thấy sản phẩm trong giỏ hàng với ID ${cartItemId}`
        );
      }

      // Kiểm tra quyền sở hữu
      if (cart.id !== cartItem.cartId) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
          'Bạn không có quyền cập nhật sản phẩm này trong giỏ hàng'
        );
      }

      // Kiểm tra sản phẩm có hợp lệ không
      if (!cartItem.product || cartItem.product.status !== ProductStatus.APPROVED) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED,
          'Sản phẩm không còn hợp lệ để mua'
        );
      }

      // Cộng thêm số lượng mới vào số lượng hiện có
      const currentQuantity = cartItem.quantity;
      const additionalQuantity = updateCartItemDto.quantity;
      const newQuantity = currentQuantity + additionalQuantity;

      this.logger.debug(`Cập nhật số lượng sản phẩm trong giỏ hàng: ${cartItemId}, số lượng hiện tại: ${currentQuantity}, số lượng thêm: ${additionalQuantity}, số lượng mới: ${newQuantity}`);
      await this.cartItemRepository.updateQuantity(cartItemId, newQuantity);

      // Lấy giỏ hàng đã cập nhật (chỉ lấy các sản phẩm có status=APPROVED)
      const updatedCart = await this.cartRepository.findByUserId(userId);

      // Chuyển đổi từ entity sang DTO
      if (!updatedCart) {
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      return this.cartHelper.mapToCartResponseDto(updatedCart);
    } catch (error) {
      this.logger.error(`Failed to update cart item: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
        `Không thể cập nhật sản phẩm trong giỏ hàng: ${error.message}`
      );
    }
  }

  /**
   * Xóa sản phẩm khỏi giỏ hàng
   * @param userId ID người dùng
   * @param cartItemId ID của cart item
   * @returns Thông tin giỏ hàng sau khi xóa
   */
  @Transactional()
  async removeCartItem(userId: number, cartItemId: number): Promise<CartResponseDto> {
    try {
      this.logger.debug(`Xóa cart item với ID: ${cartItemId}, userId: ${userId}`);

      // Tìm giỏ hàng của người dùng trước
      const cart = await this.cartRepository.findByUserId(userId);

      if (!cart) {
        this.logger.debug(`Không tìm thấy giỏ hàng của người dùng với ID: ${userId}`);
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      // Kiểm tra xem cart item có tồn tại không
      const cartItemExists = await this.cartItemRepository.checkCartItemExists(cartItemId, cart.id);

      if (!cartItemExists) {
        this.logger.debug(`Không tìm thấy cart item với ID: ${cartItemId} trong giỏ hàng của người dùng`);
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND,
          `Không tìm thấy sản phẩm trong giỏ hàng với ID ${cartItemId}`
        );
      }

      // Đếm số lượng sản phẩm trong giỏ hàng trước khi xóa
      const itemCount = await this.cartItemRepository.countCartItems(cart.id);
      this.logger.debug(`Số lượng sản phẩm trong giỏ hàng trước khi xóa: ${itemCount}`);

      // Xóa cart item
      await this.cartItemRepository.removeCartItem(cartItemId);
      this.logger.debug(`Đã xóa cart item với ID: ${cartItemId}`);

      // Nếu là sản phẩm cuối cùng trong giỏ hàng, xóa luôn giỏ hàng
      if (itemCount === 1) {
        this.logger.debug(`Đây là sản phẩm cuối cùng trong giỏ hàng, tiến hành xóa giỏ hàng với ID: ${cart.id}`);
        await this.cartRepository.removeCart(cart.id);
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      // Lấy giỏ hàng đã cập nhật (chỉ lấy các sản phẩm có status=APPROVED)
      const updatedCart = await this.cartRepository.findByUserId(userId);

      // Chuyển đổi từ entity sang DTO
      if (!updatedCart) {
        return {
          items: [],
          totalValue: 0,
          createdAt: Date.now(),
          updatedAt: Date.now()
        };
      }

      return this.cartHelper.mapToCartResponseDto(updatedCart);
    } catch (error) {
      this.logger.error(`Failed to remove cart item: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
        `Không thể xóa sản phẩm khỏi giỏ hàng: ${error.message}`
      );
    }
  }
}
