/**
 * Interface định nghĩa cấu trúc của API config
 */
export interface ApiConfig {
  enabled: boolean;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
}

/**
 * Interface định nghĩa cấu trúc của UI config
 */
export interface UiConfig {
  submitButtonText?: string;
  showResetButton?: boolean;
  resetButtonText?: string;
  layout?: 'card' | 'flat' | 'outlined';
  spacing?: 'compact' | 'normal' | 'relaxed';
  showRequiredIndicator?: boolean;
}

/**
 * Interface định nghĩa cấu trúc của grid config
 */
export interface GridConfig {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
}

/**
 * Interface định nghĩa cấu trúc của API mapping
 */
export interface ApiMapping {
  field: string;
  path?: string;
  transform?: string;
}

/**
 * Interface định nghĩa cấu trúc của component config
 */
export interface ComponentConfig {
  id: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  validation?: Record<string, any>;
  options?: Array<{ label: string; value: string | number }>;
  [key: string]: any;
}

/**
 * Interface định nghĩa cấu trúc của field config
 */
export interface FieldConfig {
  component: string;
  config: ComponentConfig;
  grid: GridConfig;
  apiMapping?: ApiMapping;
}

/**
 * Interface định nghĩa cấu trúc của group config
 */
export interface GroupConfig {
  id: string;
  label: string;
  fields: FieldConfig[];
}

/**
 * Interface định nghĩa cấu trúc của form config
 */
export interface GenericFormConfig {
  formId: string;
  title: string;
  subtitle?: string;
  groups: GroupConfig[];
  apiConfig?: ApiConfig;
  uiConfig?: UiConfig;
}
