import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiResponseDto } from '@common/response';
import { GenericPageUserService } from '../services';
import { GenericPageResponseDto, SubmissionResponseDto, SubmitFormDto } from '../dto';
import { Request } from 'express';
// TODO: Fix import path or create this decorator
// import { Public } from '@modules/auth/decorators/public.decorator';

@ApiTags('User Generic Page')
@Controller('user/generic-pages')
@ApiExtraModels(ApiResponseDto, GenericPageResponseDto, SubmissionResponseDto)
export class GenericPageUserController {
  constructor(private readonly genericPageUserService: GenericPageUserService) {}

  /**
   * Lấy thông tin trang đã xuất bản theo đường dẫn
   */
  @Get('by-path/:path')
  // @Public()
  @ApiOperation({ summary: 'Lấy thông tin trang đã xuất bản theo đường dẫn' })
  @ApiParam({ name: 'path', description: 'Đường dẫn của trang' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trang',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async getPublishedGenericPageByPath(
    @Param('path') path: string,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageUserService.getPublishedGenericPageByPath(path);
    return ApiResponseDto.success(result);
  }

  /**
   * Gửi dữ liệu form
   */
  @Post(':id/submit')
  // @Public()
  @ApiOperation({ summary: 'Gửi dữ liệu form' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Dữ liệu đã được gửi thành công',
    schema: ApiResponseDto.getSchema(SubmissionResponseDto),
  })
  async submitForm(
    @Param('id') id: string,
    @Body() submitFormDto: SubmitFormDto,
    @Req() request: Request,
    @CurrentUser() user?: JWTPayload,
  ): Promise<ApiResponseDto<SubmissionResponseDto>> {
    const ipAddress = request.ip;
    const userAgent = request.headers['user-agent'];
    
    const result = await this.genericPageUserService.submitForm(
      id,
      submitFormDto,
      user ? String(user.id) : undefined,
      ipAddress,
      userAgent,
    );
    
    return ApiResponseDto.success(result);
  }
}
