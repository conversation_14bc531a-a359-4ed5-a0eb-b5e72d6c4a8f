import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';

/**
 * Mã lỗi cho module Generic Page (40000-40099)
 */
export const GENERIC_PAGE_ERROR_CODES = {
  // Lỗi chung
  GENERIC_PAGE_NOT_FOUND: new ErrorCode(
    40000,
    'Không tìm thấy trang',
    HttpStatus.NOT_FOUND,
  ),
  
  GENERIC_PAGE_TEMPLATE_NOT_FOUND: new ErrorCode(
    40001,
    'Không tìm thấy mẫu trang',
    HttpStatus.NOT_FOUND,
  ),
  
  GENERIC_PAGE_SUBMISSION_NOT_FOUND: new ErrorCode(
    40002,
    'Không tìm thấy dữ liệu gửi',
    HttpStatus.NOT_FOUND,
  ),
  
  // Lỗi tạo và cập nhật
  GENERIC_PAGE_CREATE_ERROR: new ErrorCode(
    40010,
    'Lỗi khi tạo trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_UPDATE_ERROR: new ErrorCode(
    40011,
    'Lỗi khi cập nhật trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_DELETE_ERROR: new ErrorCode(
    40012,
    'Lỗi khi xóa trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_TEMPLATE_CREATE_ERROR: new ErrorCode(
    40013,
    'Lỗi khi tạo mẫu trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_TEMPLATE_UPDATE_ERROR: new ErrorCode(
    40014,
    'Lỗi khi cập nhật mẫu trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_TEMPLATE_DELETE_ERROR: new ErrorCode(
    40015,
    'Lỗi khi xóa mẫu trang',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  GENERIC_PAGE_SUBMISSION_CREATE_ERROR: new ErrorCode(
    40016,
    'Lỗi khi gửi dữ liệu form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  // Lỗi validation
  GENERIC_PAGE_PATH_ALREADY_EXISTS: new ErrorCode(
    40020,
    'Đường dẫn đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_INVALID_CONFIG: new ErrorCode(
    40021,
    'Cấu hình trang không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_INVALID_STATUS: new ErrorCode(
    40022,
    'Trạng thái trang không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_TEMPLATE_INVALID_CONFIG: new ErrorCode(
    40023,
    'Cấu hình mẫu trang không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_SUBMISSION_INVALID_DATA: new ErrorCode(
    40024,
    'Dữ liệu gửi không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  
  // Lỗi trạng thái
  GENERIC_PAGE_ALREADY_PUBLISHED: new ErrorCode(
    40030,
    'Trang đã được xuất bản',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_NOT_PUBLISHED: new ErrorCode(
    40031,
    'Trang chưa được xuất bản',
    HttpStatus.BAD_REQUEST,
  ),
  
  GENERIC_PAGE_ALREADY_ARCHIVED: new ErrorCode(
    40032,
    'Trang đã được lưu trữ',
    HttpStatus.BAD_REQUEST,
  ),
};
