import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';
import { GenericPageSortByEnum, GenericPageStatusEnum } from '../../constants/generic-page.enum';

/**
 * DTO cho việc truy vấn danh sách trang
 */
export class QueryGenericPageDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: GenericPageSortByEnum,
    default: GenericPageSortByEnum.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(GenericPageSortByEnum, {
    message: `sortBy phải là một trong các giá trị: ${Object.values(GenericPageSortByEnum).join(', ')}`,
  })
  sortBy?: GenericPageSortByEnum = GenericPageSortByEnum.CREATED_AT;

  /**
   * Lọc theo trạng thái
   * @example "published"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: GenericPageStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(GenericPageStatusEnum, {
    message: `status phải là một trong các giá trị: ${Object.values(GenericPageStatusEnum).join(', ')}`,
  })
  status?: GenericPageStatusEnum;
}
