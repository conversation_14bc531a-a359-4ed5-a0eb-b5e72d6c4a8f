import { Injectable, Logger } from '@nestjs/common';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../../exceptions/generic-page-error.code';
import { GenericPageRepository } from '../../repositories/generic-page.repository';
import { GenericPage } from '../../entities/generic-page.entity';
import { CreateGenericPageDto, GenericPageResponseDto, QueryGenericPageDto, UpdateGenericPageDto } from '../dto';
import { GenericPageStatusEnum } from '../../constants/generic-page.enum';

@Injectable()
export class GenericPageAdminService {
  private readonly logger = new Logger(GenericPageAdminService.name);

  constructor(
    private readonly genericPageRepository: GenericPageRepository,
  ) {}

  /**
   * Tạo trang mới
   * @param createGenericPageDto Thông tin trang mới
   * @param employeeId ID của nhân viên tạo trang
   * @returns Thông tin trang đã tạo
   */
  async createGenericPage(
    createGenericPageDto: CreateGenericPageDto,
    employeeId: string,
  ): Promise<GenericPageResponseDto> {
    try {
      // Kiểm tra xem đường dẫn đã tồn tại chưa
      const isPathExists = await this.genericPageRepository.isPathExists(createGenericPageDto.path);
      if (isPathExists) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_PATH_ALREADY_EXISTS,
          `Đường dẫn ${createGenericPageDto.path} đã tồn tại`,
        );
      }

      // Tạo entity mới
      const genericPage = new GenericPage();
      genericPage.name = createGenericPageDto.name;
      genericPage.description = createGenericPageDto.description || '';
      genericPage.path = createGenericPageDto.path;
      genericPage.config = createGenericPageDto.config;
      genericPage.status = GenericPageStatusEnum.DRAFT;
      genericPage.createdAt = Date.now();
      genericPage.updatedAt = Date.now();
      genericPage.publishedAt = null;
      genericPage.createdBy = employeeId;
      genericPage.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPage = await this.genericPageRepository.save(genericPage);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating generic page: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_CREATE_ERROR,
        'Lỗi khi tạo trang',
      );
    }
  }

  /**
   * Cập nhật trang
   * @param id ID của trang
   * @param updateGenericPageDto Thông tin cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Thông tin trang đã cập nhật
   */
  async updateGenericPage(
    id: string,
    updateGenericPageDto: UpdateGenericPageDto,
    employeeId: string,
  ): Promise<GenericPageResponseDto> {
    try {
      // Tìm trang theo ID
      const genericPage = await this.genericPageRepository.findById(id);

      // Kiểm tra xem đường dẫn đã tồn tại chưa (nếu có cập nhật đường dẫn)
      if (updateGenericPageDto.path && updateGenericPageDto.path !== genericPage.path) {
        const isPathExists = await this.genericPageRepository.isPathExists(updateGenericPageDto.path, id);
        if (isPathExists) {
          throw new AppException(
            GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_PATH_ALREADY_EXISTS,
            `Đường dẫn ${updateGenericPageDto.path} đã tồn tại`,
          );
        }
      }

      // Cập nhật thông tin
      if (updateGenericPageDto.name) {
        genericPage.name = updateGenericPageDto.name;
      }
      if (updateGenericPageDto.description !== undefined) {
        genericPage.description = updateGenericPageDto.description;
      }
      if (updateGenericPageDto.path) {
        genericPage.path = updateGenericPageDto.path;
      }
      if (updateGenericPageDto.config) {
        genericPage.config = updateGenericPageDto.config;
      }
      genericPage.updatedAt = Date.now();
      genericPage.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPage = await this.genericPageRepository.save(genericPage);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating generic page: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR,
        `Lỗi khi cập nhật trang với ID ${id}`,
      );
    }
  }

  /**
   * Lấy thông tin trang theo ID
   * @param id ID của trang
   * @returns Thông tin trang
   */
  async getGenericPageById(id: string): Promise<GenericPageResponseDto> {
    try {
      const genericPage = await this.genericPageRepository.findById(id);
      return this.mapToResponseDto(genericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting generic page by ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Lỗi khi lấy thông tin trang với ID ${id}`,
      );
    }
  }

  /**
   * Lấy thông tin trang theo đường dẫn
   * @param path Đường dẫn của trang
   * @returns Thông tin trang
   */
  async getGenericPageByPath(path: string): Promise<GenericPageResponseDto> {
    try {
      const genericPage = await this.genericPageRepository.findByPath(path);
      return this.mapToResponseDto(genericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting generic page by path: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Lỗi khi lấy thông tin trang với đường dẫn ${path}`,
      );
    }
  }

  /**
   * Xuất bản trang
   * @param id ID của trang
   * @param employeeId ID của nhân viên xuất bản
   * @returns Thông tin trang đã xuất bản
   */
  async publishGenericPage(id: string, employeeId: string): Promise<GenericPageResponseDto> {
    try {
      // Tìm trang theo ID
      const genericPage = await this.genericPageRepository.findById(id);

      // Kiểm tra xem trang đã xuất bản chưa
      if (genericPage.status === GenericPageStatusEnum.PUBLISHED) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_ALREADY_PUBLISHED,
          'Trang đã được xuất bản',
        );
      }

      // Cập nhật trạng thái
      genericPage.status = GenericPageStatusEnum.PUBLISHED;
      genericPage.publishedAt = Date.now();
      genericPage.updatedAt = Date.now();
      genericPage.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPage = await this.genericPageRepository.save(genericPage);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error publishing generic page: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR,
        `Lỗi khi xuất bản trang với ID ${id}`,
      );
    }
  }

  /**
   * Hủy xuất bản trang
   * @param id ID của trang
   * @param employeeId ID của nhân viên hủy xuất bản
   * @returns Thông tin trang đã hủy xuất bản
   */
  async unpublishGenericPage(id: string, employeeId: string): Promise<GenericPageResponseDto> {
    try {
      // Tìm trang theo ID
      const genericPage = await this.genericPageRepository.findById(id);

      // Kiểm tra xem trang đã xuất bản chưa
      if (genericPage.status !== GenericPageStatusEnum.PUBLISHED) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_PUBLISHED,
          'Trang chưa được xuất bản',
        );
      }

      // Cập nhật trạng thái
      genericPage.status = GenericPageStatusEnum.DRAFT;
      genericPage.updatedAt = Date.now();
      genericPage.updatedBy = employeeId;

      // Lưu vào cơ sở dữ liệu
      const savedGenericPage = await this.genericPageRepository.save(genericPage);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedGenericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error unpublishing generic page: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR,
        `Lỗi khi hủy xuất bản trang với ID ${id}`,
      );
    }
  }

  /**
   * Xóa trang
   * @param id ID của trang
   */
  async deleteGenericPage(id: string): Promise<void> {
    try {
      // Tìm trang theo ID
      const genericPage = await this.genericPageRepository.findById(id);

      // Xóa trang
      await this.genericPageRepository.remove(genericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting generic page: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_DELETE_ERROR,
        `Lỗi khi xóa trang với ID ${id}`,
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param genericPage Entity GenericPage
   * @returns DTO GenericPageResponseDto
   */
  private mapToResponseDto(genericPage: GenericPage): GenericPageResponseDto {
    const responseDto = new GenericPageResponseDto();
    responseDto.id = genericPage.id;
    responseDto.name = genericPage.name;
    responseDto.description = genericPage.description;
    responseDto.path = genericPage.path;
    responseDto.config = genericPage.config;
    responseDto.status = genericPage.status;
    responseDto.createdAt = genericPage.createdAt;
    responseDto.updatedAt = genericPage.updatedAt;
    responseDto.publishedAt = genericPage.publishedAt;
    responseDto.createdBy = genericPage.createdBy;
    responseDto.updatedBy = genericPage.updatedBy;
    return responseDto;
  }
}
