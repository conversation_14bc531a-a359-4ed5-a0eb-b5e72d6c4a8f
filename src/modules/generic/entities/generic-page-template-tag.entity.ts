import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { GenericPageTemplate } from './generic-page-template.entity';

/**
 * Entity đại diện cho bảng generic_page_template_tags trong cơ sở dữ liệu
 * Bảng lưu trữ các tag cho mẫu trang (quan hệ nhiều-nhiều)
 */
@Entity('generic_page_template_tags')
export class GenericPageTemplateTag {
  /**
   * ID của mẫu trang, tham chiếu đến bảng generic_page_templates
   */
  @PrimaryColumn({ name: 'template_id', length: 36 })
  templateId: string;

  /**
   * Tên tag, dùng để phân loại và tìm kiếm mẫu trang
   */
  @PrimaryColumn({ length: 50 })
  tag: string;

  /**
   * Quan hệ với mẫu trang
   */
  @ManyToOne(() => GenericPageTemplate, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'template_id' })
  template: GenericPageTemplate;
}
