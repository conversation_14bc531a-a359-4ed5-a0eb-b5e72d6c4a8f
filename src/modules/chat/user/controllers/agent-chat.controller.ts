import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiResponseDto } from '@common/response';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { CreateRunChatDto } from '@modules/chat/user/dto/create-run-chat.dto';
import { AgentBaseChatService } from '../services/agent-base-chat.service';
import { RunChatResponseDto } from '@modules/chat/user/dto/run-chat-response.dto';

@Controller('user/agent-chat')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentChatController {
  constructor(private readonly agentBaseChatService: AgentBaseChatService) {}

  @Post('run/base')
  async createRunAgentBase(
    @Body() createRunChatDto: CreateRunChatDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<RunChatResponseDto>> {
    const result = await this.agentBaseChatService.createRunAgentBase(
      createRunChatDto,
      userId,
    );
    return ApiResponseDto.success(result);
  }
}
