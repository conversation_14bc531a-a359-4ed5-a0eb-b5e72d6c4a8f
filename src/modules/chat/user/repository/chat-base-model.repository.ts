import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BaseModel } from '@modules/model-training/entities';

@Injectable()
export class ChatBaseModelRepository extends Repository<BaseModel> {
  private readonly logger = new Logger(ChatBaseModelRepository.name);

  constructor(private dataSource: DataSource) {
    super(BaseModel, dataSource.createEntityManager());
  }
}
