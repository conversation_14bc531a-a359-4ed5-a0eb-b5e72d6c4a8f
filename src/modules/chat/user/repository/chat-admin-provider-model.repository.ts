import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AdminProviderModel } from '@modules/model-training/entities';

@Injectable()
export class ChatAdminProviderModelRepository extends Repository<AdminProviderModel>{
  private readonly logger = new Logger(ChatAdminProviderModelRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminProviderModel, dataSource.createEntityManager());
  }
}
