import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  Agent,
  AgentBase,
  AgentRole,
  AgentSystem,
  AgentUser,
  UserMultiAgent,
} from '@modules/agent/entities';
import { AgentModule } from '@modules/agent/agent.module';
import { UserModule } from '@modules/user/user.module';
import { AdminProviderModel, BaseModel } from '@modules/model-training/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      AgentBase,
      AgentSystem,
      AgentUser,
      AgentRole,
      UserMultiAgent,
      BaseModel,
      AdminProviderModel,
    ]),
    AgentModule,
    UserModule,
  ],
})
export class ChatModule {}
