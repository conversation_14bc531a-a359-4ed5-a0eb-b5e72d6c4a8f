import { Body, Controller, Get, Param, ParseIntPipe, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { SystemConfigurationService } from '../service';
import { SystemConfigurationDto, UpdateSystemConfigurationDto } from '../../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { SystemConfiguration } from '../../entities/system-configuration.entity';

/**
 * Controller xử lý các API liên quan đến cấu hình hệ thống cho admin
 */
@ApiTags('Admin - System Configuration')
@Controller('admin/system-configuration')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class SystemConfigurationAdminController {
  constructor(private readonly systemConfigurationService: SystemConfigurationService) {}

  /**
   * <PERSON><PERSON>y tất cả cấu hình hệ thống
   * @returns Danh sách cấu hình hệ thống
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy tất cả cấu hình hệ thống',
    description: 'API này trả về danh sách tất cả cấu hình hệ thống'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách cấu hình thành công',
    type: ApiResponseDto
  })
  async getAllConfigurations(): Promise<ApiResponseDto<SystemConfiguration[]>> {
    const configurations = await this.systemConfigurationService.getAllConfigurations();
    return {
      code: 200,
      message: 'Lấy danh sách cấu hình thành công',
      result: configurations
    };
  }

  /**
   * Lấy cấu hình hệ thống theo ID
   * @param id ID của cấu hình
   * @returns Thông tin cấu hình
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy cấu hình hệ thống theo ID',
    description: 'API này trả về thông tin chi tiết của một cấu hình hệ thống'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    type: Number
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin cấu hình thành công',
    type: ApiResponseDto
  })
  async getConfigurationById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<SystemConfiguration>> {
    const configuration = await this.systemConfigurationService.getConfigurationById(id);
    return {
      code: 200,
      message: 'Lấy thông tin cấu hình thành công',
      result: configuration
    };
  }

  /**
   * Cập nhật cấu hình hệ thống
   * @param id ID của cấu hình
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật cấu hình hệ thống',
    description: 'API này cập nhật thông tin của một cấu hình hệ thống'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    type: Number
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật cấu hình thành công',
    type: ApiResponseDto
  })
  async updateConfiguration(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateSystemConfigurationDto
  ): Promise<ApiResponseDto<SystemConfiguration>> {
    const configuration = await this.systemConfigurationService.updateConfiguration(id, updateDto);
    return {
      code: 200,
      message: 'Cập nhật cấu hình thành công',
      result: configuration
    };
  }

  /**
   * Tạo cấu hình hệ thống mới
   * @param configurationDto Thông tin cấu hình
   * @returns Cấu hình đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo cấu hình hệ thống mới',
    description: 'API này tạo một cấu hình hệ thống mới'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo cấu hình thành công',
    type: ApiResponseDto
  })
  async createConfiguration(
    @Body() configurationDto: SystemConfigurationDto
  ): Promise<ApiResponseDto<SystemConfiguration>> {
    const configuration = await this.systemConfigurationService.createConfiguration(configurationDto);
    return {
      code: 201,
      message: 'Tạo cấu hình thành công',
      result: configuration
    };
  }

  /**
   * Lấy cấu hình hệ thống đang active
   * @returns Thông tin cấu hình đang active
   */
  @Get('active')
  @ApiOperation({
    summary: 'Lấy cấu hình hệ thống đang active',
    description: 'API này trả về thông tin cấu hình hệ thống đang active'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy cấu hình đang active thành công',
    type: ApiResponseDto
  })
  async getActiveConfiguration(): Promise<ApiResponseDto<SystemConfiguration>> {
    const configuration = await this.systemConfigurationService.getActiveConfiguration();
    return {
      code: 200,
      message: 'Lấy cấu hình đang active thành công',
      result: configuration
    };
  }
}
