/**
 * Controller xử lý các API endpoint cho StrategyAgentVersion
 */
import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ApiResponseDto } from '@common/response/api-response-dto';

import { StrategyAgentVersionService } from '../services/strategy-agent-version.service';
import { CreateStrategyAgentVersionDto, UpdateStrategyAgentVersionDto } from '../dto/strategy-agent-version.dto';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';

@ApiTags('Strategy Agent Version')
@Controller('admin/strategy-agent-versions')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class StrategyAgentVersionController {
  constructor(private readonly versionService: StrategyAgentVersionService) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách phiên bản của một chiến lược
   * @param strategyId ID của chiến lược
   * @returns Danh sách phiên bản
   */
  @Get('strategy/:strategyId')
  @ApiOperation({ summary: 'Lấy danh sách phiên bản của một chiến lược' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách phiên bản thành công' })
  async getVersions(@Param('strategyId') strategyId: string) {
    const result = await this.versionService.getVersions(strategyId);
    return ApiResponseDto.success(result, 'Lấy danh sách phiên bản thành công');
  }

  /**
   * Lấy chi tiết phiên bản
   * @param versionId ID của phiên bản
   * @returns Chi tiết phiên bản
   */
  @Get(':versionId')
  @ApiOperation({ summary: 'Lấy chi tiết phiên bản' })
  @ApiResponse({ status: 200, description: 'Lấy chi tiết phiên bản thành công' })
  async getVersionDetail(@Param('versionId', ParseIntPipe) versionId: number) {
    const result = await this.versionService.getVersionDetail(versionId);
    return ApiResponseDto.success(result, 'Lấy chi tiết phiên bản thành công');
  }

  /**
   * Tạo phiên bản mới
   * @param employeeId ID của nhân viên
   * @param createDto Thông tin phiên bản mới
   * @returns Phiên bản đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo phiên bản mới' })
  @ApiResponse({ status: 201, description: 'Tạo phiên bản mới thành công' })
  async createVersion(@CurrentEmployee('id') employeeId: number, @Body() createDto: CreateStrategyAgentVersionDto) {
    const result = await this.versionService.createVersion(employeeId, createDto);
    return ApiResponseDto.success(result, 'Tạo phiên bản mới thành công');
  }

  /**
   * Cập nhật phiên bản
   * @param versionId ID của phiên bản
   * @param employeeId ID của nhân viên
   * @param updateDto Thông tin cập nhật
   * @returns Phiên bản đã cập nhật
   */
  @Put(':versionId')
  @ApiOperation({ summary: 'Cập nhật phiên bản' })
  @ApiResponse({ status: 200, description: 'Cập nhật phiên bản thành công' })
  async updateVersion(
    @Param('versionId', ParseIntPipe) versionId: number,
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdateStrategyAgentVersionDto,
  ) {
    const result = await this.versionService.updateVersion(versionId, employeeId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật phiên bản thành công');
  }

  /**
   * Xóa phiên bản
   * @param versionId ID của phiên bản
   * @returns Thông báo xóa thành công
   */
  @Delete(':versionId')
  @ApiOperation({ summary: 'Xóa phiên bản' })
  @ApiResponse({ status: 200, description: 'Xóa phiên bản thành công' })
  async deleteVersion(@Param('versionId', ParseIntPipe) versionId: number) {
    await this.versionService.deleteVersion(versionId);
    return ApiResponseDto.success(null, 'Xóa phiên bản thành công');
  }
}
