import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa mềm cấp bậc chiến lược
 */
export class DeleteRankStrategyDto {
  /**
   * ID của cấp bậc thay thế (bắt buộc nếu cấp bậc đang được sử dụng)
   * @example 2
   */
  @ApiPropertyOptional({ 
    description: 'ID của cấp bậc thay thế (bắt buộc nếu cấp bậc đang được sử dụng)', 
    example: 2 
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID cấp bậc thay thế phải là số' })
  @Type(() => Number)
  replacementRankId?: number;
}
