import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc tạo nội dung chiến lược của người dùng
 */
export class CreateStrategyContentDto {
  @ApiProperty({
    description: 'ID của phiên bản chiến lược agent',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  strategyVersionId: number;

  @ApiProperty({
    description: 'ID của bước nội dung gốc từ phiên bản chính thức',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  strategyContentStepId: number;

  @ApiProperty({
    description: 'ID của agent người dùng',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  agentId?: string;

  @ApiProperty({
    description: 'Nội dung có thể chỉnh sửa của bước',
    example: 'Nội dung đã được tùy chỉnh bởi người dùng',
  })
  @IsString()
  @IsNotEmpty()
  editableContent: string;

  @ApiProperty({
    description: 'Thứ tự của bước trong chuỗi xử lý',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  stepOrder: number;
}
