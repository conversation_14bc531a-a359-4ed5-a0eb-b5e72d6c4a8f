import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';

/**
 * DTO cho các tham số query liên quan đến nội dung chiến lược
 * Mở rộng từ QueryDto để kế thừa các trường cơ bản
 */
export class QueryStrategyContentDto extends QueryDto {
  @ApiProperty({
    description: 'ID của phiên bản chiến lược agent',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  strategyVersionId?: number;

  @ApiProperty({
    description: 'ID của agent người dùng',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  agentId?: string;
}
