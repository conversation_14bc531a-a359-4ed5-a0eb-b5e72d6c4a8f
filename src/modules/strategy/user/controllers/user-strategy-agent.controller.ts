/**
 * Controller xử lý các API endpoint cho UserStrategyAgent
 */
import { Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ApiResponseDto } from '@common/response/api-response-dto';

import { UserStrategyAgentService } from '../services/user-strategy-agent.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { SWAGGER_API_TAGS } from '@common/swagger';

@ApiTags(SWAGGER_API_TAGS.USER_STRATEGY)
@Controller('api/v1/user/strategy-agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserStrategyAgentController {
  constructor(private readonly userStrategyService: UserStrategyAgentService) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách chiến lược của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách chiến lược
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách chiến lược của người dùng' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách chiến lược thành công' })
  async getUserStrategies(@CurrentUser('id') userId: number) {
    const result = await this.userStrategyService.getUserStrategies(userId);
    return ApiResponseDto.success(result, 'Lấy danh sách chiến lược thành công');
  }

  /**
   * Gán chiến lược cho người dùng
   * @param userId ID của người dùng
   * @param strategyId ID của chiến lược
   * @returns Thông tin gán chiến lược
   */
  @Post(':strategyId/assign')
  @ApiOperation({ summary: 'Gán chiến lược cho người dùng' })
  @ApiResponse({ status: 201, description: 'Gán chiến lược thành công' })
  async assignStrategy(@CurrentUser('id') userId: number, @Param('strategyId') strategyId: string) {
    const result = await this.userStrategyService.assignStrategyToUser(userId, strategyId);
    return ApiResponseDto.success(result, 'Gán chiến lược thành công');
  }

  /**
   * Gỡ chiến lược khỏi người dùng
   * @param userId ID của người dùng
   * @param strategyId ID của chiến lược
   * @returns Thông báo gỡ thành công
   */
  @Delete(':strategyId/remove')
  @ApiOperation({ summary: 'Gỡ chiến lược khỏi người dùng' })
  @ApiResponse({ status: 200, description: 'Gỡ chiến lược thành công' })
  async removeStrategy(@CurrentUser('id') userId: number, @Param('strategyId') strategyId: string) {
    await this.userStrategyService.removeStrategyFromUser(userId, strategyId);
    return ApiResponseDto.success(null, 'Gỡ chiến lược thành công');
  }

  /**
   * Kiểm tra người dùng có quyền truy cập chiến lược không
   * @param userId ID của người dùng
   * @param strategyId ID của chiến lược
   * @returns true nếu có quyền, false nếu không
   */
  @Get(':strategyId/check-access')
  @ApiOperation({ summary: 'Kiểm tra quyền truy cập chiến lược' })
  @ApiResponse({ status: 200, description: 'Kiểm tra quyền truy cập thành công' })
  async checkAccess(@CurrentUser('id') userId: number, @Param('strategyId') strategyId: string) {
    const hasAccess = await this.userStrategyService.checkUserHasAccess(userId, strategyId);
    return ApiResponseDto.success(hasAccess, 'Kiểm tra quyền truy cập thành công');
  }
}
