import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { Controller, Delete, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { QueryUserStrategyDto } from '../dto';
import { StrategyAgentUserService } from '../services';

@ApiTags(SWAGGER_API_TAGS.USER_STRATEGY)
@Controller('user')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class StrategyAgentUserController {
  constructor(private readonly strategyAgentUserService: StrategyAgentUserService) { }

  @Get('strategies')
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách Strategy đã mua của người dùng' })
  @ApiResponse({ status: 200, description: 'L<PERSON>y danh sách Strategy đã mua thành công.' })
  async getUserStrategies(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryUserStrategyDto
  ) {
    // Không cần xử lý sortBy ở đây nữa vì đã xử lý trong DTO

    const result = await this.strategyAgentUserService.getUserStrategies(userId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách Strategy đã mua thành công.');
  }

  @Get('strategies/:strategyId')
  @ApiOperation({ summary: 'Lấy chi tiết Strategy đã mua' })
  @ApiResponse({ status: 200, description: 'Lấy chi tiết Strategy đã mua thành công.' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Strategy hoặc bạn không có quyền truy cập.' })
  async getUserStrategyDetail(
    @CurrentUser('id') userId: number,
    @Param('strategyId') strategyId: string
  ) {
    const result = await this.strategyAgentUserService.getStrategyDetail(userId, strategyId);
    return ApiResponseDto.success({ strategy: result }, 'Lấy chi tiết Strategy đã mua thành công.');
  }

  @Delete('agents/:agentId/strategies/:strategyId')
  @ApiOperation({ summary: 'Gỡ Strategy khỏi Agent' })
  @ApiResponse({ status: 200, description: 'Gỡ Strategy khỏi Agent thành công.' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy liên kết giữa Agent và Strategy.' })
  async removeStrategyFromAgent(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Param('strategyId') strategyId: string
  ) {
    await this.strategyAgentUserService.removeStrategyFromAgent(userId, agentId, strategyId);
    return ApiResponseDto.success(null, 'Gỡ Strategy khỏi Agent thành công.');
  }

  /**
   * Gỡ Strategy khỏi tất cả Agent đang sử dụng
   * @param userId ID của người dùng hiện tại
   * @param strategyId ID của Strategy cần gỡ
   * @returns Thông báo thành công
   */
  @Delete('strategies/:strategyId/remove-from-all-agents')
  @ApiOperation({ summary: 'Gỡ Strategy khỏi tất cả Agent đang sử dụng' })
  @ApiResponse({ status: 200, description: 'Gỡ Strategy khỏi tất cả Agent thành công.' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Strategy hoặc bạn không có quyền truy cập.' })
  async removeStrategyFromAllAgents(
    @CurrentUser('id') userId: number,
    @Param('strategyId') strategyId: string
  ) {
    await this.strategyAgentUserService.removeStrategyFromAllAgents(userId, strategyId);
    return ApiResponseDto.success(null, 'Gỡ Strategy khỏi tất cả Agent thành công.');
  }
}
