import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UserStrategyAgentUserService } from '../services';

@ApiTags(SWAGGER_API_TAGS.USER_STRATEGY)
@Controller('user/user-strategy-agents')
export class UserStrategyAgentUserController {
  constructor(private readonly userStrategyAgentUserService: UserStrategyAgentUserService) { }
}
