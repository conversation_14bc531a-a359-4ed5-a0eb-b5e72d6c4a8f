import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { StrategyAgent } from '@modules/strategy/entities';
import { QueryStrategyAgentDto } from '../admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';
import { StrategyStatusEnum } from '../constants/strategy-status.enum';

/**
 * Repository cho entity StrategyAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến chiến lược agent
 */
@Injectable()
export class StrategyAgentRepository extends Repository<StrategyAgent> {
  constructor(private dataSource: DataSource) {
    super(StrategyAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho StrategyAgent
   * @returns SelectQueryBuilder cho StrategyAgent
   */
  private createBaseQuery(): SelectQueryBuilder<StrategyAgent> {
    return this.createQueryBuilder('strategy');
  }

  /**
   * Tìm kiếm danh sách Strategy Agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findPaginated(queryDto: QueryStrategyAgentDto): Promise<PaginatedResult<StrategyAgent>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC', status } = queryDto;

    const strategyFields = ['id', 'name', 'description', 'tags', 'createdAt', 'updatedAt', 'status', 'rankId'];
    const rankFields = ['id', 'name', 'image'];

    const qb = this.createBaseQuery()
      .select([
        ...strategyFields.map(field => `strategy.${field}`),
        ...rankFields.map(field => `rank.${field}`)
      ])
      .leftJoin('strategy.rankStrategy', 'rank');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(strategy.name ILIKE :search OR strategy.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('strategy.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`strategy.${sortBy}`, sortDirection as 'ASC' | 'DESC');

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Tìm Strategy Agent theo ID
   * @param id ID của Strategy Agent
   * @returns Strategy Agent hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<StrategyAgent | null> {
    const strategyFields = [
      'id', 'name', 'description', 'rankId', 'tags', 
      'status', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy'
    ];
    const rankFields = ['id', 'name', 'image'];

    return this.createBaseQuery()
      .select([
        ...strategyFields.map(field => `strategy.${field}`),
        ...rankFields.map(field => `rank.${field}`)
      ])
      .leftJoin('strategy.rankStrategy', 'rank')
      .where('strategy.id = :id', { id })
      .getOne();
  }

  /**
   * Cập nhật Strategy Agent
   * @param id ID của Strategy Agent
   * @param updateData Dữ liệu cập nhật
   * @returns Số bản ghi đã cập nhật
   */
  @Transactional()
  async updateStrategyAgent(id: string, updateData: Partial<StrategyAgent>): Promise<number> {
    const result = await this.createQueryBuilder()
      .update(StrategyAgent)
      .set({
        ...updateData,
        updatedAt: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
      })
      .where('id = :id', { id })
      .execute();

    return result.affected || 0;
  }
}
