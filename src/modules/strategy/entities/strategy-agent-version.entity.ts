import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { StrategyStatusEnum } from '../constants/strategy-status.enum';

/**
 * Entity đại diện cho bảng strategy_agent_versions trong cơ sở dữ liệu
 * Bảng lưu trữ các phiên bản chính thức của chiến lược agent, do admin quản lý
 */
@Entity('strategy_agent_versions')
export class StrategyAgentVersion {
  /**
   * ID định danh duy nhất cho mỗi phiên bản chiến lư<PERSON>, tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của chiến lược agent mà phiên bản này thuộc về, tham chiếu đến bảng strategy_agents
   */
  @Column({ name: 'strategy_agent_id', type: 'uuid' })
  strategyAgentId: string;

  /**
   * <PERSON><PERSON> thứ tự phiên bản, tă<PERSON> dần theo thời gian, gi<PERSON><PERSON> theo dõi tiến trình phát triển
   */
  @Column({ name: 'version_number' })
  versionNumber: number;

  /**
   * Tên định danh cho phiên bản, dễ nhớ hơn số (ví dụ: "v1.0", "v2.0-beta")
   */
  @Column({ name: 'version_name', length: 255, nullable: true })
  versionName: string;

  /**
   * ID của model sử dụng
   */
  @Column({ name: 'model_id', length: 255, nullable: false })
  modelId: string;

  /**
   * Cấu hình chi tiết cho model
   */
  @Column({ name: 'model_config', type: 'jsonb', nullable: true, default: '{"top_p": 1, "temperature": 1}' })
  modelConfig: any;

  /**
   * Prompt hệ thống định nghĩa cho agent
   */
  @Column({ name: 'system_prompt', type: 'text', nullable: false })
  systemPrompt: string;

  /**
   * Mô tả những thay đổi so với phiên bản trước đó, giúp theo dõi lịch sử phát triển
   */
  @Column({ name: 'change_description', type: 'text', nullable: true })
  changeDescription: string;

  /**
   * ID của nhân viên tạo phiên bản, tham chiếu đến bảng employees
   */
  @Column({ name: 'created_by' })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật gần nhất phiên bản, tham chiếu đến bảng employees
   */
  @Column({ name: 'updated_by' })
  updatedBy: number;

  /**
   * Thời điểm tạo phiên bản, dạng UNIX timestamp với millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối cùng, dạng UNIX timestamp với millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updatedAt: number;

  /**
   * Trạng thái của hàm: DRAFT, PENDING, APPROVED, REJECTED, DELETE
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: StrategyStatusEnum,
    default: StrategyStatusEnum.DRAFT
  })
  status: StrategyStatusEnum;
}
