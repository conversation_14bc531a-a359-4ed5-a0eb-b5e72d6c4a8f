import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '@modules/employee/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@utils/time';

/**
 * DTO cho thông tin nhân viên
 */
export class EmployeeInfoDto {
  /**
   * ID của nhân viên
   */
  employeeId: number;

  /**
   * Tên của nhân viên
   */
  name: string;

  /**
   * Avatar của nhân viên
   */
  avatar: string | null;
}

/**
 * Service cung cấp thông tin nhân viên cho các module khác
 */
@Injectable()
export class EmployeeInfoService {
  private readonly logger = new Logger(EmployeeInfoService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly cdnService: CdnService,
  ) {
  }

  /**
   * <PERSON><PERSON>y thông tin nhân viên theo ID
   * @param employeeId ID của nhân viên
   * @returns Thông tin nhân viên
   */
  async getEmployeeInfo(employeeId: number): Promise<EmployeeInfoDto> {
    try {
      // Tìm nhân viên theo ID, chỉ lấy các trường cần thiết
      const employee = await this.employeeRepository.findById(employeeId, ['id', 'fullName', 'avatar']);

      // Trả về thông tin nhân viên
      return {
        employeeId: employee.id,
        name: employee.fullName,
        avatar: employee.avatar ? this.cdnService.generateUrlView(employee.avatar, TimeIntervalEnum.FIVE_MINUTES) : null,
      };
    } catch (error) {
      this.logger.error(`Error getting employee info: ${error.message}`, error.stack);

      // Nếu là lỗi khác, ném lại lỗi
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi lấy thông tin nhân viên');
    }
  }

  /**
   * Lấy map thông tin nhân viên theo danh sách ID
   * @param employeeIds Danh sách ID của nhân viên
   * @returns Map với key là ID của nhân viên và value là thông tin nhân viên
   */
  async getEmployeeInfoMap(employeeIds: number[]): Promise<Map<number, EmployeeInfoDto>> {
    try {
      // Nếu danh sách rỗng, trả về map rỗng
      if (!employeeIds || employeeIds.length === 0) {
        return new Map<number, EmployeeInfoDto>();
      }

      // Loại bỏ các ID trùng lặp
      const uniqueIds = [...new Set(employeeIds)];

      // Tìm các nhân viên theo danh sách ID
      const employees: any[] = [];
      for (const id of uniqueIds) {
        try {
          const employee = await this.employeeRepository.findById(id, ['id', 'fullName', 'avatar']);
          if (employee) {
            employees.push(employee);
          }
        } catch (error) {
          this.logger.error(`Error getting employee with ID ${id}: ${error.message}`, error.stack);
        }
      }

      // Tạo map với key là ID của nhân viên và value là thông tin nhân viên
      const employeeInfoMap = new Map<number, EmployeeInfoDto>();

      for (const employee of employees) {
        employeeInfoMap.set(employee.id, {
          employeeId: employee.id,
          name: employee.fullName,
          avatar: employee.avatar ? this.cdnService.generateUrlView(employee.avatar, TimeIntervalEnum.FIVE_MINUTES) : null,
        });
      }

      return employeeInfoMap;
    } catch (error) {
      this.logger.error(`Error getting employee info map: ${error.message}`, error.stack);

      // Nếu là lỗi khác, trả về map rỗng
      return new Map<number, EmployeeInfoDto>();
    }
  }
}
