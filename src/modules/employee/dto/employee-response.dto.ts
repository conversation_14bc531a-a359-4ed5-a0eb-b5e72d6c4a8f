import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin nhân viên trong phản hồi
 */
export class EmployeeResponseDto {
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên đầy đủ của nhân viên',
    example: 'Nguyễn Văn A'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email của nhân viên',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại của nhân viên',
    example: '0987654321'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Địa chỉ của nhân viên',
    example: 'Hà Nội'
  })
  address: string;

  @ApiProperty({
    description: 'Thời gian tạo tà<PERSON> (Unix timestamp)',
    example: 1682506092000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thông tin (Unix timestamp)',
    example: 1682506092000,
    required: false
  })
  updatedAt?: number;

  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true
  })
  enable: boolean;

  @ApiProperty({
    description: 'Avatar của nhân viên (đường dẫn đến file ảnh)',
    example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
    required: false
  })
  avatar?: string;
}
