/**
 * <PERSON>ript kiểm tra API lấy URL tạm thời và upload avatar
 * 
 * <PERSON><PERSON><PERSON> sử dụng:
 * 1. <PERSON><PERSON><PERSON> đặt các thư viện cần thiết: npm install axios form-data fs
 * 2. Cập nhật các biến cấu hình bên dưới
 * 3. Chạy script: node test-avatar-upload.js
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Cấu hình
const API_URL = 'http://localhost:3000'; // URL của API
const ACCESS_TOKEN = 'YOUR_ACCESS_TOKEN'; // Token JWT
const EMPLOYEE_ID = 1; // ID của nhân viên cần test
const AVATAR_FILE_PATH = './avatar-test.jpg'; // Đường dẫn đến file avatar test

// Hàm chính
async function testAvatarUpload() {
  try {
    console.log('Bắt đầu kiểm tra API upload avatar...');
    
    // 1. Lấy URL tạm thời để upload avatar
    console.log('1. Lấy URL tạm thời để upload avatar...');
    const uploadUrlResponse = await getAvatarUploadUrl();
    console.log('- Kết quả:', uploadUrlResponse.data);
    
    if (!uploadUrlResponse.data.result || !uploadUrlResponse.data.result.uploadUrl) {
      throw new Error('Không nhận được URL tạm thời');
    }
    
    const { uploadUrl, avatarKey } = uploadUrlResponse.data.result;
    console.log(`- Đã nhận URL tạm thời: ${uploadUrl}`);
    console.log(`- Khóa avatar: ${avatarKey}`);
    
    // 2. Upload avatar sử dụng URL tạm thời
    console.log('\n2. Upload avatar sử dụng URL tạm thời...');
    await uploadAvatar(uploadUrl);
    console.log('- Upload thành công');
    
    // 3. Cập nhật avatar cho nhân viên
    console.log('\n3. Cập nhật avatar cho nhân viên...');
    const updateResponse = await updateEmployeeAvatar(avatarKey);
    console.log('- Kết quả:', updateResponse.data);
    
    console.log('\nKiểm tra hoàn tất!');
  } catch (error) {
    console.error('Lỗi:', error.message);
    if (error.response) {
      console.error('Chi tiết lỗi:', error.response.data);
    }
  }
}

/**
 * Lấy URL tạm thời để upload avatar
 */
async function getAvatarUploadUrl() {
  return axios.post(
    `${API_URL}/employees/${EMPLOYEE_ID}/avatar/upload-url`,
    {
      imageType: 'image/jpeg',
      maxSize: 2097152 // 2MB
    },
    {
      headers: {
        'Authorization': `Bearer ${ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Upload avatar sử dụng URL tạm thời
 */
async function uploadAvatar(uploadUrl) {
  // Đọc file avatar
  const fileContent = fs.readFileSync(AVATAR_FILE_PATH);
  
  // Upload file sử dụng PUT request
  return axios.put(
    uploadUrl,
    fileContent,
    {
      headers: {
        'Content-Type': 'image/jpeg'
      }
    }
  );
}

/**
 * Cập nhật avatar cho nhân viên
 */
async function updateEmployeeAvatar(avatarKey) {
  return axios.put(
    `${API_URL}/employees/${EMPLOYEE_ID}/avatar`,
    {
      avatarKey
    },
    {
      headers: {
        'Authorization': `Bearer ${ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Tạo nhân viên mới với URL tạm thời cho avatar
 */
async function createEmployeeWithAvatar() {
  return axios.post(
    `${API_URL}/employees`,
    {
      fullName: 'Nhân viên Test',
      email: `test.employee.${Date.now()}@example.com`,
      phoneNumber: '0987654321',
      password: 'Password123!',
      address: 'Địa chỉ test',
      avatarImageType: 'image/jpeg',
      avatarMaxSize: 2097152
    },
    {
      headers: {
        'Authorization': `Bearer ${ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

// Chạy test
testAvatarUpload();
