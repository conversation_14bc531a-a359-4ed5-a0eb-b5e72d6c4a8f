// Mock cho typeorm-transactional
import { Transactional } from 'typeorm-transactional';

// Mock decorator Transactional
export function MockTransactional() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      return await originalMethod.apply(this, args);
    };
    return descriptor;
  };
}

// Thay thế decorator Transactional bằng mock
jest.mock('typeorm-transactional', () => ({
  Transactional: function() {
    return function(
      target: any,
      propertyKey: string,
      descriptor: PropertyDescriptor,
    ) {
      const originalMethod = descriptor.value;
      descriptor.value = async function (...args: any[]) {
        return await originalMethod.apply(this, args);
      };
      return descriptor;
    };
  },
  initializeTransactionalContext: jest.fn(),
  patchTypeORMRepositoryWithBaseRepository: jest.fn(),
}));

// Khởi tạo context cho typeorm-transactional
export const initMockTransactionalContext = () => {
  const { initializeTransactionalContext, patchTypeORMRepositoryWithBaseRepository } = require('typeorm-transactional');
  initializeTransactionalContext();
  patchTypeORMRepositoryWithBaseRepository();
};
