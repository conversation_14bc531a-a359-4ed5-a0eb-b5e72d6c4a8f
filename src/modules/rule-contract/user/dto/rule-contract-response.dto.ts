import { ApiProperty } from '@nestjs/swagger';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * DTO cho phản hồi thông tin hợp đồng nguyên tắ<PERSON> (user)
 */
export class RuleContractResponseDto {
  /**
   * ID hợp đồng
   */
  @ApiProperty({
    description: 'ID hợp đồng',
    example: 1,
    type: Number,
  })
  id: number;

  /**
   * Mã hợp đồng
   */
  @ApiProperty({
    description: 'Mã hợp đồng',
    example: 'HD-123',
    type: String,
  })
  contractCode: string;

  /**
   * Trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatusEnum,
    example: ContractStatusEnum.APPROVED,
  })
  status: ContractStatusEnum;

  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hợp đồng',
    enum: ContractTypeEnum,
    example: ContractTypeEnum.INDIVIDUAL,
  })
  type: ContractTypeEnum;

  /**
   * Đường dẫn file hợp đồng
   */
  @ApiProperty({
    description: 'Đường dẫn file hợp đồng',
    example: 'https://example.com/contract.pdf',
    type: String,
  })
  contractUrl: string;

  /**
   * Thời gian tạo hợp đồng
   */
  @ApiProperty({
    description: 'Thời gian tạo hợp đồng (timestamp)',
    example: 1625097600000,
    type: Number,
  })
  createdAt: number;

  /**
   * Thời gian người dùng ký hợp đồng
   */
  @ApiProperty({
    description: 'Thời gian người dùng ký hợp đồng (timestamp)',
    example: 1625097600000,
    type: Number,
  })
  userSignatureAt: number;

  /**
   * Thời gian admin ký hợp đồng
   */
  @ApiProperty({
    description: 'Thời gian admin ký hợp đồng (timestamp)',
    example: 1625097600000,
    type: Number,
  })
  adminSignatureAt: number;
}
