import { Injectable, Logger } from '@nestjs/common';
import { ContractTemplateService, ContractTemplateType } from '@/modules/system-configuration/services/contract-template.service';
import { PdfEditService } from '@/shared/services/pdf/pdf-edit.service';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@/shared/utils/generators/s3-key-generator.util';
import { FileTypeEnum } from '@/shared/utils/file/file-media-type.util';
import { PdfPosition } from '@/shared/interface/pdf-edit.interface';
import { AppException, ErrorCode } from '@/common/exceptions';

/**
 * Service hỗ trợ xử lý hợp đồng
 */
@Injectable()
export class ContractHelperService {
  private readonly logger = new Logger(ContractHelperService.name);

  constructor(
    private readonly contractTemplateService: ContractTemplateService,
    private readonly pdfEditService: PdfEditService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param userId ID của người dùng
   * @param positions Danh sách vị trí cần chỉnh sửa trên PDF
   * @returns Thông tin về file hợp đồng đã tạo
   */
  async createIndividualRuleContract(
    userId: number,
    positions: PdfPosition[],
  ): Promise<{
    contractKey: string;
    contractBuffer: Buffer;
    contractBase64: string;
  }> {
    try {
      this.logger.log(`Tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}`);

      // Lấy mẫu hợp đồng từ service
      const templateBuffer = await this.contractTemplateService.getContractTemplate(
        ContractTemplateType.RULE_CONTRACT_CUSTOMER
      );

      // Chỉnh sửa PDF
      const editedPdfResult = await this.pdfEditService.editPdf(
        templateBuffer,
        positions,
      );

      // Tạo key cho file hợp đồng mới
      const contractKey = generateS3Key({
        baseFolder: 'rule-contracts',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName: `individual-contract-${userId}.pdf`,
        useTimeFolder: true,
      });

      // Upload file hợp đồng đã chỉnh sửa lên S3
      await this.s3Service.uploadBuffer(
        contractKey,
        editedPdfResult.pdfBuffer,
        FileTypeEnum.PDF,
      );

      return {
        contractKey,
        contractBuffer: editedPdfResult.pdfBuffer,
        contractBase64: editedPdfResult.pdfBase64 || '',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân: ${error.message}`,
      );
    }
  }

  /**
   * Tạo hợp đồng nguyên tắc cho doanh nghiệp
   * @param userId ID của người dùng
   * @param positions Danh sách vị trí cần chỉnh sửa trên PDF
   * @returns Thông tin về file hợp đồng đã tạo
   */
  async createBusinessRuleContract(
    userId: number,
    positions: PdfPosition[],
  ): Promise<{
    contractKey: string;
    contractBuffer: Buffer;
    contractBase64: string;
  }> {
    try {
      this.logger.log(`Tạo hợp đồng nguyên tắc cho doanh nghiệp, userId: ${userId}`);

      // Lấy mẫu hợp đồng từ service
      const templateBuffer = await this.contractTemplateService.getContractTemplate(
        ContractTemplateType.RULE_CONTRACT_BUSINESS
      );

      // Chỉnh sửa PDF
      const editedPdfResult = await this.pdfEditService.editPdf(
        templateBuffer,
        positions,
      );

      // Tạo key cho file hợp đồng mới
      const contractKey = generateS3Key({
        baseFolder: 'rule-contracts',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName: `business-contract-${userId}.pdf`,
        useTimeFolder: true,
      });

      // Upload file hợp đồng đã chỉnh sửa lên S3
      await this.s3Service.uploadBuffer(
        contractKey,
        editedPdfResult.pdfBuffer,
        FileTypeEnum.PDF,
      );

      return {
        contractKey,
        contractBuffer: editedPdfResult.pdfBuffer,
        contractBase64: editedPdfResult.pdfBase64 || '',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo hợp đồng nguyên tắc cho doanh nghiệp, userId: ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi tạo hợp đồng nguyên tắc cho doanh nghiệp: ${error.message}`,
      );
    }
  }
}
