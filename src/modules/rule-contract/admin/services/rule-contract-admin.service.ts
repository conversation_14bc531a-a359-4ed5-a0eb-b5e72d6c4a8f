import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { RuleContractRepository } from '../../repositories';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { RuleContractQueryDto, RuleContractResponseDto } from '../dto';
import { RULE_CONTRACT_ERROR_CODES } from '../../errors';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';

/**
 * Service xử lý logic nghiệp vụ cho hợp đồng nguyên tắc (admin)
 */
@Injectable()
export class RuleContractAdminService {
  private readonly logger = new Logger(RuleContractAdminService.name);

  constructor(
    private readonly ruleContractRepository: RuleContractRepository,
    private readonly userRepository: UserRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách hợp đồng nguyên tắc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Transactional()
  async getContracts(
    queryDto: RuleContractQueryDto,
  ): Promise<PaginatedResult<RuleContractResponseDto>> {
    try {
      // Lấy danh sách hợp đồng với phân trang
      const { items, meta } = await this.ruleContractRepository.findWithPaginationForAdmin(
        queryDto,
      );

      // Lấy thông tin người dùng
      const userIds = items.map((item) => item.userId);
      const uniqueUserIds = [...new Set(userIds)].filter(id => id !== null && id !== undefined);
      const users = uniqueUserIds.length > 0 ? await this.userRepository.findByIds(uniqueUserIds) : [];

      // Chuyển đổi dữ liệu sang DTO
      const contractDtos = await Promise.all(items.map(async (contract) => {
        // Tìm thông tin người dùng
        const user = users.find((u: any) => u.id === contract.userId);

        // Tạo URL có chữ ký cho file hợp đồng
        let contractUrl = '';
        if (contract.contractUrlPdf) {
          const generatedUrl = this.cdnService.generateUrlView(
            contract.contractUrlPdf as string,
            TimeIntervalEnum.ONE_HOUR,
          );
          if (generatedUrl) {
            contractUrl = generatedUrl;
          }
        }

        return {
          id: contract.id,
          userId: contract.userId,
          userName: user?.fullName || 'Unknown',
          userEmail: user?.email || 'Unknown',
          contractCode: `HD-${contract.id}`,
          status: contract.status,
          type: contract.type,
          contractUrl,
          createdAt: contract.createdAt,
          userSignatureAt: contract.userSignatureAt,
          adminSignatureAt: contract.adminSignatureAt,
        };
      }));

      return {
        items: contractDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contracts: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách hợp đồng nguyên tắc',
      );
    }
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc
   * @param id ID của hợp đồng
   * @returns Thông tin chi tiết hợp đồng
   */
  @Transactional()
  async getContractById(id: number): Promise<RuleContractResponseDto> {
    try {
      // Lấy thông tin chi tiết hợp đồng
      const contract = await this.ruleContractRepository.findById(id);

      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng nguyên tắc với ID ${id}`,
        );
      }

      // Lấy thông tin người dùng
      const user = contract.userId ? await this.userRepository.findById(contract.userId) : null;

      // Tạo URL có chữ ký cho file hợp đồng
      let contractUrl = '';
      if (contract.contractUrlPdf) {
        const generatedUrl = this.cdnService.generateUrlView(
          contract.contractUrlPdf as string,
          TimeIntervalEnum.ONE_HOUR,
        );
        if (generatedUrl) {
          contractUrl = generatedUrl;
        }
      }

      // Xử lý dữ liệu trả về
      return {
        id: contract.id,
        userId: contract.userId,
        userName: user?.fullName || 'Unknown',
        userEmail: user?.email || 'Unknown',
        contractCode: `HD-${contract.id}`,
        status: contract.status,
        type: contract.type,
        contractUrl,
        createdAt: contract.createdAt,
        userSignatureAt: contract.userSignatureAt,
        adminSignatureAt: contract.adminSignatureAt,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contract: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết hợp đồng nguyên tắc',
      );
    }
  }
}
