import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái log tự động hóa Zalo
 */
export enum ZaloAutomationLogStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách log tự động hóa Zalo
 */
export class ZaloAutomationLogQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo ID của tự động hóa',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsString()
  automationId?: string;

  @ApiProperty({
    description: 'Lọc theo ID của người theo dõi',
    example: '123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  followerId?: string;

  @ApiProperty({
    description: 'Lọc theo loại sự kiện kích hoạt',
    example: 'follow',
    required: false,
  })
  @IsOptional()
  @IsString()
  triggerType?: string;

  @ApiProperty({
    description: 'Lọc theo loại hành động',
    example: 'send_message',
    required: false,
  })
  @IsOptional()
  @IsString()
  actionType?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: ZaloAutomationLogStatus,
    example: ZaloAutomationLogStatus.SUCCESS,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloAutomationLogStatus)
  status?: ZaloAutomationLogStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
