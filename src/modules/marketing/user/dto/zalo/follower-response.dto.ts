import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin người theo dõi
 */
export class FollowerResponseDto {
  @ApiProperty({
    description: 'ID của người theo dõi trong hệ thống',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  userId: string;

  @ApiProperty({
    description: 'Tên hiển thị của người dùng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  displayName?: string;

  @ApiProperty({
    description: 'URL avatar của người dùng',
    example: 'https://zalo.me/avatar/123456789.jpg',
    nullable: true,
  })
  avatarUrl?: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0912345678',
    nullable: true,
  })
  phone?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> tính của người dùng (1: Nam, 2: N<PERSON>)',
    example: 1,
    nullable: true,
  })
  gender?: number;

  @ApiProperty({
    description: 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)',
    example: '01/01/1990',
    nullable: true,
  })
  birthDate?: string;

  @ApiProperty({
    description: 'Các tag gán cho người dùng',
    example: ['vip', 'new-customer'],
    type: [String],
  })
  tags: string[];

  @ApiProperty({
    description: 'Trạng thái (active, unfollowed)',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'Thời điểm theo dõi (Unix timestamp)',
    example: 1625097600000,
  })
  followedAt: number;
}
