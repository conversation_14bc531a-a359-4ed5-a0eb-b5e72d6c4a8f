import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái tin nhắn ZNS
 */
export enum ZnsMessageStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách tin nhắn ZNS
 */
export class ZnsMessageQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo số điện thoại',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Lọc theo ID template',
    example: 'template123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: ZnsMessageStatus,
    example: ZnsMessageStatus.DELIVERED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZnsMessageStatus)
  status?: ZnsMessageStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
