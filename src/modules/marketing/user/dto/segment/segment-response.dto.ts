import { ApiProperty } from '@nestjs/swagger';
import { SegmentCriteriaDto } from './segment-criteria.dto';

/**
 * DTO cho phản hồi thông tin segment
 */
export class SegmentResponseDto {
  /**
   * ID của segment
   * @example 1
   */
  @ApiProperty({
    description: 'ID của segment',
    example: 1,
  })
  id: number;

  /**
   * Tên segment
   * @example "Khách hàng tiềm năng"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng tiềm năng',
  })
  name: string;

  /**
   * Mô tả segment
   * @example "Khách hàng có khả năng mua hàng cao"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: 'Khách hàng có khả năng mua hàng cao',
  })
  description: string;

  /**
   * Điều kiện lọc khách hàng
   */
  @ApiProperty({
    description: 'Đi<PERSON><PERSON> kiện lọc kh<PERSON>ch hàng',
    type: SegmentCriteriaDto,
  })
  criteria: SegmentCriteriaDto;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
