import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleAdsKeyword } from '../entities/google-ads-keyword.entity';

@Injectable()
export class GoogleAdsKeywordRepository {
  constructor(
    @InjectRepository(GoogleAdsKeyword)
    private readonly repository: Repository<GoogleAdsKeyword>,
  ) {}

  /**
   * Tìm từ khóa Google Ads theo ID
   * @param id ID của từ khóa
   * @returns Từ khóa Google Ads
   */
  async findById(id: number): Promise<GoogleAdsKeyword | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm từ khóa Google Ads theo ID và ID người dùng
   * @param id ID của từ khóa
   * @param userId ID của người dùng
   * @returns Từ khóa Google Ads
   */
  async findByIdAndUserId(id: number, userId: number): Promise<GoogleAdsKeyword | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm từ khóa Google Ads theo ID từ khóa trên Google Ads
   * @param keywordId ID của từ khóa trên Google Ads
   * @param userId ID của người dùng
   * @returns Từ khóa Google Ads
   */
  async findByKeywordId(keywordId: string, userId: number): Promise<GoogleAdsKeyword | null> {
    return this.repository.findOne({ where: { keywordId, userId } });
  }

  /**
   * Lấy danh sách từ khóa Google Ads của nhóm quảng cáo
   * @param adGroupId ID của nhóm quảng cáo
   * @param userId ID của người dùng
   * @returns Danh sách từ khóa Google Ads
   */
  async findByAdGroupId(adGroupId: number, userId: number): Promise<GoogleAdsKeyword[]> {
    return this.repository.find({
      where: { adGroupId, userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tạo từ khóa Google Ads mới
   * @param data Dữ liệu từ khóa
   * @returns Từ khóa Google Ads đã tạo
   */
  async create(data: Partial<GoogleAdsKeyword>): Promise<GoogleAdsKeyword> {
    const keyword = this.repository.create(data);
    return this.repository.save(keyword);
  }

  /**
   * Tạo nhiều từ khóa Google Ads
   * @param dataList Danh sách dữ liệu từ khóa
   * @returns Danh sách từ khóa Google Ads đã tạo
   */
  async createMany(dataList: Partial<GoogleAdsKeyword>[]): Promise<GoogleAdsKeyword[]> {
    const keywords = dataList.map(data => this.repository.create(data));
    return this.repository.save(keywords);
  }

  /**
   * Cập nhật từ khóa Google Ads
   * @param id ID của từ khóa
   * @param data Dữ liệu cập nhật
   * @returns true nếu cập nhật thành công
   */
  async update(id: number, data: Partial<GoogleAdsKeyword>): Promise<boolean> {
    const result = await this.repository.update(id, data);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa từ khóa Google Ads
   * @param id ID của từ khóa
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
