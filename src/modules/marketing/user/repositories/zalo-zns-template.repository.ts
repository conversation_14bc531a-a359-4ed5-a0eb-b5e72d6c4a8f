import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloZnsTemplate } from '../entities/zalo-zns-template.entity';

/**
 * Repository cho ZaloZnsTemplate
 */
@Injectable()
export class ZaloZnsTemplateRepository {
  constructor(
    @InjectRepository(ZaloZnsTemplate)
    private readonly repository: Repository<ZaloZnsTemplate>,
  ) {}

  /**
   * Tìm kiếm nhiều template ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách template ZNS
   */
  async find(
    options?: FindManyOptions<ZaloZnsTemplate>,
  ): Promise<ZaloZnsTemplate[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một template ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Template ZNS hoặc null
   */
  async findOne(
    options?: FindOneOptions<ZaloZnsTemplate>,
  ): Promise<ZaloZnsTemplate | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm template ZNS theo ID
   * @param id ID của template ZNS
   * @returns Template ZNS hoặc null
   */
  async findById(id: number): Promise<ZaloZnsTemplate | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm template ZNS theo ID Official Account và ID template
   * @param oaId ID của Official Account
   * @param templateId ID của template trên Zalo
   * @returns Template ZNS hoặc null
   */
  async findByOaIdAndTemplateId(
    oaId: string,
    templateId: string,
  ): Promise<ZaloZnsTemplate | null> {
    return this.repository.findOne({ where: { oaId, templateId } });
  }

  /**
   * Tìm tất cả template ZNS của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách template ZNS
   */
  async findByUserId(userId: number): Promise<ZaloZnsTemplate[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tìm tất cả template ZNS của một Official Account
   * @param oaId ID của Official Account
   * @returns Danh sách template ZNS
   */
  async findByOaId(oaId: string): Promise<ZaloZnsTemplate[]> {
    return this.repository.find({ where: { oaId } });
  }

  /**
   * Tạo mới template ZNS
   * @param data Dữ liệu template ZNS
   * @returns Template ZNS đã tạo
   */
  async create(data: Partial<ZaloZnsTemplate>): Promise<ZaloZnsTemplate> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template ZNS
   * @param id ID của template ZNS
   * @param data Dữ liệu cập nhật
   * @returns Template ZNS đã cập nhật
   */
  async update(
    id: number,
    data: Partial<ZaloZnsTemplate>,
  ): Promise<ZaloZnsTemplate | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Cập nhật template ZNS theo ID Official Account và ID template
   * @param oaId ID của Official Account
   * @param templateId ID của template trên Zalo
   * @param data Dữ liệu cập nhật
   * @returns Template ZNS đã cập nhật
   */
  async updateByOaIdAndTemplateId(
    oaId: string,
    templateId: string,
    data: Partial<ZaloZnsTemplate>,
  ): Promise<ZaloZnsTemplate | null> {
    await this.repository.update({ oaId, templateId }, data);
    return this.findByOaIdAndTemplateId(oaId, templateId);
  }

  /**
   * Xóa template ZNS
   * @param id ID của template ZNS
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa template ZNS theo ID Official Account và ID template
   * @param oaId ID của Official Account
   * @param templateId ID của template trên Zalo
   * @returns true nếu xóa thành công
   */
  async deleteByOaIdAndTemplateId(
    oaId: string,
    templateId: string,
  ): Promise<boolean> {
    const result = await this.repository.delete({ oaId, templateId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Đếm số lượng template ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng template ZNS
   */
  async count(options?: FindManyOptions<ZaloZnsTemplate>): Promise<number> {
    return this.repository.count(options);
  }
}
