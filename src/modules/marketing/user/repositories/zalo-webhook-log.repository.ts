import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloWebhookLog } from '../entities/zalo-webhook-log.entity';

/**
 * Repository cho ZaloWebhookLog
 */
@Injectable()
export class ZaloWebhookLogRepository {
  constructor(
    @InjectRepository(ZaloWebhookLog)
    private readonly repository: Repository<ZaloWebhookLog>,
  ) {}

  /**
   * Tìm kiếm nhiều log webhook
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách log webhook
   */
  async find(options?: FindManyOptions<ZaloWebhookLog>): Promise<ZaloWebhookLog[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một log webhook
   * @param options Tùy chọn tìm kiếm
   * @returns Log webhook hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloWebhookLog>): Promise<ZaloWebhookLog | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm log webhook theo ID
   * @param id ID của log webhook
   * @returns Log webhook hoặc null
   */
  async findById(id: number): Promise<ZaloWebhookLog | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm log webhook theo ID sự kiện
   * @param eventId ID của sự kiện
   * @returns Log webhook hoặc null
   */
  async findByEventId(eventId: string): Promise<ZaloWebhookLog | null> {
    return this.repository.findOne({ where: { eventId } });
  }

  /**
   * Tìm tất cả log webhook của một Official Account
   * @param oaId ID của Official Account
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách log webhook
   */
  async findByOaId(oaId: string, options?: FindManyOptions<ZaloWebhookLog>): Promise<ZaloWebhookLog[]> {
    const findOptions: FindManyOptions<ZaloWebhookLog> = {
      where: { oaId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả log webhook theo tên sự kiện
   * @param eventName Tên sự kiện
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách log webhook
   */
  async findByEventName(eventName: string, options?: FindManyOptions<ZaloWebhookLog>): Promise<ZaloWebhookLog[]> {
    const findOptions: FindManyOptions<ZaloWebhookLog> = {
      where: { eventName },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả log webhook theo trạng thái xử lý
   * @param processed Trạng thái xử lý
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách log webhook
   */
  async findByProcessed(processed: boolean, options?: FindManyOptions<ZaloWebhookLog>): Promise<ZaloWebhookLog[]> {
    const findOptions: FindManyOptions<ZaloWebhookLog> = {
      where: { processed },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tạo mới log webhook
   * @param data Dữ liệu log webhook
   * @returns Log webhook đã tạo
   */
  async create(data: Partial<ZaloWebhookLog>): Promise<ZaloWebhookLog> {
    const webhookLog = this.repository.create(data);
    return this.repository.save(webhookLog);
  }

  /**
   * Tạo nhiều log webhook
   * @param dataArray Mảng dữ liệu log webhook
   * @returns Danh sách log webhook đã tạo
   */
  async createMany(dataArray: Partial<ZaloWebhookLog>[]): Promise<ZaloWebhookLog[]> {
    const webhookLogs = this.repository.create(dataArray);
    return this.repository.save(webhookLogs);
  }

  /**
   * Cập nhật log webhook
   * @param id ID của log webhook
   * @param data Dữ liệu cập nhật
   * @returns Log webhook đã cập nhật
   */
  async update(id: number, data: Partial<ZaloWebhookLog>): Promise<ZaloWebhookLog | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Đánh dấu log webhook đã xử lý
   * @param id ID của log webhook
   * @returns Log webhook đã cập nhật
   */
  async markAsProcessed(id: number): Promise<ZaloWebhookLog | null> {
    return this.update(id, { processed: true });
  }

  /**
   * Xóa log webhook
   * @param id ID của log webhook
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng log webhook
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng log webhook
   */
  async count(options?: FindManyOptions<ZaloWebhookLog>): Promise<number> {
    return this.repository.count(options);
  }
}
