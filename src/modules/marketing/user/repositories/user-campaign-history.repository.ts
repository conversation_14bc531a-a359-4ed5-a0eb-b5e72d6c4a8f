import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { UserCampaignHistory } from '../entities/user-campaign-history.entity';

/**
 * Repository cho UserCampaignHistory
 */
@Injectable()
export class UserCampaignHistoryRepository {
  constructor(
    @InjectRepository(UserCampaignHistory)
    private readonly repository: Repository<UserCampaignHistory>,
  ) {}

  /**
   * Tìm kiếm nhiều lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách lịch sử campaign
   */
  async find(options?: FindManyOptions<UserCampaignHistory>): Promise<UserCampaignHistory[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Lịch sử campaign hoặc null
   */
  async findOne(options?: FindOneOptions<UserCampaignHistory>): Promise<UserCampaignHistory | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng lịch sử campaign
   */
  async count(options?: FindManyOptions<UserCampaignHistory>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu lịch sử campaign
   * @param history Lịch sử campaign cần lưu
   * @returns Lịch sử campaign đã lưu
   */
  async save(history: UserCampaignHistory): Promise<UserCampaignHistory>;
  async save(history: UserCampaignHistory[]): Promise<UserCampaignHistory[]>;
  async save(history: UserCampaignHistory | UserCampaignHistory[]): Promise<UserCampaignHistory | UserCampaignHistory[]> {
    return this.repository.save(history as any);
  }

  /**
   * Xóa lịch sử campaign
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<UserCampaignHistory>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa lịch sử campaign
   * @param history Lịch sử campaign cần xóa
   * @returns Lịch sử campaign đã xóa
   */
  async remove(history: UserCampaignHistory): Promise<UserCampaignHistory>;
  async remove(history: UserCampaignHistory[]): Promise<UserCampaignHistory[]>;
  async remove(history: UserCampaignHistory | UserCampaignHistory[]): Promise<UserCampaignHistory | UserCampaignHistory[]> {
    return this.repository.remove(history as any);
  }
}
