import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOptionsWhere, ILike, Repository } from 'typeorm';
import { UserTemplateEmail } from '../entities/user-template-email.entity';
import { PaginatedResult } from '@/common/response';
import { TemplateEmailQueryDto } from '../dto/template-email';

/**
 * Repository cho UserTemplateEmail
 */
@Injectable()
export class UserTemplateEmailRepository {
  constructor(
    @InjectRepository(UserTemplateEmail)
    private readonly repository: Repository<UserTemplateEmail>,
  ) {}

  /**
   * Tìm kiếm nhiều template email với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template email với phân trang
   */
  async findWithPagination(
    userId: number,
    queryDto: TemplateEmailQueryDto,
  ): Promise<PaginatedResult<UserTemplateEmail>> {
    const { page, limit, search, sortBy, sortDirection, name, tag } = queryDto;
    const skip = (page - 1) * limit;

    // Xây dựng điều kiện tìm kiếm
    const where: FindOptionsWhere<UserTemplateEmail> = { userId };

    if (search) {
      where.name = ILike(`%${search}%`);
    }

    if (name) {
      where.name = ILike(`%${name}%`);
    }

    // Xây dựng options tìm kiếm
    const options: FindManyOptions<UserTemplateEmail> = {
      where,
      skip,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    };

    // Nếu có tag, cần xử lý đặc biệt vì tags là một mảng trong JSONB
    if (tag) {
      options.where = [
        {
          ...where,
          tags: () => `tags @> '["${tag}"]'`,
        },
      ];
    }

    // Thực hiện truy vấn
    const [items, totalItems] = await this.repository.findAndCount(options);

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm template email theo ID
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns Template email
   */
  async findById(id: number, userId: number): Promise<UserTemplateEmail> {
    const template = await this.repository.findOne({
      where: { id, userId },
    });

    if (!template) {
      throw new NotFoundException(`Template email với ID ${id} không tồn tại hoặc không thuộc về người dùng này`);
    }

    return template;
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @returns Template email đã tạo
   */
  async create(data: Partial<UserTemplateEmail>): Promise<UserTemplateEmail> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @param data Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, userId: number, data: Partial<UserTemplateEmail>): Promise<UserTemplateEmail> {
    // Kiểm tra template tồn tại và thuộc về người dùng
    await this.findById(id, userId);

    // Cập nhật template
    await this.repository.update({ id, userId }, data);

    // Trả về template đã cập nhật
    return this.findById(id, userId);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns true nếu xóa thành công
   */
  async delete(id: number, userId: number): Promise<boolean> {
    // Kiểm tra template tồn tại và thuộc về người dùng
    await this.findById(id, userId);

    // Xóa template
    const result = await this.repository.delete({ id, userId });

    return result && result?.affected !== null && result?.affected !== undefined && result?.affected > 0;
  }
}
