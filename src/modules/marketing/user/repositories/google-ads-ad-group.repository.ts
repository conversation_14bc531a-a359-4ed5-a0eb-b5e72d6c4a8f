import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleAdsAdGroup } from '../entities/google-ads-ad-group.entity';

@Injectable()
export class GoogleAdsAdGroupRepository {
  constructor(
    @InjectRepository(GoogleAdsAdGroup)
    private readonly repository: Repository<GoogleAdsAdGroup>,
  ) {}

  /**
   * Tìm nhóm quảng cáo Google Ads theo ID
   * @param id ID của nhóm quảng cáo
   * @returns Nhóm quảng cáo Google Ads
   */
  async findById(id: number): Promise<GoogleAdsAdGroup | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm nhóm quảng cáo Google Ads theo ID và ID người dùng
   * @param id ID của nhóm quảng cáo
   * @param userId ID của người dùng
   * @returns Nhóm quảng cáo Google Ads
   */
  async findByIdAndUserId(id: number, userId: number): Promise<GoogleAdsAdGroup | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm nhóm quảng cáo Google Ads theo ID nhóm quảng cáo trên Google Ads
   * @param adGroupId ID của nhóm quảng cáo trên Google Ads
   * @param userId ID của người dùng
   * @returns Nhóm quảng cáo Google Ads
   */
  async findByAdGroupId(adGroupId: string, userId: number): Promise<GoogleAdsAdGroup | null> {
    return this.repository.findOne({ where: { adGroupId, userId } });
  }

  /**
   * Lấy danh sách nhóm quảng cáo Google Ads của chiến dịch
   * @param campaignId ID của chiến dịch
   * @param userId ID của người dùng
   * @returns Danh sách nhóm quảng cáo Google Ads
   */
  async findByCampaignId(campaignId: number, userId: number): Promise<GoogleAdsAdGroup[]> {
    return this.repository.find({
      where: { campaignId, userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tạo nhóm quảng cáo Google Ads mới
   * @param data Dữ liệu nhóm quảng cáo
   * @returns Nhóm quảng cáo Google Ads đã tạo
   */
  async create(data: Partial<GoogleAdsAdGroup>): Promise<GoogleAdsAdGroup> {
    const adGroup = this.repository.create(data);
    return this.repository.save(adGroup);
  }

  /**
   * Cập nhật nhóm quảng cáo Google Ads
   * @param id ID của nhóm quảng cáo
   * @param data Dữ liệu cập nhật
   * @returns true nếu cập nhật thành công
   */
  async update(id: number, data: Partial<GoogleAdsAdGroup>): Promise<boolean> {
    const result = await this.repository.update(id, data);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa nhóm quảng cáo Google Ads
   * @param id ID của nhóm quảng cáo
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
