import { Injectable, Logger } from '@nestjs/common';
import { Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserSegmentService } from './user-segment.service';
import {
  MarketingStatisticsQueryDto,
  StatisticsType,
  MarketingOverviewStatisticsDto,
  AudienceGrowthStatisticsDto,
  GrowthDataPointDto,
  CampaignPerformanceStatisticsDto,
  CampaignPerformanceDto,
  SegmentDistributionStatisticsDto,
  SegmentDistributionDto
} from '../dto/statistics';
import { SendStatus } from '../dto/campaign';
import { UserCampaign } from '../entities';

/**
 * Service xử lý logic thống kê marketing
 */
@Injectable()
export class UserMarketingStatisticsService {
  private readonly logger = new Logger(UserMarketingStatisticsService.name);

  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userCampaignRepository: UserCampaignRepository,
    private readonly userTagRepository: UserTagRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
    private readonly userSegmentService: UserSegmentService,
  ) {}

  /**
   * Lấy thống kê tổng quan về marketing
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Thống kê tổng quan
   */
  async getOverviewStatistics(
    userId: number,
    query: MarketingStatisticsQueryDto,
  ): Promise<MarketingOverviewStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const startDate = query.startDate || now - 30 * 24 * 60 * 60; // Mặc định 30 ngày trước
      const endDate = query.endDate || now;

      // Tổng số audience
      const totalAudiences = await this.userAudienceRepository.count({
        where: { userId },
      });

      // Tổng số segment
      const totalSegments = await this.userSegmentRepository.count({
        where: { userId },
      });

      // Tổng số campaign
      const totalCampaigns = await this.userCampaignRepository.count({
        where: { userId },
      });

      // Tổng số tag
      const totalTags = await this.userTagRepository.count({
        where: { userId },
      });

      // Số lượng audience mới trong khoảng thời gian
      const newAudiences = await this.userAudienceRepository.count({
        where: {
          userId,
          createdAt: Between(startDate, endDate),
        },
      });

      // Lấy danh sách campaign đã chạy trong khoảng thời gian
      const activeCampaigns = await this.userCampaignRepository.count({
        where: {
          userId,
          scheduledAt: Between(startDate, endDate),
        },
      });

      // Tính tỷ lệ mở và click trung bình
      const campaigns = await this.userCampaignRepository.find({
        where: { userId },
      });

      let totalOpenRate = 0;
      let totalClickRate = 0;
      let campaignsWithStats = 0;

      for (const campaign of campaigns) {
        const history = await this.userCampaignHistoryRepository.find({
          where: { campaignId: campaign.id },
        });

        if (history.length > 0) {
          const sent = history.filter(h =>
            h.status === SendStatus.SENT ||
            h.status === SendStatus.DELIVERED ||
            h.status === SendStatus.OPENED ||
            h.status === SendStatus.CLICKED
          ).length;

          const opened = history.filter(h =>
            h.status === SendStatus.OPENED ||
            h.status === SendStatus.CLICKED
          ).length;

          const clicked = history.filter(h =>
            h.status === SendStatus.CLICKED
          ).length;

          if (sent > 0) {
            totalOpenRate += (opened / sent) * 100;
            totalClickRate += (clicked / sent) * 100;
            campaignsWithStats++;
          }
        }
      }

      const averageOpenRate = campaignsWithStats > 0 ? totalOpenRate / campaignsWithStats : 0;
      const averageClickRate = campaignsWithStats > 0 ? totalClickRate / campaignsWithStats : 0;

      return {
        totalAudiences,
        totalSegments,
        totalCampaigns,
        totalTags,
        newAudiences,
        activeCampaigns,
        averageOpenRate,
        averageClickRate,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(`Error getting overview statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê tăng trưởng audience
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Thống kê tăng trưởng audience
   */
  async getAudienceGrowthStatistics(
    userId: number,
    query: MarketingStatisticsQueryDto,
  ): Promise<AudienceGrowthStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const startDate = query.startDate || now - 30 * 24 * 60 * 60; // Mặc định 30 ngày trước
      const endDate = query.endDate || now;
      const type = query.type || StatisticsType.MONTHLY;

      // Lấy tất cả audience trong khoảng thời gian
      const audiences = await this.userAudienceRepository.find({
        where: {
          userId,
          createdAt: Between(startDate, endDate),
        },
        order: {
          createdAt: 'ASC',
        },
      });

      // Tổng số audience mới
      const totalNewAudiences = audiences.length;

      // Tính tỷ lệ tăng trưởng
      const previousStartDate = startDate - (endDate - startDate);
      const previousEndDate = startDate - 1;

      const previousAudiencesCount = await this.userAudienceRepository.count({
        where: {
          userId,
          createdAt: Between(previousStartDate, previousEndDate),
        },
      });

      const growthRate = previousAudiencesCount > 0
        ? ((totalNewAudiences - previousAudiencesCount) / previousAudiencesCount) * 100
        : totalNewAudiences > 0 ? 100 : 0;

      // Tạo dữ liệu cho biểu đồ
      const audienceGrowth: GrowthDataPointDto[] = [];

      // Nhóm dữ liệu theo loại thống kê
      const groupedData = new Map<string, number>();

      for (const audience of audiences) {
        const date = new Date(audience.createdAt * 1000);
        let label: string;

        switch (type) {
          case StatisticsType.DAILY:
            label = date.toISOString().split('T')[0]; // YYYY-MM-DD
            break;
          case StatisticsType.WEEKLY:
            const weekNumber = Math.ceil((date.getDate() + new Date(date.getFullYear(), date.getMonth(), 1).getDay()) / 7);
            label = `${date.getFullYear()}-W${weekNumber}`;
            break;
          case StatisticsType.MONTHLY:
            label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case StatisticsType.YEARLY:
            label = `${date.getFullYear()}`;
            break;
          default:
            label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        }

        if (groupedData.has(label)) {
          groupedData.set(label, groupedData.get(label)! + 1);
        } else {
          groupedData.set(label, 1);
        }
      }

      // Chuyển đổi dữ liệu nhóm thành mảng
      for (const [label, value] of groupedData.entries()) {
        let timestamp: number;

        switch (type) {
          case StatisticsType.DAILY:
            timestamp = new Date(label).getTime() / 1000;
            break;
          case StatisticsType.WEEKLY:
            const [year, week] = label.split('-W');
            const firstDayOfYear = new Date(parseInt(year), 0, 1);
            timestamp = new Date(firstDayOfYear.getTime() + (parseInt(week) - 1) * 7 * 24 * 60 * 60 * 1000).getTime() / 1000;
            break;
          case StatisticsType.MONTHLY:
            const [yearMonth, month] = label.split('-');
            timestamp = new Date(parseInt(yearMonth), parseInt(month) - 1, 1).getTime() / 1000;
            break;
          case StatisticsType.YEARLY:
            timestamp = new Date(parseInt(label), 0, 1).getTime() / 1000;
            break;
          default:
            const [yearDefault, monthDefault] = label.split('-');
            timestamp = new Date(parseInt(yearDefault), parseInt(monthDefault) - 1, 1).getTime() / 1000;
        }

        audienceGrowth.push({
          label,
          timestamp,
          value,
        });
      }

      // Sắp xếp theo thời gian
      audienceGrowth.sort((a, b) => a.timestamp - b.timestamp);

      return {
        audienceGrowth,
        totalNewAudiences,
        growthRate,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(`Error getting audience growth statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê hiệu suất campaign
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Thống kê hiệu suất campaign
   */
  async getCampaignPerformanceStatistics(
    userId: number,
    query: MarketingStatisticsQueryDto,
  ): Promise<CampaignPerformanceStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const startDate = query.startDate || now - 30 * 24 * 60 * 60; // Mặc định 30 ngày trước
      const endDate = query.endDate || now;

      // Lấy danh sách campaign đã chạy trong khoảng thời gian
      const campaigns = await this.userCampaignRepository.find({
        where: {
          userId,
          scheduledAt: Between(startDate, endDate),
        },
        order: {
          scheduledAt: 'DESC',
        },
      });

      const campaignPerformances: CampaignPerformanceDto[] = [];
      let totalOpenRate = 0;
      let totalClickRate = 0;
      let campaignsWithStats = 0;

      for (const campaign of campaigns) {
        const history = await this.userCampaignHistoryRepository.find({
          where: { campaignId: campaign.id },
        });

        const totalRecipients = history.length;
        const sent = history.filter(h =>
          h.status === SendStatus.SENT ||
          h.status === SendStatus.DELIVERED ||
          h.status === SendStatus.OPENED ||
          h.status === SendStatus.CLICKED
        ).length;

        const delivered = history.filter(h =>
          h.status === SendStatus.DELIVERED ||
          h.status === SendStatus.OPENED ||
          h.status === SendStatus.CLICKED
        ).length;

        const opened = history.filter(h =>
          h.status === SendStatus.OPENED ||
          h.status === SendStatus.CLICKED
        ).length;

        const clicked = history.filter(h =>
          h.status === SendStatus.CLICKED
        ).length;

        const openRate = sent > 0 ? (opened / sent) * 100 : 0;
        const clickRate = sent > 0 ? (clicked / sent) * 100 : 0;

        if (sent > 0) {
          totalOpenRate += openRate;
          totalClickRate += clickRate;
          campaignsWithStats++;
        }

        campaignPerformances.push({
          id: campaign.id,
          name: campaign.title,
          totalRecipients,
          sent,
          delivered,
          opened,
          clicked,
          openRate,
          clickRate,
          runAt: campaign.scheduledAt || 0,
        });
      }

      const averageOpenRate = campaignsWithStats > 0 ? totalOpenRate / campaignsWithStats : 0;
      const averageClickRate = campaignsWithStats > 0 ? totalClickRate / campaignsWithStats : 0;

      return {
        campaigns: campaignPerformances,
        averageOpenRate,
        averageClickRate,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(`Error getting campaign performance statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê phân phối segment
   * @param userId ID của người dùng
   * @returns Thống kê phân phối segment
   */
  async getSegmentDistributionStatistics(
    userId: number,
  ): Promise<SegmentDistributionStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Lấy tổng số audience
      const totalAudiences = await this.userAudienceRepository.count({
        where: { userId },
      });

      // Lấy danh sách segment
      const segments = await this.userSegmentRepository.find({
        where: { userId },
      });

      const segmentDistributions: SegmentDistributionDto[] = [];
      let totalAssignedAudiences = 0;

      for (const segment of segments) {
        // Lấy số lượng audience trong segment
        // Đây là một phương thức giả định, cần thay thế bằng logic thực tế
        const audiencesInSegment = await this.getAudiencesInSegment(userId, segment);
        const count = audiencesInSegment.length;
        totalAssignedAudiences += count;

        const percentage = totalAudiences > 0 ? (count / totalAudiences) * 100 : 0;

        segmentDistributions.push({
          id: segment.id,
          name: segment.name,
          count,
          percentage,
        });
      }

      // Sắp xếp theo số lượng giảm dần
      segmentDistributions.sort((a, b) => b.count - a.count);

      // Số lượng audience không thuộc segment nào
      const unassignedAudiences = totalAudiences - totalAssignedAudiences;

      return {
        segments: segmentDistributions,
        totalAudiences,
        unassignedAudiences,
        updatedAt: now,
      };
    } catch (error) {
      this.logger.error(`Error getting segment distribution statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách audience trong segment
   * @param userId ID của người dùng
   * @param segment Segment cần lấy audience
   * @returns Danh sách audience
   */
  private async getAudiencesInSegment(userId: number, segment: any): Promise<any[]> {
    // Sử dụng phương thức từ UserSegmentService để lấy danh sách audience trong segment
    return this.userSegmentService.getAudiencesInSegment(userId, segment);
  }
}
