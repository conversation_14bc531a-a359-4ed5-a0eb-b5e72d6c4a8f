import { Injectable, NotFoundException } from '@nestjs/common';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { In, Like, FindOptionsWhere } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, CustomFieldResponseDto, AudienceQueryDto, AudienceSortField, SortOrder } from '../dto/audience';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { UserAudience, UserAudienceCustomField, UserTag } from '../entities';

/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class UserAudienceService {
  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userAudienceCustomFieldRepository: UserAudienceCustomFieldRepository,
    private readonly userTagRepository: UserTagRepository,
  ) {}

  /**
   * Tạo audience mới
   * @param userId ID của người dùng
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Thông tin audience đã tạo
   */
  @Transactional()
  async create(userId: number, createAudienceDto: CreateAudienceDto): Promise<AudienceResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Tạo audience
    const audience = new UserAudience();
    audience.userId = userId;
    audience.email = createAudienceDto.email;
    audience.phone = createAudienceDto.phone || '';
    audience.createdAt = now;
    audience.updatedAt = now;

    const savedAudience = await this.userAudienceRepository.save(audience);

    // Tạo các trường tùy chỉnh
    const customFields: UserAudienceCustomField[] = [];
    if (createAudienceDto.customFields && createAudienceDto.customFields.length > 0) {
      for (const fieldDto of createAudienceDto.customFields) {
        const customField = new UserAudienceCustomField();
        customField.audienceId = (savedAudience as UserAudience).id;
        customField.fieldName = fieldDto.fieldName;
        customField.fieldValue = fieldDto.fieldValue;
        customField.fieldType = fieldDto.fieldType;
        customField.createdAt = now;
        customField.updatedAt = now;
        customFields.push(customField);
      }

      await this.userAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy thông tin tags nếu có
    let tags: UserTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.userTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
          userId,
        },
      });
    }

    // Đảm bảo savedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(savedAudience as UserAudience, customFields, tags);
  }

  /**
   * Cập nhật audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Thông tin audience đã cập nhật
   */
  @Transactional()
  async update(userId: number, id: number, updateAudienceDto: UpdateAudienceDto): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    const now = Math.floor(Date.now() / 1000);

    // Cập nhật thông tin audience
    if (updateAudienceDto.email !== undefined) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.phone !== undefined) {
      audience.phone = updateAudienceDto.phone;
    }

    audience.updatedAt = now;
    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh
    let customFields: UserAudienceCustomField[] = [];
    if (updateAudienceDto.customFields !== undefined) {
      // Xóa các trường tùy chỉnh hiện tại
      await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      if (updateAudienceDto.customFields.length > 0) {
        for (const fieldDto of updateAudienceDto.customFields) {
          const customField = new UserAudienceCustomField();
          customField.audienceId = id;
          customField.fieldName = fieldDto.fieldName;
          customField.fieldValue = fieldDto.fieldValue;
          customField.fieldType = fieldDto.fieldType;
          customField.createdAt = now;
          customField.updatedAt = now;
          customFields.push(customField);
        }

        const savedCustomFields = await this.userAudienceCustomFieldRepository.save(customFields);
        customFields = Array.isArray(savedCustomFields) ? savedCustomFields : [savedCustomFields];
      }
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.userAudienceCustomFieldRepository.find({ where: { audienceId: id } });
    }

    // Lấy thông tin tags
    let tags: UserTag[] = [];
    if (updateAudienceDto.tagIds !== undefined) {
      if (updateAudienceDto.tagIds.length > 0) {
        tags = await this.userTagRepository.find({
          where: {
            id: In(updateAudienceDto.tagIds),
            userId,
          },
        });
      }
    } else {
      // TODO: Nếu có bảng liên kết audience-tag, cần lấy tags hiện tại
    }

    // Đảm bảo updatedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(updatedAudience as UserAudience, customFields, tags);
  }

  /**
   * Xóa audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<boolean> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Xóa các trường tùy chỉnh
    await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa audience
    await this.userAudienceRepository.remove(audience);
    return true;
  }

  /**
   * Lấy danh sách audience của người dùng với phân trang và filter
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(userId: number, query: AudienceQueryDto): Promise<PaginatedResponseDto<AudienceResponseDto>> {
    const { page = 1, limit = 10, email, phone, tagId, customFieldName, customFieldValue, sortBy = AudienceSortField.CREATED_AT, sortOrder = SortOrder.DESC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<UserAudience> = { userId };

    // Thêm điều kiện tìm kiếm theo email
    if (email) {
      where.email = Like(`%${email}%`);
    }

    // Thêm điều kiện tìm kiếm theo số điện thoại
    if (phone) {
      where.phone = Like(`%${phone}%`);
    }

    // Đếm tổng số audience
    const total = await this.userAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.userAudienceRepository.find({
      where,
      order: { [sortBy]: sortOrder },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map(a => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    let customFieldsQuery: FindOptionsWhere<UserAudienceCustomField> = { audienceId: In(audienceIds) };

    // Thêm điều kiện tìm kiếm theo tên trường tùy chỉnh
    if (customFieldName) {
      customFieldsQuery.fieldName = Like(`%${customFieldName}%`);
    }

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tags nếu có tagId
    let tags: UserTag[] = [];
    if (tagId) {
      tags = await this.userTagRepository.find({
        where: { id: tagId, userId },
      });
    }

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields = customFields.filter(cf => cf.audienceId === audience.id);
      const audienceTags = tagId ? tags : [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy thông tin audience theo ID
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns Thông tin audience
   */
  async findOne(userId: number, id: number): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    const customFields = await this.userAudienceCustomFieldRepository.find({ where: { audienceId: id } });
    // TODO: Nếu có bảng liên kết audience-tag, cần lấy tags

    return this.mapToDto(audience, customFields, []);
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param audience Entity audience
   * @param customFields Danh sách các trường tùy chỉnh
   * @param tags Danh sách các tag
   * @returns DTO audience
   */
  private mapToDto(
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
    tags: UserTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();
    dto.id = audience.id;
    dto.email = audience.email;
    dto.phone = audience.phone;
    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map(field => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldName = field.fieldName;
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.fieldType = field.fieldType as any;
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map(tag => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }
}
