import { Test, TestingModule } from '@nestjs/testing';
import { UserTagService } from '../services/user-tag.service';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { NotFoundException } from '@nestjs/common';
import { CreateTagDto, UpdateTagDto } from '../dto/tag';
// Các entity được sử dụng trong các mock

describe('UserTagService', () => {
  let service: UserTagService;
  let repository: UserTagRepository;

  const mockUserTagRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserTagService,
        {
          provide: UserTagRepository,
          useValue: mockUserTagRepository,
        },
      ],
    }).compile();

    service = module.get<UserTagService>(UserTagService);
    repository = module.get<UserTagRepository>(UserTagRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new tag', async () => {
      // Arrange
      const userId = 1;
      const createTagDto: CreateTagDto = {
        name: 'Test Tag',
        color: '#FF5733',
      };

      const savedTag = {
        id: 1,
        userId,
        name: createTagDto.name,
        color: createTagDto.color,
        createdAt: **********,
        updatedAt: **********,
      };

      mockUserTagRepository.save.mockResolvedValue(savedTag);

      // Act
      const result = await service.create(userId, createTagDto);

      // Assert
      expect(mockUserTagRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: savedTag.id,
        name: savedTag.name,
        color: savedTag.color,
        createdAt: savedTag.createdAt,
        updatedAt: savedTag.updatedAt,
      });
    });
  });

  describe('findAll', () => {
    it('should return an array of tags', async () => {
      // Arrange
      const userId = 1;
      const tags = [
        {
          id: 1,
          userId,
          name: 'Tag 1',
          color: '#FF5733',
          createdAt: **********,
          updatedAt: **********,
        },
        {
          id: 2,
          userId,
          name: 'Tag 2',
          color: '#33FF57',
          createdAt: **********,
          updatedAt: **********,
        },
      ];

      mockUserTagRepository.find.mockResolvedValue(tags);

      // Act
      const result = await service.findAll(userId);

      // Assert
      expect(mockUserTagRepository.find).toHaveBeenCalledWith({ where: { userId } });
      expect(result).toHaveLength(2);
      expect(result[0].id).toEqual(tags[0].id);
      expect(result[1].id).toEqual(tags[1].id);
    });
  });

  describe('findOne', () => {
    it('should return a tag by id', async () => {
      // Arrange
      const userId = 1;
      const tagId = 1;
      const tag = {
        id: tagId,
        userId,
        name: 'Test Tag',
        color: '#FF5733',
        createdAt: **********,
        updatedAt: **********,
      };

      mockUserTagRepository.findOne.mockResolvedValue(tag);

      // Act
      const result = await service.findOne(userId, tagId);

      // Assert
      expect(mockUserTagRepository.findOne).toHaveBeenCalledWith({ where: { id: tagId, userId } });
      expect(result).toEqual({
        id: tag.id,
        name: tag.name,
        color: tag.color,
        createdAt: tag.createdAt,
        updatedAt: tag.updatedAt,
      });
    });

    it('should throw NotFoundException if tag not found', async () => {
      // Arrange
      const userId = 1;
      const tagId = 999;

      mockUserTagRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(userId, tagId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a tag', async () => {
      // Arrange
      const userId = 1;
      const tagId = 1;
      const updateTagDto: UpdateTagDto = {
        name: 'Updated Tag',
      };

      const existingTag = {
        id: tagId,
        userId,
        name: 'Test Tag',
        color: '#FF5733',
        createdAt: **********,
        updatedAt: **********,
      };

      const updatedTag = {
        ...existingTag,
        name: updateTagDto.name,
        updatedAt: 1619171300,
      };

      mockUserTagRepository.findOne.mockResolvedValue(existingTag);
      mockUserTagRepository.save.mockResolvedValue(updatedTag);

      // Act
      const result = await service.update(userId, tagId, updateTagDto);

      // Assert
      expect(mockUserTagRepository.findOne).toHaveBeenCalledWith({ where: { id: tagId, userId } });
      expect(mockUserTagRepository.save).toHaveBeenCalled();
      expect(result.name).toEqual(updateTagDto.name);
    });

    it('should throw NotFoundException if tag not found', async () => {
      // Arrange
      const userId = 1;
      const tagId = 999;
      const updateTagDto: UpdateTagDto = {
        name: 'Updated Tag',
      };

      mockUserTagRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(userId, tagId, updateTagDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove a tag', async () => {
      // Arrange
      const userId = 1;
      const tagId = 1;
      const tag = {
        id: tagId,
        userId,
        name: 'Test Tag',
        color: '#FF5733',
        createdAt: **********,
        updatedAt: **********,
      };

      mockUserTagRepository.findOne.mockResolvedValue(tag);
      mockUserTagRepository.remove.mockResolvedValue(tag);

      // Act
      const result = await service.remove(userId, tagId);

      // Assert
      expect(mockUserTagRepository.findOne).toHaveBeenCalledWith({ where: { id: tagId, userId } });
      expect(mockUserTagRepository.remove).toHaveBeenCalledWith(tag);
      expect(result).toBe(true);
    });

    it('should throw NotFoundException if tag not found', async () => {
      // Arrange
      const userId = 1;
      const tagId = 999;

      mockUserTagRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(userId, tagId)).rejects.toThrow(NotFoundException);
    });
  });
});
