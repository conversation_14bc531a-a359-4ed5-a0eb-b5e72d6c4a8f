import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminTagRepository } from '../repositories/admin-tag.repository';
import { CreateTagDto, UpdateTagDto, TagResponseDto, TagQueryDto, TagSortField, SortOrder } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { AdminTag } from '../entities';
import { Like, FindOptionsWhere } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic liên quan đến tag
 */
@Injectable()
export class AdminTagService {
  constructor(private readonly adminTagRepository: AdminTagRepository) {}

  /**
   * Tạo tag mới
   * @param employeeId ID của employee
   * @param createTagDto Dữ liệu tạo tag
   * @returns Tag đã tạo
   */
  @Transactional()
  async create(employeeId: number, createTagDto: CreateTagDto): Promise<TagResponseDto> {
    // Tạo tag mới
    const tag = new AdminTag();
    tag.name = createTagDto.name;
    tag.color = '#000000'; // Màu mặc định
    tag.createdAt = Math.floor(Date.now() / 1000);
    tag.updatedAt = Math.floor(Date.now() / 1000);
    tag.createdBy = employeeId;
    tag.updatedBy = employeeId;

    // Lưu tag
    const savedTag = await this.adminTagRepository.save(tag);

    // Đảm bảo savedTag là một đối tượng AdminTag, không phải mảng
    return this.mapToDto(savedTag as AdminTag);
  }

  /**
   * Cập nhật tag
   * @param employeeId ID của employee
   * @param id ID của tag
   * @param updateTagDto Dữ liệu cập nhật tag
   * @returns Tag đã cập nhật
   */
  @Transactional()
  async update(employeeId: number, id: number, updateTagDto: UpdateTagDto): Promise<TagResponseDto> {
    // Kiểm tra tag tồn tại
    const tag = await this.adminTagRepository.findOne({
      where: { id, createdBy: employeeId },
    });

    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    // Cập nhật thông tin tag
    if (updateTagDto.name) {
      tag.name = updateTagDto.name;
    }

    tag.updatedAt = Math.floor(Date.now() / 1000);
    tag.updatedBy = employeeId;

    // Lưu tag
    const updatedTag = await this.adminTagRepository.save(tag);

    // Đảm bảo updatedTag là một đối tượng AdminTag, không phải mảng
    return this.mapToDto(updatedTag as AdminTag);
  }

  /**
   * Lấy danh sách tag của employee với phân trang và filter
   * @param employeeId ID của employee
   * @param query Tham số truy vấn
   * @returns Danh sách tag với phân trang
   */
  async findAll(employeeId: number, query: TagQueryDto): Promise<PaginatedResponseDto<TagResponseDto>> {
    const { page = 1, limit = 10, name, sortBy = TagSortField.CREATED_AT, sortOrder = SortOrder.DESC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<AdminTag> = { createdBy: employeeId };

    // Thêm điều kiện tìm kiếm theo tên
    if (name) {
      where.name = Like(`%${name}%`);
    }

    // Đếm tổng số tag
    const total = await this.adminTagRepository.count({ where });

    // Lấy danh sách tag với phân trang và sắp xếp
    const tags = await this.adminTagRepository.find({
      where,
      order: { [sortBy]: sortOrder },
      skip: offset,
      take: limit,
    });

    // Chuyển đổi kết quả thành DTO
    const data = tags.map(tag => this.mapToDto(tag));

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy tag theo ID
   * @param employeeId ID của employee
   * @param id ID của tag
   * @returns Tag
   */
  async findOne(employeeId: number, id: number): Promise<TagResponseDto> {
    // Kiểm tra tag tồn tại
    const tag = await this.adminTagRepository.findOne({
      where: { id, createdBy: employeeId },
    });

    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    return this.mapToDto(tag);
  }

  /**
   * Xóa tag
   * @param employeeId ID của employee
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(employeeId: number, id: number): Promise<boolean> {
    // Kiểm tra tag tồn tại
    const tag = await this.adminTagRepository.findOne({
      where: { id, createdBy: employeeId },
    });

    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    // Xóa tag
    await this.adminTagRepository.remove(tag);

    return true;
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param tag Tag entity
   * @returns Tag DTO
   */
  private mapToDto(tag: AdminTag): TagResponseDto {
    const dto = new TagResponseDto();
    dto.id = tag.id;
    dto.createdBy = tag.createdBy;
    dto.updatedBy = tag.updatedBy;
    dto.name = tag.name;
    dto.color = tag.color;
    dto.createdAt = tag.createdAt;
    dto.updatedAt = tag.updatedAt;
    return dto;
  }
}
