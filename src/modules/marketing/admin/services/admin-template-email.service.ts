import { Injectable, Logger } from '@nestjs/common';
import { AdminTemplateEmailRepository } from '../repositories/admin-template-email.repository';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { CategoryTemplateAutoEnum } from '@modules/email/interface/category-template-auto.enum';
import { TemplateEmailQueryDto, TemplateEmailSortField, TemplateEmailResponseDto, TemplateEmailSortOrder } from '../dto/template-email';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { Like, FindOptionsWhere } from 'typeorm';

/**
 * Service xử lý logic liên quan đến template email bên admin
 */
@Injectable()
export class AdminTemplateEmailService {
  private readonly logger = new Logger(AdminTemplateEmailService.name);

  constructor(
    private readonly adminTemplateEmailRepository: AdminTemplateEmailRepository,
  ) {}

  /**
   * Tìm template email theo category
   * @param category Danh mục của template
   * @returns Template email
   */
  async findByCategory(category: string): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tìm template email theo category: ${category}`);
    return this.adminTemplateEmailRepository.findByCategory(category);
  }

  /**
   * Tìm template email theo category sử dụng enum
   * @param category Danh mục của template (enum)
   * @returns Template email
   */
  async findTemplateAutoByCategory(category: CategoryTemplateAutoEnum): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tìm template email theo category enum: ${category}`);
    return this.adminTemplateEmailRepository.findTemplateAutoByCategory(category);
  }

  /**
   * Lấy danh sách template email với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách template email với phân trang
   */
  async findAll(query: TemplateEmailQueryDto): Promise<PaginatedResponseDto<TemplateEmailResponseDto>> {
    this.logger.debug('Lấy danh sách template email với phân trang và filter');

    const { page = 1, limit = 10, category, subject, sortBy = TemplateEmailSortField.CREATED_AT, sortOrder = TemplateEmailSortOrder.DESC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<AdminTemplateEmail> = {};

    // Thêm điều kiện tìm kiếm theo category
    if (category) {
      where.category = Like(`%${category}%`);
    }

    // Thêm điều kiện tìm kiếm theo subject
    if (subject) {
      where.subject = Like(`%${subject}%`);
    }

    // Đếm tổng số template email
    const total = await this.adminTemplateEmailRepository.count({ where });

    // Lấy danh sách template email với phân trang và sắp xếp
    const templates = await this.adminTemplateEmailRepository.find({
      where,
      order: { [sortBy]: sortOrder },
      skip: offset,
      take: limit,
    });

    // Chuyển đổi kết quả thành DTO
    const data = templates.map(template => this.mapToDto(template));

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy tất cả template email (phương thức cũ để hỗ trợ tương thích ngược)
   * @returns Danh sách template email
   */
  async findAllTemplates(): Promise<AdminTemplateEmail[]> {
    this.logger.debug('Lấy tất cả template email');
    return this.adminTemplateEmailRepository.findAll();
  }

  /**
   * Lấy template email theo ID
   * @param id ID của template
   * @returns Template email
   */
  async findById(id: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Lấy template email theo ID: ${id}`);
    return this.adminTemplateEmailRepository.findById(id);
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @param employeeId ID của nhân viên tạo
   * @returns Template email đã tạo
   */
  async create(data: Partial<AdminTemplateEmail>, employeeId: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tạo mới template email với category: ${data.category}`);

    // Thêm thông tin người tạo và thời gian
    const templateData = {
      ...data,
      createdBy: employeeId,
      createdAt: Date.now(),
    };

    return this.adminTemplateEmailRepository.create(templateData);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param data Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, data: Partial<AdminTemplateEmail>, employeeId: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Cập nhật template email với ID: ${id}`);

    // Thêm thông tin người cập nhật và thời gian
    const templateData = {
      ...data,
      updatedBy: employeeId,
      updatedAt: Date.now(),
    };

    return this.adminTemplateEmailRepository.update(id, templateData);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    this.logger.debug(`Xóa template email với ID: ${id}`);
    return this.adminTemplateEmailRepository.delete(id);
  }

  /**
   * Tìm template email theo danh sách category
   * @param categories Danh sách category
   * @returns Danh sách template email
   */
  async findByCategories(categories: string[]): Promise<AdminTemplateEmail[]> {
    this.logger.debug(`Tìm template email theo danh sách category: ${categories.join(', ')}`);

    // Sử dụng queryBuilder để tìm kiếm theo danh sách category
    const templates = await this.adminTemplateEmailRepository.repository
      .createQueryBuilder('template')
      .where('template.category IN (:...categories)', { categories })
      .getMany();

    return templates;
  }

  /**
   * Tìm template email theo danh sách category enum
   * @param categories Danh sách category enum
   * @returns Danh sách template email
   */
  async findTemplateAutoByCategories(categories: CategoryTemplateAutoEnum[]): Promise<AdminTemplateEmail[]> {
    return this.findByCategories(categories as unknown as string[]);
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param template Template email entity
   * @returns Template email DTO
   */
  private mapToDto(template: AdminTemplateEmail): TemplateEmailResponseDto {
    const dto = new TemplateEmailResponseDto();
    dto.id = template.id;
    dto.category = template.category;
    dto.subject = template.subject;
    dto.name = template.name;
    dto.content = template.content;
    dto.placeholders = template.placeholders;
    dto.createdAt = template.createdAt;
    dto.updatedAt = template.updatedAt;
    return dto;
  }
}
