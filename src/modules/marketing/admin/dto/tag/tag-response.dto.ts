import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi tag
 */
export class TagResponseDto {
  /**
   * ID của tag
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tag',
    example: 1,
  })
  id: number;

  /**
   * ID của nhân viên tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của nhân viên tạo',
    example: 1,
  })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật
   * @example 1
   */
  @ApiProperty({
    description: 'ID của nhân viên cập nhật',
    example: 1,
  })
  updatedBy: number;

  /**
   * Tên tag
   * @example "VIP"
   */
  @ApiProperty({
    description: 'Tên tag',
    example: 'VIP',
  })
  name: string;

  /**
   * <PERSON><PERSON><PERSON> sắc của tag
   * @example "#FF0000"
   */
  @ApiProperty({
    description: '<PERSON><PERSON>u sắc của tag',
    example: '#FF0000',
  })
  color: string;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
