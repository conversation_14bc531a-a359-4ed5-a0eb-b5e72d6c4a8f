import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi template email
 */
export class TemplateEmailResponseDto {
  /**
   * ID của template email
   * @example 1
   */
  @ApiProperty({
    description: 'ID của template email',
    example: 1,
  })
  id: number;

  /**
   * Category của template email
   * @example "ACCOUNT_VERIFICATION"
   */
  @ApiProperty({
    description: 'Category của template email',
    example: 'ACCOUNT_VERIFICATION',
  })
  category: string;

  /**
   * Subject của template email
   * @example "Xác thực tài khoản"
   */
  @ApiProperty({
    description: 'Subject của template email',
    example: 'Xác thực tài khoản',
  })
  subject: string;

  /**
   * Tên mẫu
   * @example "Mẫu email xác thực tài khoản"
   */
  @ApiProperty({
    description: 'Tên mẫu',
    example: 'Mẫu email xác thực tài khoản',
  })
  name: string;

  /**
   * Nội dung của template email
   * @example "<html><body>...</body></html>"
   */
  @ApiProperty({
    description: 'Nội dung của template email',
    example: '<html><body>...</body></html>',
  })
  content: string;

  /**
   * Danh sách các placeholder
   */
  @ApiProperty({
    description: 'Danh sách các placeholder',
    example: { name: 'Tên người dùng', otp: 'Mã OTP' },
  })
  placeholders: any;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example **********
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example **********
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: **********,
  })
  updatedAt: number;
}
