import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { AdminCampaign } from '../entities/admin-campaign.entity';

/**
 * Repository cho AdminCampaign
 */
@Injectable()
export class AdminCampaignRepository {
  constructor(
    @InjectRepository(AdminCampaign)
    private readonly repository: Repository<AdminCampaign>,
  ) {}

  /**
   * Tìm kiếm nhiều campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách campaign
   */
  async find(options?: FindManyOptions<AdminCampaign>): Promise<AdminCampaign[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Campaign hoặc null
   */
  async findOne(options?: FindOneOptions<AdminCampaign>): Promise<AdminCampaign | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu campaign
   * @param campaign Campaign cần lưu
   * @returns Campaign đã lưu
   */
  async save(campaign: AdminCampaign): Promise<AdminCampaign>;
  async save(campaign: AdminCampaign[]): Promise<AdminCampaign[]>;
  async save(campaign: AdminCampaign | AdminCampaign[]): Promise<AdminCampaign | AdminCampaign[]> {
    return this.repository.save(campaign as any);
  }

  /**
   * Xóa campaign
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<AdminCampaign>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa campaign
   * @param campaign Campaign cần xóa
   * @returns Campaign đã xóa
   */
  async remove(campaign: AdminCampaign): Promise<AdminCampaign>;
  async remove(campaign: AdminCampaign[]): Promise<AdminCampaign[]>;
  async remove(campaign: AdminCampaign | AdminCampaign[]): Promise<AdminCampaign | AdminCampaign[]> {
    return this.repository.remove(campaign as any);
  }

  /**
   * Đếm số lượng campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng campaign
   */
  async count(options?: FindManyOptions<AdminCampaign>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }
}
