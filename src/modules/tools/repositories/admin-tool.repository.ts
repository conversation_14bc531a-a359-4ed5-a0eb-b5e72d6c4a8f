import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminTool } from '../entities/admin-tool.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { AccessTypeEnum } from '../constants/access-type.enum';
import { PaginatedResult } from '@common/response/api-response-dto';

@Injectable()
export class AdminToolRepository extends Repository<AdminTool> {
  private readonly logger = new Logger(AdminToolRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminTool, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho admin tool
   * @returns SelectQueryBuilder<AdminTool> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTool> {
    return this.createQueryBuilder('tool');
  }

  /**
   * Tìm tool theo ID
   * @param id ID của tool cần tìm
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolById(id: string): Promise<AdminTool | null> {
    return this.createBaseQuery()
      .where('tool.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm tool theo tên
   * @param name Tên của tool cần tìm
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolByName(name: string): Promise<AdminTool | null> {
    return this.createBaseQuery()
      .where('tool.name = :name', { name })
      .getOne();
  }

  /**
   * Lấy danh sách tool với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái tool (tùy chọn)
   * @param accessType Loại quyền truy cập (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tool với phân trang
   */
  async findTools(
    page: number,
    limit: number,
    search?: string,
    status?: ToolStatusEnum,
    accessType?: AccessTypeEnum,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AdminTool>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy tools có trạng thái DRAFT hoặc APPROVED (loại bỏ tools đã xóa mềm)
    qb.andWhere('tool.status IN (:...allowedStatuses)', {
      allowedStatuses: [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED]
    });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có (trong phạm vi cho phép)
    if (status && [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED].includes(status)) {
      qb.andWhere('tool.status = :status', { status });
    }

    // Thêm điều kiện lọc theo loại quyền truy cập nếu có
    if (accessType) {
      qb.andWhere('tool.accessType = :accessType', { accessType });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`tool.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Kiểm tra tính hợp lệ và tính duy nhất của functionName
   * @param toolName Tên function cần kiểm tra
   * @param excludeToolId ID của tool cần loại trừ khi kiểm tra (dùng khi update)
   * @returns Kết quả kiểm tra: { valid: boolean, reason?: string }
   */
  async validateToolName(
    toolName: string,
    excludeToolId?: string,
  ): Promise<{ valid: boolean; reason?: string }> {
    // Kiểm tra định dạng functionName theo quy tắc của OpenAI
    const nameRegex = /^[a-zA-Z0-9_]+$/;
    if (!nameRegex.test(toolName)) {
      return {
        valid: false,
        reason:
          'Tên hàm chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
      };
    }

    // Kiểm tra độ dài functionName
    if (toolName.length > 64) {
      return {
        valid: false,
        reason: 'Tên hàm không được vượt quá 64 ký tự',
      };
    }

    try {
      // Kiểm tra tính duy nhất của functionName trong codeDefinition
      const qb = this.createQueryBuilder('tool')
        .leftJoin(
          'admin_tool_versions',
          'version',
          'tool.id = version.tool_id',
        )
        .where('version.tool_name = :toolName', { toolName });

      // Nếu có excludeToolId, loại trừ tool này khỏi kết quả kiểm tra
      if (excludeToolId) {
        qb.andWhere('tool.id != :excludeToolId', { excludeToolId });
      }

      const existingTool = await qb.getOne();

      if (existingTool) {
        return {
          valid: false,
          reason: `Tên hàm '${toolName}' đã được sử dụng`,
        };
      }

      return { valid: true };
    } catch (error) {
      this.logger.error(`Error validating function name: ${error.message}`, error.stack);
      return {
        valid: false,
        reason: 'Lỗi khi kiểm tra tên hàm',
      };
    }
  }

  /**
   * Lấy tất cả các tool
   * @returns Danh sách tất cả các tool
   */
  async findAll(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .orderBy('tool.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Kiểm tra xem danh sách tool_id có tồn tại trong cơ sở dữ liệu không
   * @param toolIds Danh sách ID của các tool cần kiểm tra
   * @returns Đối tượng chứa thông tin về các ID tồn tại và không tồn tại
   */
  async validateToolIds(toolIds: string[]): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, trả về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Tìm tất cả các tool có ID nằm trong danh sách
      const existingTools = await this.find({
        where: { id: In(toolIds) },
        select: ['id'], // Chỉ lấy trường ID để tối ưu hiệu suất
      });

      // Lấy danh sách ID tồn tại
      const existingIds = existingTools.map((tool) => tool.id);

      // Lấy danh sách ID không tồn tại
      const nonExistingIds = toolIds.filter(
        (id) => !existingIds.includes(id),
      );

      return {
        valid: nonExistingIds.length === 0,
        existingIds,
        nonExistingIds,
      };
    } catch (error) {
      this.logger.error(`Error validating tool IDs: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }

  /**
   * Lấy danh sách tất cả tool có trạng thái APPROVED
   * @returns Danh sách tool APPROVED
   */
  async findAllApprovedTools(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .where('tool.status = :status', { status: ToolStatusEnum.APPROVED })
      .getMany();
  }

  /**
   * Lấy danh sách tools với default versions và group info trong một query tối ưu
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái tool (tùy chọn)
   * @param accessType Loại quyền truy cập (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tool với default versions và group info
   */
  async findToolsWithLatestVersions(
    page: number,
    limit: number,
    search?: string,
    status?: ToolStatusEnum,
    accessType?: AccessTypeEnum,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AdminTool & { defaultVersion?: any; groupInfo?: any }>> {
    try {
      // Query chính với LEFT JOIN để lấy default version và group info
      const qb = this.createBaseQuery()
        // LEFT JOIN để lấy default version
        .leftJoin(
          'admin_tool_versions',
          'defaultVer',
          'tool.id = defaultVer.tool_id AND defaultVer.is_default = :isDefault',
          { isDefault: true }
        )
        // LEFT JOIN để lấy latest version (fallback nếu không có default)
        .leftJoin(
          'admin_tool_versions',
          'latestVer',
          `tool.id = latestVer.tool_id AND latestVer.created_at = (
            SELECT MAX(v.created_at)
            FROM admin_tool_versions v
            WHERE v.tool_id = tool.id
          )`
        )
        // LEFT JOIN để lấy group mappings
        .leftJoin('admin_group_tool_mappings', 'mapping', 'tool.id = mapping.tool_id')
        .leftJoin('admin_group_tools', 'groupTool', 'mapping.group_id = groupTool.id')
        // Select default version info (ưu tiên default, fallback về latest)
        .addSelect('COALESCE(defaultVer.id, latestVer.id)', 'versionId')
        .addSelect('COALESCE(defaultVer.version_name, latestVer.version_name)', 'versionName')
        .addSelect('COALESCE(defaultVer.is_default, false)', 'isDefault')
        // Select group info với array aggregation để lấy tất cả groups
        .addSelect('ARRAY_AGG(DISTINCT groupTool.id) FILTER (WHERE groupTool.id IS NOT NULL)', 'groupIds')
        .addSelect('ARRAY_AGG(DISTINCT groupTool.name) FILTER (WHERE groupTool.name IS NOT NULL)', 'groupNames');

      // Chỉ lấy tools có trạng thái DRAFT hoặc APPROVED
      qb.andWhere('tool.status IN (:...allowedStatuses)', {
        allowedStatuses: [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED]
      });

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
          { search: `%${search}%` });
      }

      // Thêm điều kiện lọc theo trạng thái nếu có (trong phạm vi cho phép)
      if (status && [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED].includes(status)) {
        qb.andWhere('tool.status = :status', { status });
      }

      // Thêm điều kiện lọc theo loại quyền truy cập nếu có
      if (accessType) {
        qb.andWhere('tool.accessType = :accessType', { accessType });
      }

      // Thêm GROUP BY để hỗ trợ array aggregation
      qb.groupBy('tool.id')
        .addGroupBy('defaultVer.id')
        .addGroupBy('defaultVer.version_name')
        .addGroupBy('defaultVer.is_default')
        .addGroupBy('latestVer.id')
        .addGroupBy('latestVer.version_name');

      // Đếm tổng số records trước khi phân trang (sử dụng subquery để tránh lỗi với GROUP BY)
      const countQuery = this.createBaseQuery()
        .andWhere('tool.status IN (:...allowedStatuses)', {
          allowedStatuses: [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED]
        });

      if (search) {
        countQuery.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
          { search: `%${search}%` });
      }

      if (status && [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED].includes(status)) {
        countQuery.andWhere('tool.status = :status', { status });
      }

      if (accessType) {
        countQuery.andWhere('tool.accessType = :accessType', { accessType });
      }

      const total = await countQuery.getCount();

      // Thêm phân trang và sắp xếp
      qb.skip((page - 1) * limit)
        .take(limit)
        .orderBy(`tool.${sortBy}`, sortDirection);

      // Lấy kết quả với raw data để có thể access các field bổ sung
      const rawResults = await qb.getRawAndEntities();

      // Map kết quả để thêm thông tin bổ sung vào entity
      const items = rawResults.entities.map((tool, index) => {
        const rawData = rawResults.raw[index];

        // Parse group arrays từ PostgreSQL
        const groupIds = rawData.groupIds ?
          (Array.isArray(rawData.groupIds) ? rawData.groupIds : [rawData.groupIds]) : [];
        const groupNames = rawData.groupNames ?
          (Array.isArray(rawData.groupNames) ? rawData.groupNames : [rawData.groupNames]) : [];

        // Tạo danh sách groups
        const groups = groupIds.map((id: number, idx: number) => ({
          id,
          name: groupNames[idx] || null
        })).filter((group: any) => group.id !== null);

        return {
          ...tool,
          defaultVersion: {
            id: rawData.versionId || null,
            versionName: rawData.versionName || null,
            isDefault: rawData.isDefault || false
          },
          groups: groups,
          // Backward compatibility
          latestVersionId: rawData.versionId || null,
          latestVersionName: rawData.versionName || null,
          groupId: groups.length > 0 ? groups[0].id : null,
          groupName: groups.length > 0 ? groups[0].name : null
        };
      });

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding tools with latest versions and group info: ${error.message}`, error.stack);
      // Fallback to original method if optimized query fails
      return this.findTools(page, limit, search, status, accessType, sortBy, sortDirection);
    }
  }

  /**
   * Soft delete tool - đánh dấu tool là đã xóa
   * @param toolId ID của tool cần xóa
   * @param deletedBy ID của employee thực hiện xóa
   * @returns Promise<void>
   */
  async softDeleteTool(toolId: string, deletedBy: number): Promise<void> {
    try {
      const now = Date.now();
      await this.createQueryBuilder()
        .update(AdminTool)
        .set({
          deletedAt: now,
          deletedBy: deletedBy,
          status: ToolStatusEnum.DEPRECATED
        })
        .where('id = :toolId', { toolId })
        .andWhere('deletedAt IS NULL') // Chỉ xóa tools chưa bị xóa
        .execute();

      this.logger.log(`Soft deleted tool ${toolId} by employee ${deletedBy}`);
    } catch (error) {
      this.logger.error(`Error soft deleting tool ${toolId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Rollback tool - khôi phục tool đã bị xóa mềm
   * @param toolId ID của tool cần khôi phục
   * @returns Promise<void>
   */
  async rollbackTool(toolId: string): Promise<void> {
    try {
      await this.createQueryBuilder()
        .update(AdminTool)
        .set({
          deletedAt: null,
          deletedBy: null,
          status: ToolStatusEnum.DRAFT // Khôi phục về trạng thái DRAFT
        })
        .where('id = :toolId', { toolId })
        .andWhere('deletedAt IS NOT NULL') // Chỉ rollback tools đã bị xóa
        .execute();

      this.logger.log(`Rollback tool ${toolId} successfully`);
    } catch (error) {
      this.logger.error(`Error rollback tool ${toolId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách tools đã bị xóa mềm
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tools đã bị xóa
   */
  async findDeletedTools(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AdminTool & { deletedByInfo?: any }>> {
    try {
      // Tạo query builder cơ bản
      const baseQuery = this.createBaseQuery()
        .where('tool.deletedAt IS NOT NULL'); // Chỉ lấy tools đã bị xóa

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        baseQuery.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
          { search: `%${search}%` });
      }

      // Đếm tổng số records
      const total = await baseQuery.getCount();

      // Tạo query chính với join để lấy thông tin employee
      const qb = this.createBaseQuery()
        .leftJoinAndSelect(Employee, 'deletedByEmployee', 'tool.deletedBy = deletedByEmployee.id')
        .where('tool.deletedAt IS NOT NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
          { search: `%${search}%` });
      }

      // Thêm phân trang và sắp xếp
      qb.skip((page - 1) * limit)
        .take(limit)
        .orderBy(`tool.${sortBy}`, sortDirection);

      // Lấy kết quả
      const tools = await qb.getMany();

      // Map kết quả để thêm thông tin người xóa
      const items = tools.map((tool) => {
        return {
          ...tool,
          deletedByInfo: tool.deletedBy ? {
            name: (tool as any).deletedByEmployee?.fullName || null,
            email: (tool as any).deletedByEmployee?.email || null
          } : null
        };
      });

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding deleted tools: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật version mặc định cho tool
   * @param toolId ID của tool
   * @param versionId ID của version mới làm mặc định (null để clear default)
   * @returns Promise<void>
   */
  async updateDefaultVersion(toolId: string, versionId: string | null): Promise<void> {
    try {
      // Cập nhật field versionDefault trong admin_tools
      await this.dataSource.query(
        'UPDATE admin_tools SET version_default = $1, updated_at = $2 WHERE id = $3 AND deleted_at IS NULL',
        [versionId, Date.now(), toolId]
      );

      this.logger.log(`Updated default version for tool ${toolId} to version ${versionId || 'null'}`);
    } catch (error) {
      this.logger.error(`Error updating default version for tool ${toolId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra tồn tại tool name và function name trong một query duy nhất
   * @param toolName Tên hiển thị của tool
   * @param functionName Tên function của tool
   * @param excludeToolId ID tool cần loại trừ (dùng khi update)
   * @returns Kết quả kiểm tra tồn tại
   */
  async validateToolAndFunctionName(
    toolName: string,
    functionName: string,
    excludeToolId?: string,
  ): Promise<{
    toolNameExists: boolean;
    functionNameExists: boolean;
    functionNameValid: boolean;
    functionNameReason?: string;
  }> {
    try {
      // Kiểm tra định dạng functionName trước
      const nameRegex = /^[a-zA-Z0-9_]+$/;
      if (!nameRegex.test(functionName)) {
        return {
          toolNameExists: false,
          functionNameExists: false,
          functionNameValid: false,
          functionNameReason: 'Tên hàm chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
        };
      }

      if (functionName.length > 64) {
        return {
          toolNameExists: false,
          functionNameExists: false,
          functionNameValid: false,
          functionNameReason: 'Tên hàm không được vượt quá 64 ký tự',
        };
      }

      // Sử dụng EXISTS để kiểm tra tồn tại hiệu quả hơn
      const toolNameExistsQuery = this.createQueryBuilder()
        .select('1')
        .where('name = :toolName', { toolName });

      if (excludeToolId) {
        toolNameExistsQuery.andWhere('id != :excludeToolId', { excludeToolId });
      }

      const functionNameExistsQuery = this.createQueryBuilder('tool')
        .select('1')
        .leftJoin('admin_tool_versions', 'version', 'tool.id = version.tool_id')
        .where('version.tool_name = :functionName', { functionName });

      if (excludeToolId) {
        functionNameExistsQuery.andWhere('tool.id != :excludeToolId', { excludeToolId });
      }

      // Thực hiện 2 query EXISTS song song (sử dụng getCount() > 0 thay vì getExists())
      const [toolNameCount, functionNameCount] = await Promise.all([
        toolNameExistsQuery.getCount(),
        functionNameExistsQuery.getCount(),
      ]);

      const toolNameExists = toolNameCount > 0;
      const functionNameExists = functionNameCount > 0;

      return {
        toolNameExists,
        functionNameExists,
        functionNameValid: true,
      };
    } catch (error) {
      this.logger.error(`Error validating tool and function name: ${error.message}`, error.stack);
      return {
        toolNameExists: false,
        functionNameExists: false,
        functionNameValid: false,
        functionNameReason: 'Lỗi khi kiểm tra tên tool và function',
      };
    }
  }

  /**
   * Lấy danh sách tất cả tool công khai có trạng thái APPROVED
   * @returns Danh sách tool công khai APPROVED
   */
  async findAllPublicApprovedTools(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .where('tool.accessType = :accessType', { accessType: AccessTypeEnum.PUBLIC })
      .andWhere('tool.status = :status', { status: ToolStatusEnum.APPROVED })
      .getMany();
  }
}
