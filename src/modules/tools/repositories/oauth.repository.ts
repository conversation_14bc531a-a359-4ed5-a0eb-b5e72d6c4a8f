import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { OAuth } from '../entities/oauth.entity';
import { AppException } from '@common/exceptions';
import { CUSTOM_TOOLS_ERROR_CODES } from '../exceptions';

@Injectable()
export class OAuthRepository extends Repository<OAuth> {
  private readonly logger = new Logger(OAuthRepository.name);

  constructor(private dataSource: DataSource) {
    super(OAuth, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho OAuth
   * @returns SelectQueryBuilder<OAuth> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<OAuth> {
    return this.createQueryBuilder('oauth');
  }

  /**
   * Tìm OAuth theo ID
   * @param id ID của OAuth cần tìm
   * @returns OAuth nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<OAuth | null> {
    try {
      return this.createBaseQuery()
        .where('oauth.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm OAuth theo ID: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_NOT_FOUND, `Không tìm thấy cấu hình OAuth với ID ${id}`);
    }
  }

  /**
   * Tạo mới OAuth
   * @param oauth Dữ liệu OAuth cần tạo
   * @returns OAuth đã tạo
   */
  @Transactional()
  async createOAuth(oauth: Partial<OAuth>): Promise<OAuth> {
    try {
      const newOAuth = this.create(oauth);
      return await this.save(newOAuth);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo OAuth: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_CREATE_FAILED, 'Tạo cấu hình OAuth thất bại');
    }
  }

  /**
   * Cập nhật OAuth
   * @param id ID của OAuth cần cập nhật
   * @param oauth Dữ liệu cập nhật
   * @returns Void
   */
  @Transactional()
  async updateOAuth(id: string, oauth: Partial<OAuth>): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(OAuth)
        .set({
          ...oauth,
          updatedAt: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
        })
        .where('id = :id', { id })
        .execute();

      if (result.affected === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_NOT_FOUND, `Không tìm thấy cấu hình OAuth với ID ${id}`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật OAuth: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_UPDATE_FAILED, 'Cập nhật cấu hình OAuth thất bại');
    }
  }

  /**
   * Xóa OAuth
   * @param id ID của OAuth cần xóa
   * @returns Void
   */
  @Transactional()
  async deleteOAuth(id: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(OAuth)
        .where('id = :id', { id })
        .execute();

      if (result.affected === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_NOT_FOUND, `Không tìm thấy cấu hình OAuth với ID ${id}`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa OAuth: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OAUTH_DELETE_FAILED, 'Xóa cấu hình OAuth thất bại');
    }
  }
}
