import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminToolVersion } from '../entities/admin-tool-version.entity';
import { FunctionStatusEnum } from '../constants/tools-status.enum';

@Injectable()
export class AdminToolVersionRepository extends Repository<AdminToolVersion> {
  private readonly logger = new Logger(AdminToolVersionRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminToolVersion, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho admin tool version
   * @returns SelectQueryBuilder<AdminToolVersion> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<AdminToolVersion> {
    return this.createQueryBuilder('version');
  }

  /**
   * Tìm phiên bản tool theo ID
   * @param id ID của phiên bản tool cần tìm
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionById(id: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool và tên phiên bản
   * @param functionId ID của tool
   * @param versionName Tên phiên bản
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionByFunctionIdAndVersionName(
    functionId: string,
    versionName: string,
  ): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.versionName = :versionName', { versionName })
      .getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool và số phiên bản (deprecated)
   * @param functionId ID của tool
   * @param versionNumber Số phiên bản
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   * @deprecated Sử dụng findVersionByFunctionIdAndVersionName thay thế
   */
  async findVersionByFunctionIdAndVersionNumber(
    functionId: string,
    versionNumber: number,
  ): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.versionNumber = :versionNumber', { versionNumber })
      .getOne();
  }

  /**
   * Lấy phiên bản mới nhất của tool
   * @param functionId ID của tool
   * @returns Phiên bản mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestVersionByFunctionId(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Lấy danh sách phiên bản của tool
   * @param functionId ID của tool
   * @returns Danh sách phiên bản của tool
   */
  async findVersionsByToolId(functionId: string): Promise<AdminToolVersion[]> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy tên phiên bản mới nhất của tool
   * @param functionId ID của tool
   * @returns Tên phiên bản mới nhất của tool
   */
  async getLatestVersionName(functionId: string): Promise<string> {
    const latestVersion = await this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();

    return latestVersion ? latestVersion.versionName : 'v1.0.0';
  }


  /**
   * Kiểm tra xem tên hàm đã tồn tại trong phiên bản khác chưa
   * @param toolName Tên hàm cần kiểm tra
   * @param excludeVersionId ID của phiên bản cần loại trừ khi kiểm tra (dùng khi update)
   * @returns true nếu tên hàm đã tồn tại, false nếu chưa tồn tại
   */
  async isToolNameExists(
    toolName: string,
    excludeVersionId?: string,
  ): Promise<boolean> {
    const qb = this.createQueryBuilder('version')
      .where('version.toolName = :toolName', { toolName });

    if (excludeVersionId) {
      qb.andWhere('version.id != :excludeVersionId', { excludeVersionId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Lấy phiên bản được phê duyệt mới nhất của tool
   * @param functionId ID của tool
   * @returns Phiên bản được phê duyệt mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestApprovedVersionByFunctionId(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.status = :status', { status: FunctionStatusEnum.APPROVED })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Lấy phiên bản mới nhất dựa trên createdAt thay vì versionNumber
   * @param functionId ID của tool
   * @returns Phiên bản mới nhất
   */
  async findLatestVersionByCreatedAt(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Cập nhật trạng thái isDefault cho tất cả versions của một tool
   * @param toolId ID của tool
   * @param newDefaultVersionId ID của version mới làm mặc định
   * @returns Promise<void>
   */
  async updateDefaultVersionStatus(toolId: string, newDefaultVersionId: string): Promise<void> {
    try {
      // Đặt tất cả versions của tool thành không phải default
      await this.createQueryBuilder()
        .update(AdminToolVersion)
        .set({ isDefault: false })
        .where('toolId = :toolId', { toolId })
        .execute();

      // Đặt version được chọn thành default
      await this.createQueryBuilder()
        .update(AdminToolVersion)
        .set({ isDefault: true })
        .where('id = :versionId', { versionId: newDefaultVersionId })
        .andWhere('toolId = :toolId', { toolId })
        .execute();

      this.logger.log(`Updated default version status for tool ${toolId}, new default: ${newDefaultVersionId}`);
    } catch (error) {
      this.logger.error(`Error updating default version status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy version mặc định của tool
   * @param toolId ID của tool
   * @returns Version mặc định hoặc null
   */
  async findDefaultVersion(toolId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :toolId', { toolId })
      .andWhere('version.isDefault = :isDefault', { isDefault: true })
      .getOne();
  }

  /**
   * Tự động chọn version mặc định mới khi xóa version hiện tại
   * @param toolId ID của tool
   * @param excludeVersionId ID của version bị xóa (để loại trừ)
   * @returns Promise<string | null> - ID của version mặc định mới
   */
  async selectNewDefaultVersion(toolId: string, excludeVersionId: string): Promise<string | null> {
    try {
      // Tìm version mới nhất (không phải version bị xóa)
      const latestVersion = await this.createBaseQuery()
        .where('version.toolId = :toolId', { toolId })
        .andWhere('version.id != :excludeVersionId', { excludeVersionId })
        .orderBy('version.createdAt', 'DESC')
        .getOne();

      if (latestVersion) {
        // Cập nhật version này thành default
        await this.updateDefaultVersionStatus(toolId, latestVersion.id);
        return latestVersion.id;
      }

      return null;
    } catch (error) {
      this.logger.error(`Error selecting new default version: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem tool có versions nào không (sau khi xóa)
   * @param toolId ID của tool
   * @returns Promise<boolean> - true nếu còn versions
   */
  async hasVersions(toolId: string): Promise<boolean> {
    try {
      const count = await this.createBaseQuery()
        .where('version.toolId = :toolId', { toolId })
        .getCount();

      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking if tool has versions: ${error.message}`, error.stack);
      return false;
    }
  }
}
