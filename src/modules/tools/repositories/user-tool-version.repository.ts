import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserToolVersion } from '../entities/user-tool-version.entity';
import { ToolStatusEnum } from '../constants/tool-status.enum';

@Injectable()
export class UserToolVersionRepository extends Repository<UserToolVersion> {
  private readonly logger = new Logger(UserToolVersionRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserToolVersion, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho user tool version
   * @returns SelectQueryBuilder<UserToolVersion> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<UserToolVersion> {
    return this.createQueryBuilder('version');
  }

  /**
   * Tì<PERSON> phiên bản tool theo ID
   * @param id ID của phiên bản tool cần tìm
   * @param userId ID của người dùng (tùy chọn, nếu cần kiểm tra quyền sở hữu)
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionById(id: string, userId?: number): Promise<UserToolVersion | null> {
    const qb = this.createBaseQuery()
      .where('version.id = :id', { id });

    if (userId) {
      qb.andWhere('version.userId = :userId', { userId });
    }

    return qb.getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool, ID người dùng và original version ID
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @param originalVersionId ID của phiên bản gốc
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionByFunctionIdAndUserIdAndOriginalVersionId(
    originalToolId: string,
    userId: number,
    originalVersionId: string,
  ): Promise<UserToolVersion | null> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .andWhere('version.originalVersionId = :originalVersionId', { originalVersionId })
      .getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool, ID người dùng và số phiên bản (deprecated)
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @param versionNumber Số phiên bản
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   * @deprecated Sử dụng findVersionByFunctionIdAndUserIdAndVersionName thay thế
   */
  async findVersionByFunctionIdAndUserIdAndVersionNumber(
    originalToolId: string,
    userId: number,
    versionNumber: number,
  ): Promise<UserToolVersion | null> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .andWhere('version.versionNumber = :versionNumber', { versionNumber })
      .getOne();
  }

  /**
   * Lấy phiên bản mới nhất của tool của người dùng
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Phiên bản mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestVersionByFunctionIdAndUserId(
    originalToolId: string,
    userId: number,
  ): Promise<UserToolVersion | null> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Lấy danh sách phiên bản của tool của người dùng
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Danh sách phiên bản của tool
   */
  async findVersionsByToolIdAndUserId(
    originalToolId: string,
    userId: number,
  ): Promise<UserToolVersion[]> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .orderBy('version.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy tên phiên bản mới nhất của tool của người dùng
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Tên phiên bản mới nhất của tool
   */
  async getLatestVersionName(
    originalToolId: string,
    userId: number,
  ): Promise<string> {
    const count = await this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .getCount();

    return count > 0 ? `v${count}.0.0` : 'v1.0.0';
  }



  /**
   * Kiểm tra xem tên hàm đã tồn tại trong phiên bản khác chưa
   * @param toolName Tên hàm cần kiểm tra
   * @param userId ID của người dùng
   * @param excludeVersionId ID của phiên bản cần loại trừ khi kiểm tra (dùng khi update)
   * @returns true nếu tên hàm đã tồn tại, false nếu chưa tồn tại
   */
  async isToolNameExists(
    toolName: string,
    userId: number,
    excludeVersionId?: string,
  ): Promise<boolean> {
    const qb = this.createQueryBuilder('version')
      .where('version.toolName = :toolName', { toolName })
      .andWhere('version.userId = :userId', { userId });

    if (excludeVersionId) {
      qb.andWhere('version.id != :excludeVersionId', { excludeVersionId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Lấy phiên bản được phê duyệt mới nhất của tool của người dùng
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Phiên bản được phê duyệt mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestApprovedVersionByFunctionIdAndUserId(
    originalToolId: string,
    userId: number,
  ): Promise<UserToolVersion | null> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .andWhere('version.status = :status', { status: ToolStatusEnum.APPROVED })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Tạo tên phiên bản mới dựa trên số lượng phiên bản hiện có
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Tên phiên bản mới
   */
  async generateNextVersionName(
    originalToolId: string,
    userId: number,
  ): Promise<string> {
    const count = await this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .getCount();

    // Tạo version name dựa trên số lượng + 1
    const nextVersion = count + 1;
    return `v${nextVersion}.0.0`;
  }

  /**
   * Lấy phiên bản mới nhất dựa trên createdAt thay vì versionNumber
   * @param originalToolId ID của tool
   * @param userId ID của người dùng
   * @returns Phiên bản mới nhất
   */
  async findLatestVersionByCreatedAt(
    originalToolId: string,
    userId: number,
  ): Promise<UserToolVersion | null> {
    return this.createBaseQuery()
      .where('version.originalToolId = :originalToolId', { originalToolId })
      .andWhere('version.userId = :userId', { userId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }
}
