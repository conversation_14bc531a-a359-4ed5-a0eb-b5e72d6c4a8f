import { Injectable } from '@nestjs/common';

/**
 * Helper x<PERSON> lý việc xác thực các tool
 */
@Injectable()
export class ToolValidationHelper {
  /**
   * <PERSON><PERSON><PERSON> thực danh sách ID tool của người dùng
   * @param toolIds Danh sách ID tool cần xác thực
   * @param userId ID của người dùng
   * @returns Kết quả xác thực
   */
  async validateUserToolIds(
    toolIds: string[],
    userId?: number
  ): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    // <PERSON><PERSON><PERSON> lập kết quả xác thực
    return {
      valid: true,
      existingIds: toolIds,
      nonExistingIds: []
    };
  }
}
