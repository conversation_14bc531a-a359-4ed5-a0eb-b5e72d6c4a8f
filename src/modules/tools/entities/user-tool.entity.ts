import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { UserToolVersion } from './user-tool-version.entity';

/**
 * Entity đại diện cho bảng user_tools trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin cơ bản của các tool do người dùng tạo hoặc sao chép từ admin
 */
@Entity('user_tools')
export class UserTool {
  /**
   * ID duy nhất của tool, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên của tool, dùng để nhận diện
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về chức năng và mục đích của tool
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Thời điểm tạo tool, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * ID của người dùng sở hữu tool, tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID của tool gốc từ admin (nếu là tool sao chép), tham chiếu đến bảng admin_tools
   */
  @Column({ name: 'original_id', type: 'uuid', nullable: true })
  originalId: string | null;

  /**
   * Có bản cập nhật mới
   */
  @Column({ name: 'has_update', default: false })
  hasUpdate: boolean;

  /**
   * Trạng thái của tool: DRAFT, APPROVED, DEPRECATED
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.APPROVED
  })
  status: ToolStatusEnum;

  /**
   * Trạng thái hoạt động của tool
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;
  
  /**
   * Cờ xác định tool có phải là bản cập nhật hay không
   */
  @Column({ name: 'is_update', type: 'boolean', default: false })
  isUpdate: boolean;
}
