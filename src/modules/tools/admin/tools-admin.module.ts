import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion,
  AdminGroupTool,
  AdminGroupToolMapping,
  AdminGroupToolsTypeAgent,
  UserGroupToolMapping
} from '../entities';
import { TypeAgent } from '../../agent/entities/type-agent.entity';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  AdminGroupToolRepository,
  AdminGroupToolMappingRepository,
  AdminGroupToolsTypeAgentRepository,
  TypeAgentRepository,
  UserGroupToolMappingRepository,
} from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { AdminToolService, AdminToolVersionService, AdminGroupToolService } from './services';
import { AdminToolController, AdminToolVersionController, AdminGroupToolController } from './controllers';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';

@Module({
  imports: [TypeOrmModule.forFeature([
    AdminTool,
    UserTool,
    AdminToolVersion,
    UserToolVersion,
    AdminGroupTool,
    AdminGroupToolMapping,
    AdminGroupToolsTypeAgent,
    UserGroupToolMapping,
    TypeAgent
  ])],
  controllers: [
    // Đăng ký AdminGroupToolController trước để đảm bảo route /admin/tools/groups được ưu tiên
    AdminGroupToolController,
    AdminToolVersionController,
    AdminToolController
  ],
  providers: [
    AdminToolService,
    AdminToolVersionService,
    AdminGroupToolService,
    AdminToolRepository,
    UserToolRepository,
    AdminToolVersionRepository,
    UserToolVersionRepository,
    AdminGroupToolRepository,
    AdminGroupToolMappingRepository,
    AdminGroupToolsTypeAgentRepository,
    TypeAgentRepository,
    UserGroupToolMappingRepository,
    EmployeeInfoRepository,
    S3Service
  ],
  exports: [
    AdminToolService,
    AdminToolVersionService,
    AdminGroupToolService
  ],
})
export class ToolsAdminModule {}
