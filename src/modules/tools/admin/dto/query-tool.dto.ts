import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto';
import { ToolStatusEnum } from '@/modules/tools/constants';
import { AccessTypeEnum } from '@/modules/tools/constants';

/**
 * Enum cho các trường sắp xếp tool
 */
export enum ToolSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách tool
 */
export class QueryToolDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái (chỉ cho phép DRAFT hoặc APPROVED)',
    enum: [ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED],
    required: false,
  })
  @IsEnum([ToolStatusEnum.DRAFT, ToolStatusEnum.APPROVED], {
    message: 'status chỉ có thể là DRAFT hoặc APPROVED'
  })
  @IsOptional()
  status?: ToolStatusEnum;

  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo loại quyền truy cập',
    enum: AccessTypeEnum,
    required: false,
  })
  @IsEnum(AccessTypeEnum)
  @IsOptional()
  accessType?: AccessTypeEnum;

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: ToolSortBy,
    default: ToolSortBy.CREATED_AT,
    required: false,
  })
  @IsEnum(ToolSortBy)
  @IsOptional()
  sortBy: ToolSortBy = ToolSortBy.CREATED_AT;
}
