import { Injectable, Logger } from '@nestjs/common';
import { In } from 'typeorm';
import { AdminGroupToolRepository } from '@/modules/tools/repositories';
import { AdminGroupToolsTypeAgentRepository } from '@/modules/tools/repositories';
import { TypeAgentRepository } from '@/modules/tools/repositories';
import { AdminToolRepository } from '@/modules/tools/repositories';
import { AdminGroupToolMappingRepository } from '@/modules/tools/repositories';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';
import { CreateAdminGroupToolDto, UpdateAdminGroupToolDto, EmployeeInfoDto } from '../dto';
import { AdminGroupTool } from '@/modules/tools/entities';
import { AdminGroupToolsTypeAgent } from '@/modules/tools/entities';
import { AdminGroupToolMapping } from '@/modules/tools/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '@/modules/tools/exceptions';

@Injectable()
export class AdminGroupToolService {
  private readonly logger = new Logger(AdminGroupToolService.name);

  constructor(
    private readonly adminGroupToolRepository: AdminGroupToolRepository,
    private readonly adminGroupToolsTypeAgentRepository: AdminGroupToolsTypeAgentRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminGroupToolMappingRepository: AdminGroupToolMappingRepository,
    private readonly employeeInfoRepository: EmployeeInfoRepository,
  ) {}

  /**
   * Tạo mới nhóm tool
   * @param dto Thông tin nhóm tool cần tạo
   * @param employeeId ID của nhân viên thực hiện tạo
   * @returns Nhóm tool đã được tạo
   */
  @Transactional()
  async createGroupTool(
    dto: CreateAdminGroupToolDto,
    employeeId: number,
  ): Promise<AdminGroupTool> {
    try {
      // Kiểm tra xem loại agent có tồn tại không (nếu có)
      if (dto.typeAgentId) {
        const typeAgent = await this.typeAgentRepository.findTypeAgentById(dto.typeAgentId);
        if (!typeAgent) {
          throw new AppException(TOOLS_ERROR_CODES.TYPE_AGENT_NOT_FOUND, `Không tìm thấy loại agent với ID ${dto.typeAgentId}`);
        }
      }

      // Kiểm tra xem tên nhóm tool đã tồn tại chưa
      const existingGroup = await this.adminGroupToolRepository.findGroupByName(dto.name);
      if (existingGroup) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NAME_EXISTS, `Nhóm tool với tên "${dto.name}" đã tồn tại`);
      }

      // Kiểm tra danh sách tool nếu có
      if (dto.toolIds && dto.toolIds.length > 0) {
        // Kiểm tra xem các tool có tồn tại không
        const tools = await this.adminToolRepository.find({
          where: { id: In(dto.toolIds) }
        });
        if (tools.length !== dto.toolIds.length) {
          // Tìm các ID không tồn tại
          const foundIds = tools.map(tool => tool.id);
          const notFoundIds = dto.toolIds.filter(id => !foundIds.includes(id));
          throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, `Không tìm thấy các tool với ID: ${notFoundIds.join(', ')}`);
        }
      }

      // Tạo mới nhóm tool
      const groupTool = new AdminGroupTool();
      groupTool.name = dto.name;
      groupTool.description = dto.description || null;
      groupTool.createdBy = employeeId;
      groupTool.updatedBy = employeeId;

      // Lưu nhóm tool
      const savedGroupTool = await this.adminGroupToolRepository.save(groupTool);

      // Tạo liên kết với loại agent (nếu có)
      if (dto.typeAgentId) {
        const groupToolTypeAgent = new AdminGroupToolsTypeAgent();
        groupToolTypeAgent.groupId = savedGroupTool.id;
        groupToolTypeAgent.typeAgentId = dto.typeAgentId;
        await this.adminGroupToolsTypeAgentRepository.save(groupToolTypeAgent);
      }

      // Tạo liên kết với các tool (nếu có)
      if (dto.toolIds && dto.toolIds.length > 0) {
        const mappings = dto.toolIds.map(toolId => {
          const mapping = new AdminGroupToolMapping();
          mapping.groupId = savedGroupTool.id;
          mapping.toolId = toolId;
          return mapping;
        });
        await this.adminGroupToolMappingRepository.save(mappings);
      }

      return savedGroupTool;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo nhóm tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_CREATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật thông tin nhóm tool
   * @param id ID của nhóm tool cần cập nhật
   * @param dto Thông tin cần cập nhật
   * @param employeeId ID của nhân viên thực hiện cập nhật
   * @returns Nhóm tool đã được cập nhật
   */
  @Transactional()
  async updateGroupTool(
    id: number,
    dto: UpdateAdminGroupToolDto,
    employeeId: number,
  ): Promise<AdminGroupTool> {
    try {
      // Kiểm tra xem nhóm tool có tồn tại không
      const groupTool = await this.adminGroupToolRepository.findGroupById(id);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND, `Không tìm thấy nhóm tool với ID ${id}`);
      }

      // Kiểm tra xem loại agent có tồn tại không (nếu có cập nhật)
      if (dto.typeAgentId) {
        const typeAgent = await this.typeAgentRepository.findTypeAgentById(dto.typeAgentId);
        if (!typeAgent) {
          throw new AppException(TOOLS_ERROR_CODES.TYPE_AGENT_NOT_FOUND, `Không tìm thấy loại agent với ID ${dto.typeAgentId}`);
        }

        // Cập nhật liên kết với loại agent
        const existingMapping = await this.adminGroupToolsTypeAgentRepository.findByGroupIdAndTypeAgentId(id, dto.typeAgentId);
        if (!existingMapping) {
          // Xóa các liên kết cũ
          await this.adminGroupToolsTypeAgentRepository.deleteByGroupId(id);

          // Tạo liên kết mới
          const groupToolTypeAgent = new AdminGroupToolsTypeAgent();
          groupToolTypeAgent.groupId = id;
          groupToolTypeAgent.typeAgentId = dto.typeAgentId;
          await this.adminGroupToolsTypeAgentRepository.save(groupToolTypeAgent);
        }
      }

      // Kiểm tra danh sách tool nếu có cập nhật
      if (dto.toolIds && dto.toolIds.length > 0) {
        // Kiểm tra xem các tool có tồn tại không
        const tools = await this.adminToolRepository.find({
          where: { id: In(dto.toolIds) }
        });
        if (tools.length !== dto.toolIds.length) {
          // Tìm các ID không tồn tại
          const foundIds = tools.map(tool => tool.id);
          const notFoundIds = dto.toolIds.filter(id => !foundIds.includes(id));
          throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, `Không tìm thấy các tool với ID: ${notFoundIds.join(', ')}`);
        }

        // Xóa các liên kết cũ
        await this.adminGroupToolMappingRepository.deleteByGroupId(id);

        // Tạo các liên kết mới
        const mappings = dto.toolIds.map(toolId => {
          const mapping = new AdminGroupToolMapping();
          mapping.groupId = id;
          mapping.toolId = toolId;
          return mapping;
        });
        await this.adminGroupToolMappingRepository.save(mappings);
      }

      // Kiểm tra xem tên nhóm tool đã tồn tại chưa (nếu có cập nhật)
      if (dto.name && dto.name !== groupTool.name) {
        const existingGroup = await this.adminGroupToolRepository.findGroupByName(dto.name);
        if (existingGroup && existingGroup.id !== id) {
          throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NAME_EXISTS, `Nhóm tool với tên "${dto.name}" đã tồn tại`);
        }
        groupTool.name = dto.name;
      }

      // Cập nhật các trường khác
      if (dto.description !== undefined) {
        groupTool.description = dto.description;
      }

      groupTool.updatedBy = employeeId;
      groupTool.updatedAt = Date.now();

      return this.adminGroupToolRepository.save(groupTool);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật nhóm tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa nhóm tool
   * @param id ID của nhóm tool cần xóa
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async deleteGroupTool(id: number): Promise<boolean> {
    try {
      // Kiểm tra xem nhóm tool có tồn tại không
      const groupTool = await this.adminGroupToolRepository.findGroupById(id);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND, `Không tìm thấy nhóm tool với ID ${id}`);
      }

      // Xóa các liên kết với loại agent
      await this.adminGroupToolsTypeAgentRepository.deleteByGroupId(id);

      // Xóa các liên kết với tool
      await this.adminGroupToolMappingRepository.deleteByGroupId(id);

      // Xóa nhóm tool
      await this.adminGroupToolRepository.remove(groupTool);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhóm tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_DELETE_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết của nhóm tool
   * @param id ID của nhóm tool cần lấy thông tin
   * @returns Thông tin chi tiết của nhóm tool
   */
  async getGroupToolById(id: number): Promise<AdminGroupTool & {
    tools?: any[],
    typeAgentInfo?: {
      id: number | null,
      name: string | null
    },
    createdByInfo?: EmployeeInfoDto | null,
    updatedByInfo?: EmployeeInfoDto | null
  }> {
    try {
      const groupTool = await this.adminGroupToolRepository.findGroupById(id);
      if (!groupTool) {
        throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_NOT_FOUND, `Không tìm thấy nhóm tool với ID ${id}`);
      }

      // Lấy danh sách tool trong nhóm
      const toolMappings = await this.adminGroupToolMappingRepository.findByGroupId(id);

      // Nếu có tool mappings, lấy thông tin tool từ quan hệ đã được thiết lập
      let tools: any[] = [];
      if (toolMappings && toolMappings.length > 0) {
        // Lấy tool từ quan hệ nếu có, nếu không thì lấy theo ID
        const toolIds = toolMappings.map(mapping => mapping.toolId);
        if (toolIds.length > 0) {
          tools = await this.adminToolRepository.find({
            where: { id: In(toolIds) }
          });
        }
      }

      // Thêm thông tin về loại agent nếu có
      let typeAgentInfo = {
        id: null as number | null,
        name: null as string | null
      };

      try {
        // Lấy thông tin về loại agent liên kết với nhóm tool
        const typeAgentMappings = await this.adminGroupToolsTypeAgentRepository.findByGroupId(id);

        if (typeAgentMappings && typeAgentMappings.length > 0) {
          const mappingTypeAgentId = typeAgentMappings[0].typeAgentId;
          // Lấy thông tin chi tiết về loại agent
          const typeAgent = await this.typeAgentRepository.findTypeAgentById(mappingTypeAgentId);
          if (typeAgent) {
            typeAgentInfo.id = typeAgent.id;
            typeAgentInfo.name = typeAgent.name;
          }
        }
      } catch (error) {
        // Bỏ qua lỗi liên quan đến typeAgent và tiếp tục với giá trị mặc định
        this.logger.warn(`Không thể lấy thông tin type agent cho group tool ${id}: ${error.message}`);
        // Giữ nguyên giá trị mặc định cho typeAgentInfo
      }

      // Lấy thông tin người tạo và người cập nhật
      let createdByInfo: EmployeeInfoDto | null = null;
      let updatedByInfo: EmployeeInfoDto | null = null;

      try {
        if (groupTool.createdBy) {
          createdByInfo = await this.employeeInfoRepository.getEmployeeInfo(groupTool.createdBy);
        }

        if (groupTool.updatedBy) {
          updatedByInfo = await this.employeeInfoRepository.getEmployeeInfo(groupTool.updatedBy);
        }
      } catch (error) {
        this.logger.warn(`Không thể lấy thông tin employee cho group tool ${id}: ${error.message}`);
      }

      // Tạo đối tượng kết quả với các trường bổ sung
      return {
        ...groupTool,
        tools,
        typeAgentInfo,
        createdByInfo,
        updatedByInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin nhóm tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.GROUP_TOOL_FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách nhóm tool với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách nhóm tool với phân trang
   */
  async getGroupTools(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AdminGroupTool>> {
    return this.adminGroupToolRepository.findGroups(
      page,
      limit,
      search,
      sortBy,
      sortDirection,
    );
  }


}
