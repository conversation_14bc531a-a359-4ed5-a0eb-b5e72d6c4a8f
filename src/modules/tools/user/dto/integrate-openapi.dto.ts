import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, ValidateIf, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IsOpenApiSpec } from '../validators/openapi-spec.validator';

/**
 * Enum định nghĩa các loại route
 */
export enum RouteType {
  TOOL = "TOOL",
  RESOURCE = "RESOURCE"
}

/**
 * Interface cho việc ánh xạ route
 */
export interface RouteMap {
  methods: string[];           // Danh sách các phương thức HTTP (GET, POST, v.v.)
  pattern: RegExp | string;    // Mẫu để khớp với đường dẫn (chuỗi hoặc RegExp)
  route_type: RouteType;       // Loại route (TOOL hoặc RESOURCE)
}

/**
 * DTO cho việc tích hợp OpenAPI
 */
export class IntegrateOpenApiDto {
  /**
   * Đặc tả OpenAPI
   */
  @IsNotEmpty({ message: 'Đặc tả OpenAPI không được để trống' })
  @IsObject({ message: 'Đặc tả OpenAPI phải là một đối tượng hợp lệ' })
  @IsOpenApiSpec({ message: 'Đặc tả OpenAPI không hợp lệ. Phải là đặc tả OpenAPI 3.x.x với ít nhất một path và một phương thức HTTP' })
  @ApiProperty({
    description: 'Đặc tả OpenAPI',
    example: {
      openapi: '3.0.0',
      paths: {
        '/users': {
          get: { summary: 'Lấy danh sách người dùng' },
          post: { summary: 'Tạo người dùng mới' }
        }
      }
    }
  })
  openapiSpec: Record<string, any>;

  /**
   * Base URL cho API
   */
  @IsOptional()
  @ApiProperty({
    description: 'Base URL cho API (nếu không cung cấp, sẽ sử dụng từ đặc tả OpenAPI hoặc giá trị mặc định)',
    example: 'https://api.example.com',
    required: false
  })
  baseUrl?: string;

  // customMappings đã được cấu hình ngầm trong hệ thống
}

/**
 * DTO cho kết quả tích hợp OpenAPI
 */
export class IntegrateOpenApiResponseDto {
  /**
   * Số lượng công cụ đã tạo
   */
  @ApiProperty({
    description: 'Số lượng công cụ đã tạo',
    example: 5
  })
  toolsCreated: number;

  /**
   * Số lượng tài nguyên đã tạo
   */
  @ApiProperty({
    description: 'Số lượng tài nguyên đã tạo',
    example: 3
  })
  resourcesCreated: number;
}
