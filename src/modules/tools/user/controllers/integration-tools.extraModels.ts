import { IntegrateFromOpenApiDto, DirectOpenApiInputDto } from '../dto/integrate-from-openapi.dto';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của tool
 */
export class ToolResponseDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Get Users'
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiProperty({
    description: 'Mô tả của tool',
    example: '<PERSON><PERSON>y danh sách người dùng'
  })
  description: string;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1625097600000
  })
  updatedAt: number;
}

/**
 * DTO cho response chi tiết của tool
 */
export class ToolDetailResponseDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Get Users'
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiProperty({
    description: 'Mô tả của tool',
    example: 'Lấy danh sách người dùng'
  })
  description: string;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1625097600000
  })
  updatedAt: number;

  /**
   * Thông tin phiên bản
   */
  @ApiProperty({
    description: 'Thông tin phiên bản',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      versionNumber: 1,
      toolName: 'getUsers',
      parameters: {}
    }
  })
  version: any;
}

// Export các DTO để Swagger có thể tìm thấy
export const extraModels = [
  IntegrateFromOpenApiDto,
  DirectOpenApiInputDto,
  ToolResponseDto,
  ToolDetailResponseDto
];
