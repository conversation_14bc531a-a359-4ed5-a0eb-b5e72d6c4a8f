import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho query params khi lấy danh sách giao dịch
 */
export class TransactionQueryDto {
  @ApiProperty({
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Số trang phải là số' })
  @Min(1, { message: 'Số trang phải lớn hơn hoặc bằng 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Số lượng item trên mỗi trang phải là số' })
  @Min(1, { message: 'Số lượng item trên mỗi trang phải lớn hơn hoặc bằng 1' })
  limit?: number = 10;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  userId?: number;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(TransactionStatus, { message: 'Trạng thái giao dịch không hợp lệ' })
  status?: TransactionStatus;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm (tìm theo mã giao dịch, mã tham chiếu)',
    example: 'VNP123456',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  keyword?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600000,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Thời gian bắt đầu phải là số' })
  startTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1625184000000,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Thời gian kết thúc phải là số' })
  endTime?: number;
}
