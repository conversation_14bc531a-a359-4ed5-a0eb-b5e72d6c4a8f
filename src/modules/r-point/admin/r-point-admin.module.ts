import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as services from './services';
import * as controllers from './controllers';
import { RPointUserModule } from '../user/r-point-user.module';

/**
 * Module quản lý R-Point cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities)),
    RPointUserModule,
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class RPointAdminModule {}
