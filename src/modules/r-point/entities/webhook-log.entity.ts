import { Column, Entity, PrimaryColumn } from 'typeorm';
import { TransactionType } from '@modules/r-point/enums';
import { WebhookStatus } from '@modules/r-point/enums';

/**
 * Entity đại diện cho bảng webhook_logs trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các webhook từ cổng thanh toán
 */
@Entity('webhook_logs')
export class WebhookLog {
  /**
   * ID của webhook log (UUID)
   */
  @PrimaryColumn({ name: 'id', type: 'uuid', comment: 'Định danh log webhook' })
  id: string;

  /**
   * ID của giao dịch liên quan
   */
  @Column({ name: 'transaction_id', type: 'bigint', nullable: true, comment: 'Định danh giao dịch' })
  transactionId: number;

  /**
   * Loại giao dịch
   */
  @Column({ name: 'transaction_type', type: 'enum', enum: TransactionType, nullable: false, comment: 'Loạ<PERSON> giao dịch (POINT_PURCHASE, POINT_WITHDRAWAL)' })
  transactionType: TransactionType;

  /**
   * Nguồn webhook
   */
  @Column({ name: 'source', length: 50, nullable: false, comment: 'Nguồn webhook' })
  source: string;

  /**
   * Dữ liệu webhook
   */
  @Column({ name: 'payload', type: 'jsonb', nullable: false, comment: 'Dữ liệu webhook' })
  payload: any;

  /**
   * Trạng thái xử lý webhook
   */
  @Column({ name: 'status', type: 'enum', enum: WebhookStatus, default: WebhookStatus.PROCESSING, comment: 'Trạng thái xử lý webhook (SUCCESS, FAILED, PROCESSING)' })
  status: WebhookStatus;

  /**
   * Mã phản hồi
   */
  @Column({ name: 'response_code', length: 20, nullable: true, comment: 'Mã phản hồi' })
  responseCode: string;

  /**
   * Thông điệp phản hồi
   */
  @Column({ name: 'response_message', type: 'text', nullable: true, comment: 'Thông điệp phản hồi' })
  responseMessage: string;

  /**
   * Thời gian xử lý
   */
  @Column({ name: 'processed_at', type: 'bigint', nullable: true, comment: 'Thời gian xử lý (Unix timestamp)' })
  processedAt: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo (Unix timestamp)' })
  createdAt: number;
}
