import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho việc trả về thông tin chi tiết thanh toán
 */
export class PaymentInfoResponseDto {
  @ApiProperty({
    description: 'ID của giao dịch',
    example: 123456
  })
  transactionId: number;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB'
  })
  bankCode: string;

  @ApiProperty({
    description: 'Tên ngân hàng',
    example: 'Vietcombank'
  })
  bankName: string;

  @ApiProperty({
    description: 'Đường dẫn logo ngân hàng',
    example: 'https://cdn.redai.vn/images/banks/vcb-logo.png'
  })
  logoPath?: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'CÔNG TY CỔ PHẦN REDAI'
  })
  accountHolder: string;

  @ApiProperty({
    description: 'Số tiền cần thanh toán',
    example: 100000
  })
  amount: number;

  @ApiProperty({
    description: 'Số lượng R-Point mua',
    example: 100
  })
  pointsAmount: number;

  @ApiProperty({
    description: 'Tên gói R-Point',
    example: 'Gói 100k'
  })
  pointName: string;

  @ApiProperty({
    description: 'Nội dung chuyển khoản',
    example: 'REDAI123456SEPAY'
  })
  description: string;

  @ApiProperty({
    description: 'URL QR code thanh toán',
    example: 'https://qr.sepay.vn/img?bank=VCB&acc=**********&template=compact&amount=100000.00&des=REDAI123456SEPAY'
  })
  qrCodeUrl: string;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    example: TransactionStatus.PENDING
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'Thời gian tạo giao dịch',
    example: *************
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật giao dịch',
    example: *************
  })
  updatedAt: number;
}
