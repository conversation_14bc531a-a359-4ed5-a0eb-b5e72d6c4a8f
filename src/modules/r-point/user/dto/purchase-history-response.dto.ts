import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho response trả về thông tin gói point trong lịch sử mua
 */
export class PurchaseHistoryPointDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k'
  })
  name: string;
}

/**
 * DTO cho response trả về lịch sử mua point
 */
export class PurchaseHistoryResponseDto {
  @ApiProperty({
    description: 'ID của giao dịch',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Số tiền giao dịch (VND)',
    example: 100000
  })
  amount: number;

  @ApiProperty({
    description: 'Số lượng point mua',
    example: 100
  })
  pointsAmount: number;

  @ApiProperty({
    description: 'Tên loại point',
    example: 'Gói 100k',
    required: false
  })
  pointName?: string;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    example: TransactionStatus.CONFIRMED
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'Số tiền giảm giá từ coupon (nếu có)',
    example: 10000,
    required: false
  })
  couponAmount?: number;

  @ApiProperty({
    description: 'Thời gian hoàn thành giao dịch (Unix timestamp)',
    example: 1625097660000,
    required: false
  })
  completedAt?: number;
}
