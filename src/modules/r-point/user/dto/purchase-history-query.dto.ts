import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho query params khi lấy lịch sử mua point
 */
export class PurchaseHistoryQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(TransactionStatus)
  status?: TransactionStatus;
}
