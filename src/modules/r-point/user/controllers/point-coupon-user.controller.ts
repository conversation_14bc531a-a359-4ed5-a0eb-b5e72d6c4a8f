import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CouponUserService } from '@modules/r-point/user/services';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApplyCouponRequestDto, ApplyCouponResponseDto } from '../dto';
import { ApiResponseDto } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý các API liên quan đến áp dụng coupon cho point
 */
@ApiTags('R-Point - User Points')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/r-point/points')
export class PointCouponUserController {
  constructor(
    private readonly couponUserService: CouponUserService
  ) {}

  /**
   * Áp dụng mã giảm giá cho đơn hàng mua point
   * @param dto Thông tin về mã coupon, gói point và số lượng point
   * @param user Thông tin người dùng hiện tại
   * @returns Thông tin chi tiết về đơn hàng sau khi áp dụng mã giảm giá
   */
  @Post('apply-coupon')
  @ApiOperation({ summary: 'Áp dụng mã giảm giá cho đơn hàng mua point' })
  @ApiResponse({ status: 200, description: 'Thông tin chi tiết về đơn hàng sau khi áp dụng mã giảm giá', type: ApplyCouponResponseDto })
  async applyCoupon(
    @Body() dto: ApplyCouponRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<ApplyCouponResponseDto>> {
    const result = await this.couponUserService.applyCoupon(dto, user.id);
    return ApiResponseDto.success(result);
  }
}
