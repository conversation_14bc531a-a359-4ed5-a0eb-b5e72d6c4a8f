import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionUserService } from '@modules/r-point/user/services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';

@ApiTags('R-Point - User Transactions')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/r-point/transactions')
export class TransactionUserController {
  constructor(
    private readonly transactionUserService: TransactionUserService,
  ) {}
}
