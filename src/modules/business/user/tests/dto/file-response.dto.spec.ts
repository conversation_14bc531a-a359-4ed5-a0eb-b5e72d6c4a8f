import { plainToInstance } from 'class-transformer';
import { FileResponseDto } from '../../dto/file/file-response.dto';

describe('FileResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      id: 1,
      warehouseId: 2,
      name: '<PERSON>ài liệu hướng dẫn.pdf',
      folderId: 3,
      storageKey: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
      size: 1024000,
      viewUrl: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
      uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.warehouseId).toBe(2);
    expect(dto.name).toBe('Tài liệu hướng dẫn.pdf');
    expect(dto.folderId).toBe(3);
    expect(dto.storageKey).toBe('files/2023/05/document-1625097600000-abcdef123456.pdf');
    expect(dto.size).toBe(1024000);
    expect(dto.viewUrl).toBe('https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf');
    expect(dto.uploadUrl).toBe('https://storage.example.com/presigned-url?token=abc123');
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625097600000);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với size là null', () => {
    // Arrange
    const plainData = {
      id: 1,
      warehouseId: 2,
      name: 'Tài liệu hướng dẫn.pdf',
      folderId: 3,
      storageKey: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
      size: null,
      viewUrl: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.size).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO không có uploadUrl', () => {
    // Arrange
    const plainData = {
      id: 1,
      warehouseId: 2,
      name: 'Tài liệu hướng dẫn.pdf',
      folderId: 3,
      storageKey: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
      size: 1024000,
      viewUrl: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.uploadUrl).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với folderId là null', () => {
    // Arrange
    const plainData = {
      id: 1,
      warehouseId: 2,
      name: 'Tài liệu hướng dẫn.pdf',
      folderId: null,
      storageKey: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
      size: 1024000,
      viewUrl: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.folderId).toBeNull();
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        id: 1,
        warehouseId: 2,
        name: 'Tài liệu 1.pdf',
        folderId: 3,
        storageKey: 'files/2023/05/document1.pdf',
        size: 1024000,
        viewUrl: 'https://cdn.example.com/files/document1.pdf',
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      },
      {
        id: 2,
        warehouseId: 2,
        name: 'Tài liệu 2.pdf',
        folderId: 3,
        storageKey: 'files/2023/05/document2.pdf',
        size: 2048000,
        viewUrl: 'https://cdn.example.com/files/document2.pdf',
        createdAt: 1625097700000,
        updatedAt: 1625097700000,
      },
    ];

    // Act
    const dtoArray = plainToInstance(FileResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(FileResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(FileResponseDto);
    expect(dtoArray[0].id).toBe(1);
    expect(dtoArray[1].id).toBe(2);
    expect(dtoArray[0].name).toBe('Tài liệu 1.pdf');
    expect(dtoArray[1].name).toBe('Tài liệu 2.pdf');
  });
});