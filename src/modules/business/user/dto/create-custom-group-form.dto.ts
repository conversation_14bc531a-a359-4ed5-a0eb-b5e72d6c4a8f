import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * D<PERSON> cho request tạo nhóm trường tùy chỉnh
 */
export class CreateCustomGroupFormDto {
  /**
   * Nhãn hiển thị
   * @example "Thông tin bổ sung"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin bổ sung',
    maxLength: 255,
    required: true,
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label: string;



  // Trường userId sẽ được lấy từ thông tin người dùng đăng nhập
  // và được gán trong controller
  userId?: number;
}
