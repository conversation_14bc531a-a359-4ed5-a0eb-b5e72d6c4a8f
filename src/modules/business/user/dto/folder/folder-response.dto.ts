import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho phản hồi thông tin thư mục
 */
export class FolderResponseDto {
  /**
   * ID của thư mục
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của thư mục',
    example: 1,
  })
  id: number;

  /**
   * Tên thư mục
   * @example "Tài liệu dự án"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Tài liệu dự án',
  })
  name: string;

  /**
   * ID thư mục cha (null nếu là thư mục gốc)
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID thư mục cha (null nếu là thư mục gốc)',
    example: 1,
    nullable: true,
  })
  parentId: number | null;

  /**
   * ID người dùng sở hữu
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID người dùng sở hữu',
    example: 1,
  })
  userId: number;

  /**
   * Đường dẫn thư mục
   * @example "/Tài liệu/Dự án"
   */
  @Expose()
  @ApiProperty({
    description: 'Đường dẫn thư mục',
    example: '/Tài liệu/Dự án',
    nullable: true,
  })
  path: string | null;

  /**
   * ID kho ảo gốc
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID kho ảo gốc',
    example: 1,
    nullable: true,
  })
  root: number | null;

  /**
   * Thời gian tạo (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625097600000,
  })
  updatedAt: number;
}
