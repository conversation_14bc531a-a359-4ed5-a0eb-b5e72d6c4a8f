import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo trường tùy chỉnh của kho
 */
export class CreateWarehouseCustomFieldDto {
  /**
   * ID của trường tùy chỉnh
   * @example 2
   */
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 2,
  })
  @IsNotEmpty({ message: 'ID trường tùy chỉnh không được để trống' })
  @IsNumber({}, { message: 'ID trường tùy chỉnh phải là số' })
  @Type(() => Number)
  fieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   * @example { "value": "Giá trị mẫu" }
   */
  @ApiProperty({
    description: '<PERSON>i<PERSON> trị của trường tùy chỉnh',
    example: { value: '<PERSON>i<PERSON> trị mẫu' },
  })
  @IsNotEmpty({ message: 'Gi<PERSON> trị trường tùy chỉnh không được để trống' })
  @IsObject({ message: 'Giá trị trường tùy chỉnh phải là đối tượng JSON' })
  value: any;
}
