import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho các tham số truy vấn danh sách trường tùy chỉnh
 */
export class QueryCustomFieldDto extends QueryDto {

  /**
   * ID người dùng để lọc
   * @example 1001
   */
  @ApiProperty({
    description: 'ID người dùng để lọc',
    example: 1001,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  /**
   * ID nhân viên để lọc
   * @example 101
   */
  @ApiProperty({
    description: 'ID nhân viên để lọc',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  employeeId?: number;

  /**
   * <PERSON>ại trường để lọc
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường để lọc',
    example: 'text',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;
}
