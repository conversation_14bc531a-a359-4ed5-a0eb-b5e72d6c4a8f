import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ValidationHelper } from '../helpers/validation.helper';
import { WarehouseCustomFieldRepository } from '@modules/business/repositories/warehouse-custom-field.repository';
import { CreateWarehouseCustomFieldDto } from '@modules/business/user/dto/warehouse';
import { WarehouseCustomFieldResponseDto } from '@modules/business/user/dto/warehouse';
import { UpdateWarehouseCustomFieldDto } from '@modules/business/user/dto/warehouse';


/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh của kho
 */
@Injectable()
export class UserWarehouseCustomFieldService {
  private readonly logger = new Logger(UserWarehouseCustomFieldService.name);

  constructor(
    private readonly warehouseCustomFieldRepository: WarehouseCustomFieldRepository,
    private readonly validationHelper: ValidationHelper
  ) {}

  /**
   * Thêm trường tùy chỉnh cho kho
   * @param warehouseId ID của kho
   * @param createDto DTO chứa thông tin trường tùy chỉnh
   * @param userId ID của người dùng
   * @returns Thông tin trường tùy chỉnh đã thêm
   */
  @Transactional()
  async addCustomField(
    warehouseId: number,
    createDto: CreateWarehouseCustomFieldDto,
    userId: number
  ): Promise<WarehouseCustomFieldResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào thông qua validation helper
      await this.validationHelper.validateCreateWarehouseCustomField(warehouseId, createDto);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by
      await this.validationHelper.validateWarehouseExists(warehouseId);

      // Sử dụng repository để tạo trường tùy chỉnh mới
      const savedCustomField = await this.warehouseCustomFieldRepository.createCustomField(
        warehouseId,
        createDto.fieldId,
        createDto.value
      );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseCustomFieldResponseDto, savedCustomField, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi thêm trường tùy chỉnh cho kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_CREATION_FAILED,
        `Lỗi khi thêm trường tùy chỉnh cho kho: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async updateCustomField(
    warehouseId: number,
    fieldId: number,
    updateDto: UpdateWarehouseCustomFieldDto,
    userId: number
  ): Promise<WarehouseCustomFieldResponseDto> {
    try {
      // Kiểm tra trường tùy chỉnh của kho tồn tại thông qua validation helper
      await this.validationHelper.validateWarehouseCustomFieldExists(warehouseId, fieldId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by
      await this.validationHelper.validateWarehouseExists(warehouseId);

      // Sử dụng repository để cập nhật trường tùy chỉnh
      const updatedCustomField = await this.warehouseCustomFieldRepository.updateCustomField(
        warehouseId,
        fieldId,
        updateDto.value
      );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseCustomFieldResponseDto, updatedCustomField, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trường tùy chỉnh của kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_UPDATE_FAILED,
        `Lỗi khi cập nhật trường tùy chỉnh của kho: ${error.message}`
      );
    }
  }

  /**
   * Xóa trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteCustomField(warehouseId: number, fieldId: number, userId: number): Promise<void> {
    try {
      // Kiểm tra trường tùy chỉnh của kho tồn tại
      await this.validationHelper.validateWarehouseCustomFieldExists(warehouseId, fieldId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by
      await this.validationHelper.validateWarehouseExists(warehouseId);

      // Xóa trường tùy chỉnh
      await this.warehouseCustomFieldRepository.deleteByWarehouseIdAndFieldId(warehouseId, fieldId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa trường tùy chỉnh của kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_DELETION_FAILED,
        `Lỗi khi xóa trường tùy chỉnh của kho: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Danh sách trường tùy chỉnh
   */
  async getCustomFields(warehouseId: number, userId: number): Promise<WarehouseCustomFieldResponseDto[]> {
    try {
      // Kiểm tra kho tồn tại
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Lấy danh sách trường tùy chỉnh từ repository
      const customFields = await this.warehouseCustomFieldRepository.findByWarehouseId_user(warehouseId);

      // Chuyển đổi sang DTO và trả về
      return customFields.map(field =>
        plainToInstance(WarehouseCustomFieldResponseDto, field, {
          excludeExtraneousValues: true
        })
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh của kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED,
        `Lỗi khi lấy danh sách trường tùy chỉnh của kho: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin trường tùy chỉnh của kho theo ID
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param userId ID của người dùng
   * @returns Thông tin trường tùy chỉnh
   */
  async getCustomFieldById(warehouseId: number, fieldId: number, userId: number): Promise<WarehouseCustomFieldResponseDto> {
    try {
      // Kiểm tra trường tùy chỉnh của kho tồn tại thông qua validation helper
      // Validation helper sẽ gọi repository để kiểm tra
      const customField = await this.validationHelper.validateWarehouseCustomFieldExists(warehouseId, fieldId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by
      await this.validationHelper.validateWarehouseExists(warehouseId);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseCustomFieldResponseDto, customField, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin trường tùy chỉnh của kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED,
        `Lỗi khi lấy thông tin trường tùy chỉnh của kho: ${error.message}`
      );
    }
  }
}
