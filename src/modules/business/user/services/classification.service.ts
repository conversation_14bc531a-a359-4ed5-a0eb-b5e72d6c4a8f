import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserClassificationRepository,
  CustomFieldClassificationRepository,
  CustomFieldRepository,
  UserProductRepository,
} from '@modules/business/repositories';
import {
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  ClassificationCustomFieldDto,
} from '../dto';
import { UserClassification, CustomFieldClassification } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';

/**
 * Service xử lý logic nghiệp vụ cho phân loại sản phẩm
 */
@Injectable()
export class ClassificationService {
  private readonly logger = new Logger(ClassificationService.name);

  constructor(
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly customFieldClassificationRepository: CustomFieldClassificationRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductRepository: UserProductRepository,
  ) {}

  /**
   * Tạo phân loại mới cho sản phẩm
   * @param productId ID của sản phẩm
   * @param createDto DTO chứa thông tin phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã tạo
   */
  @Transactional()
  async create(
    productId: number,
    createDto: CreateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Tạo phân loại mới cho sản phẩm ${productId}, userId=${userId}`);

      // Xử lý các trường tùy chỉnh nếu có
      const customFieldsData: ClassificationCustomFieldDto[] = [];

      if (createDto.customFields && createDto.customFields.length > 0) {
        for (const fieldDto of createDto.customFields) {
          // Kiểm tra trường tùy chỉnh tồn tại
          const customField = await this.customFieldRepository.findById(fieldDto.customFieldId);
          if (!customField) {
            this.logger.warn(`Không tìm thấy trường tùy chỉnh với ID ${fieldDto.customFieldId}`);
            throw new AppException(
              BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
              `Không tìm thấy trường tùy chỉnh với ID ${fieldDto.customFieldId}. Vui lòng kiểm tra lại ID của các trường tùy chỉnh trong phần phân loại.`
            );
          }

          // Thêm vào danh sách để lưu vào bảng custom_field_classifications
          customFieldsData.push(fieldDto);
        }
      }

      // Lấy thông tin sản phẩm để kiểm tra loại giá và quyền sở hữu
      const product = await this.userProductRepository.findById(productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền tạo phân loại cho sản phẩm này`,
        );
      }

      // Kiểm tra giá sản phẩm theo loại giá
      this.validateClassificationPrice(createDto.price, product.typePrice, createDto.type);

      // Tạo phân loại mới
      const classification = new UserClassification();
      classification.type = createDto.type;
      classification.price = createDto.price;
      classification.productId = productId;

      // Lưu phân loại vào database
      const savedClassification = await this.userClassificationRepository.save(classification);

      // Lưu các trường tùy chỉnh vào bảng custom_field_classifications
      const customFields: ClassificationCustomFieldDto[] = [];
      if (customFieldsData.length > 0) {
        for (const fieldDto of customFieldsData) {
          // Tạo liên kết giữa phân loại và trường tùy chỉnh
          const fieldClassification = new CustomFieldClassification();
          fieldClassification.classificationId = savedClassification.id;
          fieldClassification.customFieldId = fieldDto.customFieldId;
          fieldClassification.value = fieldDto.value;

          // Lưu vào database
          await this.customFieldClassificationRepository.save(fieldClassification);
          customFields.push(fieldDto);
        }
      }

      // Đảm bảo price có đầy đủ thông tin
      let price = savedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Trả về kết quả
      return {
        id: savedClassification.id,
        type: savedClassification.type,
        price,
        customFields,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_CREATION_FAILED,
        `Lỗi khi tạo phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật phân loại
   * @param id ID của phân loại
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền cập nhật phân loại này`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateDto.type !== undefined) {
        classification.type = updateDto.type;
      }
      if (updateDto.price !== undefined) {
        // Kiểm tra giá sản phẩm theo loại giá
        this.validateClassificationPrice(updateDto.price, product.typePrice, classification.type);
        classification.price = updateDto.price;
      }

      // Xử lý các trường tùy chỉnh nếu có
      const customFieldsData: ClassificationCustomFieldDto[] = [];

      if (updateDto.customFields && updateDto.customFields.length > 0) {
        for (const fieldDto of updateDto.customFields) {
          // Kiểm tra trường tùy chỉnh tồn tại
          const customField = await this.customFieldRepository.findById(fieldDto.customFieldId);
          if (!customField) {
            this.logger.warn(`Không tìm thấy trường tùy chỉnh với ID ${fieldDto.customFieldId}`);
            throw new AppException(
              BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
              `Không tìm thấy trường tùy chỉnh với ID ${fieldDto.customFieldId}. Vui lòng kiểm tra lại ID của các trường tùy chỉnh trong phần phân loại.`
            );
          }

          // Thêm vào danh sách để lưu vào bảng custom_field_classifications
          customFieldsData.push(fieldDto);
        }
      }

      // Lưu phân loại vào database
      const updatedClassification = await this.userClassificationRepository.save(classification);

      // Xử lý các trường tùy chỉnh trong bảng custom_field_classifications
      const customFields: ClassificationCustomFieldDto[] = [];
      if (customFieldsData.length > 0) {
        for (const fieldDto of customFieldsData) {
          // Tìm liên kết hiện có hoặc tạo mới
          let fieldClassification = await this.customFieldClassificationRepository.findByClassificationIdAndCustomFieldId(
            id,
            fieldDto.customFieldId,
          );

          if (!fieldClassification) {
            fieldClassification = new CustomFieldClassification();
            fieldClassification.classificationId = id;
            fieldClassification.customFieldId = fieldDto.customFieldId;
          }

          // Cập nhật giá trị
          fieldClassification.value = fieldDto.value;

          // Lưu vào database
          await this.customFieldClassificationRepository.save(fieldClassification);
          customFields.push(fieldDto);
        }
      }

      // Lấy tất cả các trường tùy chỉnh hiện có từ bảng custom_field_classifications
      const existingFields = await this.customFieldClassificationRepository.findByClassificationId(id);
      for (const field of existingFields) {
        // Nếu trường không có trong danh sách cập nhật, giữ nguyên
        if (!customFields.some(f => f.customFieldId === field.customFieldId)) {
          customFields.push({
            customFieldId: field.customFieldId,
            value: field.value,
          });

          // Không cần cập nhật thuộc tính customFields vì đã loại bỏ khỏi entity
        }
      }

      // Đảm bảo price có đầy đủ thông tin
      let price = updatedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        price,
        customFields,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED,
        `Lỗi khi cập nhật phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa phân loại
   * @param id ID của phân loại
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền xóa phân loại này`,
        );
      }

      // Xóa các trường tùy chỉnh liên quan
      await this.customFieldClassificationRepository.deleteByClassificationId(id);

      // Xóa phân loại
      await this.userClassificationRepository.delete(id);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_DELETION_FAILED,
        `Lỗi khi xóa phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách phân loại theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại
   */
  async getByProductId(productId: number): Promise<ClassificationResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách phân loại cho sản phẩm ${productId}`);

      // Tìm tất cả phân loại của sản phẩm
      const classifications = await this.userClassificationRepository.findByProductId_user(productId);

      // Chuyển đổi sang DTO response
      const result: ClassificationResponseDto[] = [];
      for (const classification of classifications) {
        // Lấy các trường tùy chỉnh liên quan từ bảng custom_field_classifications
        const fieldClassifications = await this.customFieldClassificationRepository.findByClassificationId(
          classification.id,
        );

        // Chuyển đổi thành DTO
        const customFields: ClassificationCustomFieldDto[] = fieldClassifications.map(fc => ({
          customFieldId: fc.customFieldId,
          value: fc.value,
        }));

        // Đảm bảo price có đầy đủ thông tin
        let price = classification.price;
        if (price && typeof price === 'object') {
          // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
          if (price.listPrice === undefined && price.value !== undefined) {
            price.listPrice = price.value;
          }
          if (price.salePrice === undefined && price.value !== undefined) {
            price.salePrice = price.value;
          }
          if (price.value === undefined && price.salePrice !== undefined) {
            price.value = price.salePrice;
          }
        }

        // Thêm vào kết quả
        result.push({
          id: classification.id,
          type: classification.type,
          price,
          customFields,
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy danh sách phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết phân loại theo ID
   * @param id ID của phân loại
   * @returns Chi tiết phân loại
   */
  async getById(id: number): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết phân loại với ID ${id}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy các trường tùy chỉnh liên quan từ bảng custom_field_classifications
      const fieldClassifications = await this.customFieldClassificationRepository.findByClassificationId(
        classification.id,
      );

      // Chuyển đổi thành DTO
      const customFields: ClassificationCustomFieldDto[] = fieldClassifications.map(fc => ({
        customFieldId: fc.customFieldId,
        value: fc.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = classification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Trả về kết quả
      return {
        id: classification.id,
        type: classification.type,
        price,
        customFields,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy chi tiết phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra giá sản phẩm theo loại giá
   * @param price Giá của phân loại
   * @param typePrice Loại giá của sản phẩm
   * @param classificationType Loại phân loại
   * @throws AppException nếu giá không hợp lệ
   */
  private validateClassificationPrice(price: any, typePrice: PriceTypeEnum, classificationType: string): void {
    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.listPrice || !price.salePrice || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ listPrice, salePrice và currency khi sản phẩm có loại giá HAS_PRICE`,
          );
        }

        // Kiểm tra giá bán phải nhỏ hơn hoặc bằng giá niêm yết
        if (price.salePrice > price.listPrice) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice) trong phân loại "${classificationType}"`,
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        // Kiểm tra có trường priceDescription không
        if (!price || !price.priceDescription) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có priceDescription khi sản phẩm có loại giá STRING_PRICE`,
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        // Kiểm tra price phải là null
        if (price !== null) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" không được có giá khi sản phẩm có loại giá NO_PRICE`,
          );
        }
        break;

      // Các loại giá khác như HOURLY, DAILY, MONTHLY, YEARLY, CONTACT
      default:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.value || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ value và currency khi sản phẩm có loại giá ${typePrice}`,
          );
        }
        break;
    }
  }
}
