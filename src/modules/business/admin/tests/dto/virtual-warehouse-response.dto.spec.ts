import { plainToInstance } from 'class-transformer';
import { VirtualWarehouseResponseDto } from '../../dto/warehouse/virtual-warehouse-response.dto';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('VirtualWarehouseResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của VirtualWarehouseResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 2,
      name: '<PERSON>ho ảo',
      description: 'Kho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL
    };

    const plainObject = {
      warehouseId: 2,
      associatedSystem: 'ERP System',
      purpose: 'Digital inventory management',
      warehouse: warehouseInfo,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.associatedSystem).toBe('ERP System');
    expect(dto.purpose).toBe('Digital inventory management');
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(2);
    expect(dto.warehouse.name).toBe('Kho ảo');
    expect(dto.warehouse.description).toBe('Kho ảo quản lý hàng hóa trực tuyến');
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.VIRTUAL);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của VirtualWarehouseResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 4,
      name: 'Kho ảo Cloud',
      type: WarehouseTypeEnum.VIRTUAL
    };

    const plainObject = {
      warehouseId: 4,
      associatedSystem: 'ERP Cloud',
      warehouse: warehouseInfo
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.warehouseId).toBe(4);
    expect(dto.associatedSystem).toBe('ERP Cloud');
    expect(dto.purpose).toBeUndefined();
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(4);
    expect(dto.warehouse.name).toBe('Kho ảo Cloud');
    expect(dto.warehouse.description).toBeUndefined();
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.VIRTUAL);
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const warehouseInfo = new WarehouseResponseDto({
      warehouseId: 2,
      name: 'Kho ảo',
      description: 'Kho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL
    });

    const data = {
      warehouseId: 2,
      associatedSystem: 'ERP System',
      purpose: 'Digital inventory management',
      warehouse: warehouseInfo
    };

    // Act
    const dto = new VirtualWarehouseResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.associatedSystem).toBe('ERP System');
    expect(dto.purpose).toBe('Digital inventory management');
    expect(dto.warehouse).toBe(warehouseInfo);
  });
});
