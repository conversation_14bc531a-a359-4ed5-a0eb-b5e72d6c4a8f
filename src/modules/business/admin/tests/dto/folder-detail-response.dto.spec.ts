import { plainToInstance } from 'class-transformer';
import { FolderDetailResponseDto, ParentFolderInfoDto } from '../../dto/folder/folder-detail-response.dto';
import { FileResponseDto } from '../../dto/file/file-response.dto';

describe('FolderDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của FolderDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Documents',
      parent: {
        id: 3,
        name: 'Root',
        path: '/'
      },
      owner: {
        id: 42,
        name: '<PERSON>',
        email: '<EMAIL>'
      },
      path: '/Root/Documents',
      root: 1,
      fileCount: 2,
      files: [
        {
          id: 1,
          name: 'document1.pdf',
          folderId: 1,
          sizeBytes: 1024000,
          createdAt: 1620000000000,
          updatedAt: 1620000000000
        },
        {
          id: 2,
          name: 'document2.pdf',
          folderId: 1,
          sizeBytes: 2048000,
          createdAt: 1620000000000,
          updatedAt: 1620000000000
        }
      ],
      createdAt: 1620000000000,
      formattedCreatedAt: '2021-05-03 10:00:00',
      updatedAt: 1620000000000,
      formattedUpdatedAt: '2021-05-03 10:00:00',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FolderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Documents');
    expect(dto.parent).toBeInstanceOf(ParentFolderInfoDto);
    // Kiểm tra các thuộc tính của parent chỉ khi parent không phải là null hoặc undefined
    if (dto.parent) {
      expect(dto.parent.id).toBe(3);
      expect(dto.parent.name).toBe('Root');
      expect(dto.parent.path).toBe('/');
    }
    expect(dto.path).toBe('/Root/Documents');
    expect(dto.root).toBe(1);
    expect(dto.fileCount).toBe(2);
    expect(Array.isArray(dto.files)).toBe(true);
    expect(dto.files.length).toBe(2);
    expect(dto.files[0]).toBeInstanceOf(FileResponseDto);
    expect(dto.files[1]).toBeInstanceOf(FileResponseDto);
    expect(dto.files[0].id).toBe(1);
    expect(dto.files[1].id).toBe(2);
    expect(dto.createdAt).toBe(1620000000000);
    expect(dto.formattedCreatedAt).toBe('2021-05-03 10:00:00');
    expect(dto.updatedAt).toBe(1620000000000);
    expect(dto.formattedUpdatedAt).toBe('2021-05-03 10:00:00');
  });

  it('nên chuyển đổi plain object với parent null thành instance của FolderDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Root',
      parent: null,
      owner: {
        id: 42,
        name: 'John Doe',
        email: '<EMAIL>'
      },
      path: '/',
      root: null,
      fileCount: 0,
      files: [],
      createdAt: 1620000000000,
      formattedCreatedAt: '2021-05-03 10:00:00',
      updatedAt: 1620000000000,
      formattedUpdatedAt: '2021-05-03 10:00:00'
    };

    // Act
    const dto = plainToInstance(FolderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Root');
    expect(dto.parent).toBeNull();
    expect(dto.path).toBe('/');
    expect(dto.root).toBeNull();
    expect(dto.fileCount).toBe(0);
    expect(Array.isArray(dto.files)).toBe(true);
    expect(dto.files.length).toBe(0);
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FolderDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Documents',
      owner: {
        id: 42,
        name: 'John Doe',
        email: '<EMAIL>'
      },
      path: '/Documents'
    };

    // Act
    const dto = plainToInstance(FolderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Documents');
    expect(dto.parent).toBeUndefined();
    expect(dto.path).toBe('/Documents');
    expect(dto.root).toBeUndefined();
    expect(dto.fileCount).toBeUndefined();
    expect(dto.files).toBeUndefined();
    expect(dto.createdAt).toBeUndefined();
    expect(dto.formattedCreatedAt).toBeUndefined();
    expect(dto.updatedAt).toBeUndefined();
    expect(dto.formattedUpdatedAt).toBeUndefined();
  });
});
