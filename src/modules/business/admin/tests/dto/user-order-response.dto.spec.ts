import { plainToInstance } from 'class-transformer';
import { UserOrderResponseDto } from '../../dto/userconverts/user-order-response.dto';

describe('UserOrderResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserOrderResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      userConvertCustomerId: 2,
      userId: 3,
      productInfo: [
        {
          productId: 1,
          name: 'Sản phẩm A',
          quantity: 2,
          price: 100000,
        },
        {
          productId: 2,
          name: 'Sản phẩm B',
          quantity: 1,
          price: 150000,
        },
      ],
      billInfo: {
        subtotal: 350000,
        tax: 35000,
        shipping: 30000,
        total: 415000,
        paymentMethod: 'COD',
      },
      hasShipping: true,
      shippingStatus: 'pending',
      logisticInfo: {
        address: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
        carrier: 'GHN',
        trackingNumber: 'GHN123456789',
      },
      createdAt: *************,
      updatedAt: *************,
      source: 'website',
    };

    // Act
    const dto = plainToInstance(UserOrderResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserOrderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBe(2);
    expect(dto.userId).toBe(3);
    
    // Kiểm tra thông tin sản phẩm
    expect(dto.productInfo).toHaveLength(2);
    expect(dto.productInfo?.[0].productId).toBe(1);
    expect(dto.productInfo?.[0].name).toBe('Sản phẩm A');
    expect(dto.productInfo?.[0].quantity).toBe(2);
    expect(dto.productInfo?.[0].price).toBe(100000);
    expect(dto.productInfo?.[1].productId).toBe(2);
    
    // Kiểm tra thông tin hóa đơn
    expect(dto.billInfo).toBeDefined();
    expect(dto.billInfo?.subtotal).toBe(350000);
    expect(dto.billInfo?.tax).toBe(35000);
    expect(dto.billInfo?.shipping).toBe(30000);
    expect(dto.billInfo?.total).toBe(415000);
    expect(dto.billInfo?.paymentMethod).toBe('COD');
    
    // Kiểm tra thông tin vận chuyển
    expect(dto.hasShipping).toBe(true);
    expect(dto.shippingStatus).toBe('pending');
    expect(dto.logisticInfo).toBeDefined();
    expect(dto.logisticInfo?.address).toBe('Số 1, Đường ABC, Quận XYZ, Hà Nội');
    expect(dto.logisticInfo?.carrier).toBe('GHN');
    expect(dto.logisticInfo?.trackingNumber).toBe('GHN123456789');
    
    expect(dto.createdAt).toBe(*************);
    expect(dto.updatedAt).toBe(*************);
    expect(dto.source).toBe('website');
  });

  it('nên xử lý đúng khi các trường tùy chọn là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      userConvertCustomerId: null,
      userId: null,
      productInfo: null,
      billInfo: null,
      hasShipping: false,
      shippingStatus: null,
      logisticInfo: null,
      createdAt: *************,
      updatedAt: *************,
      source: null,
    };

    // Act
    const dto = plainToInstance(UserOrderResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserOrderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBeNull();
    expect(dto.userId).toBeNull();
    expect(dto.productInfo).toBeNull();
    expect(dto.billInfo).toBeNull();
    expect(dto.hasShipping).toBe(false);
    expect(dto.shippingStatus).toBeNull();
    expect(dto.logisticInfo).toBeNull();
    expect(dto.createdAt).toBe(*************);
    expect(dto.updatedAt).toBe(*************);
    expect(dto.source).toBeNull();
  });

  it('nên xử lý đúng với mảng các UserOrderResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        userConvertCustomerId: 2,
        userId: 3,
        productInfo: [{ productId: 1, name: 'Sản phẩm A', quantity: 2, price: 100000 }],
        billInfo: { subtotal: 200000, total: 200000, paymentMethod: 'COD' },
        hasShipping: true,
        shippingStatus: 'pending',
        logisticInfo: { address: 'Hà Nội' },
        createdAt: *************,
        updatedAt: *************,
        source: 'website',
      },
      {
        id: 2,
        userConvertCustomerId: 2,
        userId: 3,
        productInfo: [{ productId: 2, name: 'Sản phẩm B', quantity: 1, price: 150000 }],
        billInfo: { subtotal: 150000, total: 150000, paymentMethod: 'Banking' },
        hasShipping: false,
        shippingStatus: null,
        logisticInfo: null,
        createdAt: *************,
        updatedAt: *************,
        source: 'app',
      },
    ];

    // Act
    const dtos = plainToInstance(UserOrderResponseDto, plainArray);

    // Assert
    expect(Array.isArray(dtos)).toBe(true);
    expect(dtos.length).toBe(2);
    expect(dtos[0]).toBeInstanceOf(UserOrderResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[0].hasShipping).toBe(true);
    expect(dtos[1]).toBeInstanceOf(UserOrderResponseDto);
    expect(dtos[1].id).toBe(2);
    expect(dtos[1].hasShipping).toBe(false);
  });
});
