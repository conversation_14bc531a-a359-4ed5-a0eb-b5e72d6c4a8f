import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';

/**
 * DTO cho thông tin thư mục
 */
export class FolderInfoDto {
  @ApiProperty({
    description: 'ID của thư mục',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Documents',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Đường dẫn thư mục',
    example: '/Documents',
  })
  @Expose()
  path: string;

  @ApiProperty({
    description: 'ID kho ảo gốc',
    example: 1,
    nullable: true,
  })
  @Expose()
  root: number;
}



/**
 * DTO cho response chi tiết của file
 */
export class FileDetailResponseDto {
  @ApiProperty({
    description: 'ID của tệp tin',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên tệp tin',
    example: 'document.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Thông tin thư mục chứa tệp tin',
    type: FolderInfoDto,
  })
  @Expose()
  @Type(() => FolderInfoDto)
  folder: FolderInfoDto;

  @ApiProperty({
    description: 'Kích thước tệp tin (byte)',
    example: 1024000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  size: number;

  @ApiProperty({
    description: 'Khóa lưu trữ trên hệ thống storage',
    example: 'files/user/1/document.pdf',
  })
  @Expose()
  storageKey: string;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1620000000000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1620000000000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  updatedAt: number;
}
