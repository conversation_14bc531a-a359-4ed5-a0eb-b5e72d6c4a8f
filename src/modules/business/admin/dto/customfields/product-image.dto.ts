import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

/**
 * DTO cho hình ảnh sản phẩm
 */
export class ProductImageDto {
  @ApiProperty({
    description: 'Khóa S3 của hình ảnh',
    example: 'products/image1.jpg',
  })
  @IsString()
  key: string;
  
  @ApiProperty({
    description: 'Vị trí hiển thị của hình ảnh',
    example: 0,
  })
  @IsNumber()
  position: number;
  
  @ApiProperty({
    description: 'URL đầy đủ của hình ảnh',
    example: 'https://cdn.redai.vn/products/image1.jpg',
  })
  @IsString()
  url: string;
}
