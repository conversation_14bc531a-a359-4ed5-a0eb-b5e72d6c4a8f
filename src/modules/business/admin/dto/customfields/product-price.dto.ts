import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

/**
 * DTO cho giá sản phẩm
 */
export class ProductPriceDto {
  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  currency: string;
  
  @ApiProperty({
    description: '<PERSON>i<PERSON> niêm yết',
    example: 1000000,
  })
  @IsNumber()
  listPrice: number;
  
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> b<PERSON> (sau giảm giá)',
    example: 900000,
  })
  @IsNumber()
  salePrice: number;
}
