import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, FindOptionsWhere, ILike, SelectQueryBuilder } from 'typeorm';
import { CustomGroupForm } from '@modules/business/entities';
import { QueryCustomGroupFormDto } from '@modules/business/user/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * Repository xử lý truy vấn dữ liệu cho entity CustomGroupForm
 * Hỗ trợ cả chức năng cho admin và user
 */
@Injectable()
export class CustomGroupFormRepository extends Repository<CustomGroupForm> {
  protected readonly logger = new Logger(CustomGroupFormRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomGroupForm, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho CustomGroupForm
   * @returns SelectQueryBuilder<CustomGroupForm>
   */
  protected createBaseQuery(): SelectQueryBuilder<CustomGroupForm> {
    return this.createQueryBuilder('customGroupForm');
  }

  /**
   * Tìm nhóm trường tùy chỉnh theo ID
   * @param id ID của nhóm trường tùy chỉnh
   * @returns Nhóm trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<CustomGroupForm | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm nhóm trường tùy chỉnh theo ID ${id}: ${error.message}`);
      throw new Error(`Lỗi khi tìm nhóm trường tùy chỉnh theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm nhóm trường tùy chỉnh theo ID và ID người dùng
   * @param id ID của nhóm trường tùy chỉnh
   * @param userId ID của người dùng
   * @returns Nhóm trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findByIdAndUserId(id: number, userId: number): Promise<CustomGroupForm | null> {
    try {
      return await this.findOne({ where: { id, userId } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm nhóm trường tùy chỉnh theo ID ${id} và userId ${userId}: ${error.message}`);
      throw new Error(`Lỗi khi tìm nhóm trường tùy chỉnh theo ID ${id} và userId ${userId}: ${error.message}`);
    }
  }



  /**
   * Tìm kiếm nhóm trường tùy chỉnh với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn, bao gồm:
   *   - page: Số trang (mặc định: 1)
   *   - limit: Số bản ghi mỗi trang (mặc định: 10)
   *   - search: Từ khóa tìm kiếm (tùy chọn)
   *   - userId: ID người dùng để lọc (bắt buộc, được thêm vào từ controller)
   *   - sortBy: Trường sắp xếp (mặc định: 'createAt')
   *   - sortDirection: Hướng sắp xếp (mặc định: 'DESC')
   * @returns Danh sách nhóm trường tùy chỉnh với phân trang
   */
  async findAll(queryDto: QueryCustomGroupFormDto): Promise<PaginatedResult<CustomGroupForm>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createAt',
        sortDirection = 'DESC',
      } = queryDto;

      // Tính toán offset dựa trên page và limit
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện where
      const whereConditions: FindOptionsWhere<CustomGroupForm> = {};

      // Luôn lọc theo userId (bắt buộc, được thêm vào từ controller)
      console.log(`Repository - userId: ${queryDto.userId}`);
      if (queryDto.userId) {
        whereConditions.userId = queryDto.userId;
        console.log(`Đã thêm điều kiện lọc userId = ${queryDto.userId}`);
      } else {
        this.logger.error('userId không được cung cấp trong truy vấn');
        throw new AppException(
          BUSINESS_ERROR_CODES.GROUP_FORM_FIND_FAILED,
          'BAD_REQUEST',
          'userId là bắt buộc để lọc nhóm trường tùy chỉnh'
        );
      }



      // Thêm điều kiện tìm kiếm theo label nếu có search
      if (search) {
        whereConditions.label = ILike(`%${search}%`);
      }

      // Kiểm tra trường sắp xếp hợp lệ
      const validSortFields = ['id', 'label', 'createAt', 'userId'];
      if (!validSortFields.includes(sortBy)) {
        this.logger.error(`Trường sắp xếp '${sortBy}' không hợp lệ. Các trường hợp lệ: ${validSortFields.join(', ')}`);
        throw new AppException(
          BUSINESS_ERROR_CODES.GROUP_FORM_INVALID_SORT_FIELD,
          'BAD_REQUEST',
          `Trường sắp xếp '${sortBy}' không hợp lệ. Các trường hợp lệ: ${validSortFields.join(', ')}`
        );
      }

      // Thực hiện truy vấn với điều kiện và phân trang
      const [items, totalItems] = await this.findAndCount({
        where: whereConditions,
        order: { [sortBy]: sortDirection },
        skip,
        take: limit,
      });

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      // Log kết quả truy vấn
      console.log(`Tìm thấy ${items.length} nhóm trường tùy chỉnh`);
      items.forEach(item => {
        console.log(`ID: ${item.id}, Label: ${item.label}, UserId: ${item.userId}`);
      });

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm nhóm trường tùy chỉnh: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_FIND_FAILED,
        'INTERNAL_SERVER_ERROR',
        `Lỗi khi tìm kiếm nhóm trường tùy chỉnh: ${error.message}`
      );
    }
  }
}