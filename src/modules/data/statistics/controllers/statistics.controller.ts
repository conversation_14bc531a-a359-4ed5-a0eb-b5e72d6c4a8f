import { Controller, Get, Logger, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { StatisticsService } from '../services/statistics.service';
import { StatisticsResponseDto } from '../dto/statistics-response.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';

/**
 * Controller xử lý API liên quan đến thống kê dữ liệu
 * Cung cấp endpoint để lấy thống kê về số lượng người dùng, knowledge file, media, URL và vector store
 */
@ApiTags(SWAGGER_API_TAGS.DATA_STATISTICS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(ApiResponseDto, StatisticsResponseDto)
@Controller('user/statistics')
export class StatisticsController {
  private readonly logger = new Logger(StatisticsController.name);

  constructor(private readonly statisticsService: StatisticsService) {}

  /**
   * Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store
   */
  @ApiOperation({ summary: 'Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê thành công',
    schema: ApiResponseDto.getSchema(StatisticsResponseDto),
  })
  @Get()
  async getStatistics(): Promise<ApiResponseDto<StatisticsResponseDto>> {
    this.logger.log('Đang lấy thống kê dữ liệu...');

    const statistics = await this.statisticsService.getStatistics();

    this.logger.log('Đã lấy thống kê dữ liệu thành công');

    return ApiResponseDto.success(statistics);
  }
}
