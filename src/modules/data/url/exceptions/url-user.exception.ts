import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

export const URL_ERROR_CODES = {
  URL_NOT_FOUND: new ErrorCode(
    30001,
    'Không tìm thấy URL hoặc URL không khả dụng',
    HttpStatus.NOT_FOUND,
  ),

  URL_ACCESS_DENIED: new ErrorCode(
    30002,
    'Bạn không có quyền truy cập URL này',
    HttpStatus.FORBIDDEN,
  ),

  URL_INVALID_FORMAT: new ErrorCode(
    30003,
    'Định dạng URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  URL_ALREADY_EXISTS: new ErrorCode(
    30004,
    'URL này đã tồn tại trong hệ thống',
    HttpStatus.CONFLICT,
  ),

  URL_CREATION_FAILED: new ErrorCode(
    30005,
    'Không thể tạo URL mới',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_UPDATE_FAILED: new ErrorCode(
    30006,
    'Không thể cập nhật thông tin URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_DELETE_FAILED: new ErrorCode(
    30007,
    'Không thể xóa URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_INVALID_CONTENT: new ErrorCode(
    30008,
    'Nội dung URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  URL_PROCESSING_ERROR: new ErrorCode(
    30009,
    'Lỗi khi xử lý URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  //: URL EMBEDDING ERRORS (30100-30199):
  URL_EMBEDDING_FAILED: new ErrorCode(
    30100,
    'Không thể tạo embedding cho URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_EMBEDDING_NOT_FOUND: new ErrorCode(
    30101,
    'Không tìm thấy embedding cho URL',
    HttpStatus.NOT_FOUND,
  ),

  //: URL SEARCH ERRORS (30200-30299):
  URL_SEARCH_FAILED: new ErrorCode(
    30200,
    'Tìm kiếm URL thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_INVALID_SEARCH_PARAMS: new ErrorCode(
    30201,
    'Tham số tìm kiếm URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  URL_FETCH_FAILED: new ErrorCode(
    30202,
    'Không thể lấy thông tin URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_INVALID_PARAMS: new ErrorCode(
    30203,
    'Tham số URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  //: URL CRAWL ERRORS (30300-30399):
  URL_CRAWL_FAILED: new ErrorCode(
    30300,
    'Không thể crawl URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_CRAWL_TIMEOUT: new ErrorCode(
    30301,
    'Quá thời gian crawl URL',
    HttpStatus.REQUEST_TIMEOUT,
  ),

  URL_CRAWL_CONTENT_EXTRACTION_FAILED: new ErrorCode(
    30302,
    'Không thể trích xuất nội dung từ URL',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  URL_CRAWL_AI_PROCESSING_FAILED: new ErrorCode(
    30303,
    'Không thể xử lý nội dung bằng AI',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};