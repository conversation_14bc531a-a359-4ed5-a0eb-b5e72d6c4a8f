import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { UrlRepository } from '../../repositories';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SortDirection } from '@common/dto/query.dto';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';
import { ExtractedMetadata } from '../interfaces/extracted-metadata.interface';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { backOff } from 'exponential-backoff';
import { URL_ERROR_CODES } from 'src/modules/data/url/exceptions';
import { AppException } from '@/common';
import { ProxyRotationService } from '../../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../../shared/services/user-agent-rotation.service';
import { AdvancedCrawlerService } from '../../shared/services/advanced-crawler.service';

// Enum cho các loại lỗi crawl
enum CrawlErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  ROBOTS_BLOCKED = 'ROBOTS_BLOCKED',
  INVALID_URL = 'INVALID_URL',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Interface cho thông tin lỗi chi tiết
interface CrawlError {
  type: CrawlErrorType;
  message: string;
  url: string;
  statusCode?: number;
  retryable: boolean;
  retryAfter?: number; // seconds
}

// Interface cho progress tracking
interface CrawlProgress {
  totalUrls: number;
  processedUrls: number;
  successfulUrls: number;
  failedUrls: number;
  currentDepth: number;
  startTime: number;
  estimatedTimeRemaining?: number;
  currentUrl?: string;
  errors: CrawlError[];
}

// Type cho progress callback
type ProgressCallback = (progress: CrawlProgress) => void;

@Injectable()
export class UrlUserService {
  private readonly logger = new Logger(UrlUserService.name);
  private progressCallbacks = new Map<string, ProgressCallback>(); // sessionId -> callback

  // Cache cho robots.txt
  private robotsCache = new Map<string, { allowed: boolean; expiry: number }>(); // domain -> cache
  private readonly ROBOTS_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 giờ

  // Cache cho DNS lookups
  private dnsCache = new Map<string, { ip: string; expiry: number }>(); // hostname -> cache
  private readonly DNS_CACHE_TTL = 60 * 60 * 1000; // 1 giờ

  // Cache cho metadata
  private metadataCache = new Map<string, { metadata: ExtractedMetadata; expiry: number }>(); // url -> cache
  private readonly METADATA_CACHE_TTL = 30 * 60 * 1000; // 30 phút

  // Rate limiting per domain - Tối ưu cho crawling nhanh hơn với environment variables
  private domainRateLimits = new Map<string, { lastRequest: number; requestCount: number; resetTime: number }>(); // domain -> rate limit info
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 phút
  private readonly MAX_REQUESTS_PER_MINUTE = parseInt(process.env.CRAWL_RATE_LIMIT_REQUESTS_PER_MINUTE || '30') || 30; // Configurable từ env
  private readonly MIN_DELAY_BETWEEN_REQUESTS = parseInt(process.env.CRAWL_MIN_DELAY_BETWEEN_REQUESTS || '200') || 200; // Configurable từ env

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly httpService: HttpService,
    private readonly proxyRotationService: ProxyRotationService,
    private readonly userAgentRotationService: UserAgentRotationService,
    private readonly advancedCrawlerService: AdvancedCrawlerService,
  ) {
    // Cleanup cache mỗi 30 phút
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 30 * 60 * 1000);
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();

    // Cleanup robots cache
    for (const [domain, cache] of this.robotsCache.entries()) {
      if (cache.expiry < now) {
        this.robotsCache.delete(domain);
      }
    }

    // Cleanup DNS cache
    for (const [hostname, cache] of this.dnsCache.entries()) {
      if (cache.expiry < now) {
        this.dnsCache.delete(hostname);
      }
    }

    // Cleanup metadata cache
    for (const [url, cache] of this.metadataCache.entries()) {
      if (cache.expiry < now) {
        this.metadataCache.delete(url);
      }
    }

    // Cleanup rate limit data
    for (const [domain, rateLimit] of this.domainRateLimits.entries()) {
      if (rateLimit.resetTime < now) {
        this.domainRateLimits.delete(domain);
      }
    }

    this.logger.debug(`Cache cleanup completed. Robots: ${this.robotsCache.size}, DNS: ${this.dnsCache.size}, Metadata: ${this.metadataCache.size}, RateLimit: ${this.domainRateLimits.size}`);
  }

  /**
   * Kiểm tra và áp dụng rate limiting cho domain
   * @param url URL cần kiểm tra
   * @returns Số milliseconds cần chờ trước khi có thể request, 0 nếu có thể request ngay
   */
  private checkRateLimit(url: string): number {
    try {
      const domain = new URL(url).hostname;
      const now = Date.now();

      let rateLimit = this.domainRateLimits.get(domain);

      if (!rateLimit) {
        // Lần đầu tiên request domain này
        rateLimit = {
          lastRequest: now,
          requestCount: 1,
          resetTime: now + this.RATE_LIMIT_WINDOW
        };
        this.domainRateLimits.set(domain, rateLimit);
        return 0;
      }

      // Reset counter nếu đã hết window
      if (now >= rateLimit.resetTime) {
        rateLimit.requestCount = 1;
        rateLimit.lastRequest = now;
        rateLimit.resetTime = now + this.RATE_LIMIT_WINDOW;
        return 0;
      }

      // Kiểm tra số lượng requests trong window
      if (rateLimit.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
        const waitTime = rateLimit.resetTime - now;
        this.logger.warn(`Rate limit exceeded for domain ${domain}, wait ${waitTime}ms`);
        return waitTime;
      }

      // Kiểm tra delay tối thiểu giữa các requests
      const timeSinceLastRequest = now - rateLimit.lastRequest;
      if (timeSinceLastRequest < this.MIN_DELAY_BETWEEN_REQUESTS) {
        const waitTime = this.MIN_DELAY_BETWEEN_REQUESTS - timeSinceLastRequest;
        this.logger.debug(`Min delay not met for domain ${domain}, wait ${waitTime}ms`);
        return waitTime;
      }

      // Cập nhật rate limit
      rateLimit.requestCount++;
      rateLimit.lastRequest = now;

      return 0;
    } catch (error) {
      this.logger.warn(`Error checking rate limit for ${url}: ${error.message}`);
      return 0; // Cho phép request nếu có lỗi
    }
  }

  /**
   * Chờ theo rate limit nếu cần thiết
   * @param url URL cần request
   */
  private async waitForRateLimit(url: string): Promise<void> {
    const waitTime = this.checkRateLimit(url);
    if (waitTime > 0) {
      this.logger.log(`Waiting ${waitTime}ms for rate limit on domain: ${new URL(url).hostname}`);
      await new Promise(resolve => setTimeout(resolve, waitTime));

      // Kiểm tra lại sau khi chờ
      const remainingWait = this.checkRateLimit(url);
      if (remainingWait > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingWait));
      }
    }
  }

  /**
   * Đăng ký progress callback cho một session crawl
   * @param sessionId ID của session crawl
   * @param callback Callback function để nhận progress updates
   */
  registerProgressCallback(sessionId: string, callback: ProgressCallback): void {
    this.progressCallbacks.set(sessionId, callback);
  }

  /**
   * Hủy đăng ký progress callback
   * @param sessionId ID của session crawl
   */
  unregisterProgressCallback(sessionId: string): void {
    this.progressCallbacks.delete(sessionId);
  }

  /**
   * Cập nhật progress và gọi callback nếu có
   * @param sessionId ID của session crawl
   * @param progress Thông tin progress hiện tại
   */
  private updateProgress(sessionId: string, progress: CrawlProgress): void {
    const callback = this.progressCallbacks.get(sessionId);
    if (callback) {
      // Tính toán estimated time remaining
      const elapsedTime = Date.now() - progress.startTime;
      if (progress.processedUrls > 0) {
        const avgTimePerUrl = elapsedTime / progress.processedUrls;
        const remainingUrls = progress.totalUrls - progress.processedUrls;
        progress.estimatedTimeRemaining = Math.round(avgTimePerUrl * remainingUrls / 1000); // seconds
      }

      try {
        callback(progress);
      } catch (error) {
        this.logger.warn(`Error in progress callback for session ${sessionId}: ${error.message}`);
      }
    }

    // Log progress
    const percentage = progress.totalUrls > 0 ? Math.round((progress.processedUrls / progress.totalUrls) * 100) : 0;
    this.logger.log(`Progress [${sessionId}]: ${percentage}% (${progress.processedUrls}/${progress.totalUrls}) - Success: ${progress.successfulUrls}, Failed: ${progress.failedUrls}`);
  }

  /**
   * Phân loại lỗi crawl để xử lý thông minh
   * @param error Lỗi gốc
   * @param url URL gây lỗi
   * @returns Thông tin lỗi đã phân loại
   */
  private categorizeError(error: any, url: string): CrawlError {
    // Lỗi timeout
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return {
        type: CrawlErrorType.TIMEOUT_ERROR,
        message: `Timeout khi truy cập ${url}`,
        url,
        retryable: true,
        retryAfter: 5
      };
    }

    // Lỗi network
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET') {
      return {
        type: CrawlErrorType.NETWORK_ERROR,
        message: `Lỗi kết nối mạng: ${error.code}`,
        url,
        retryable: true,
        retryAfter: 10
      };
    }

    // Lỗi HTTP response
    if (error.response?.status) {
      const status = error.response.status;

      if (status === 429) {
        const retryAfter = parseInt(error.response.headers['retry-after']) || 60;
        return {
          type: CrawlErrorType.RATE_LIMIT_ERROR,
          message: `Rate limit exceeded cho ${url}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter
        };
      }

      if (status >= 400 && status < 500) {
        return {
          type: CrawlErrorType.CLIENT_ERROR,
          message: `Lỗi client ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable: status === 408 || status === 409 || status === 423 || status === 424, // Chỉ retry một số lỗi 4xx
        };
      }

      if (status >= 500) {
        return {
          type: CrawlErrorType.SERVER_ERROR,
          message: `Lỗi server ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter: 30
        };
      }
    }

    // Lỗi parsing
    if (error.message?.includes('parse') || error.message?.includes('invalid')) {
      return {
        type: CrawlErrorType.PARSING_ERROR,
        message: `Lỗi phân tích dữ liệu: ${error.message}`,
        url,
        retryable: false
      };
    }

    // Lỗi không xác định
    return {
      type: CrawlErrorType.UNKNOWN_ERROR,
      message: `Lỗi không xác định: ${error.message}`,
      url,
      retryable: true,
      retryAfter: 15
    };
  }

  /**
   * Tạo retry strategy thông minh dựa trên loại lỗi
   * @param crawlError Thông tin lỗi đã phân loại
   * @returns Retry configuration
   */
  private createRetryStrategy(crawlError: CrawlError) {
    if (!crawlError.retryable) {
      return {
        numOfAttempts: 1,
        retry: () => false
      };
    }

    const baseConfig = {
      startingDelay: (crawlError.retryAfter || 1) * 1000,
      timeMultiple: 2,
      maxDelay: 60000,
      delayFirstAttempt: false,
      jitter: 'full' as const
    };

    switch (crawlError.type) {
      case CrawlErrorType.RATE_LIMIT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 2, // Ít retry hơn cho rate limit
          startingDelay: (crawlError.retryAfter || 60) * 1000,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(`Rate limit retry ${attemptNumber} for ${crawlError.url}`);
            return attemptNumber < 2;
          }
        };

      case CrawlErrorType.TIMEOUT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(`Timeout retry ${attemptNumber} for ${crawlError.url}`);
            return attemptNumber < 3;
          }
        };

      case CrawlErrorType.SERVER_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 4,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(`Server error retry ${attemptNumber} for ${crawlError.url}`);
            return attemptNumber < 4;
          }
        };

      case CrawlErrorType.NETWORK_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          startingDelay: 5000, // Chờ lâu hơn cho network error
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(`Network error retry ${attemptNumber} for ${crawlError.url}`);
            return attemptNumber < 3;
          }
        };

      default:
        return {
          ...baseConfig,
          numOfAttempts: 2,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(`Generic retry ${attemptNumber} for ${crawlError.url}`);
            return attemptNumber < 2;
          }
        };
    }
  }

  /**
   * Trích xuất domain chính từ hostname
   * Ví dụ: www.example.com -> example.com
   * @param hostname Hostname cần trích xuất
   * @returns Domain chính
   */
  private extractMainDomain(hostname: string): string {
    // Loại bỏ www. nếu có
    let domain = hostname.replace(/^www\./, '');

    // Trả về domain chính
    return domain;
  }



  /**
   * Kiểm tra quyền crawl theo robots.txt với cache
   * @param url URL cần kiểm tra
   * @returns true nếu được phép crawl, false nếu không
   */
  private async checkRobotsPermission(url: string): Promise<boolean> {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;
      const robotsUrl = `${parsedUrl.protocol}//${domain}/robots.txt`;

      // Kiểm tra cache trước
      const cached = this.robotsCache.get(domain);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Robots.txt cache hit for domain: ${domain}`);
        return cached.allowed;
      }

      this.logger.log(`Kiểm tra robots.txt: ${robotsUrl}`);

      // Sử dụng thư viện exponential-backoff để retry
      const robotsTxt = await backOff(
        async () => {
          const response = await firstValueFrom(this.httpService.get(robotsUrl, {
            timeout: 5000,
            headers: {
              'User-Agent': 'Mozilla/5.0'
            }
          }));
          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: 3,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 5000, // Tối đa 5 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(`Attempt ${attemptNumber} failed for robots.txt ${robotsUrl}: ${error.message}`);

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (error.response && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
              return false;
            }

            return true;
          }
        }
      );

      let allowed = true;

      // Phân tích robots.txt đơn giản
      // Kiểm tra xem có bị disallow toàn bộ không
      if (robotsTxt.includes('Disallow: /')) {
        this.logger.warn(`Robots.txt không cho phép crawl: ${url}`);
        allowed = false;
      } else {
        // Kiểm tra xem URL cụ thể có bị disallow không
        const urlPath = parsedUrl.pathname;
        const disallowLines = robotsTxt.split('\n')
          .filter((line: string) => line.trim().startsWith('Disallow:'))
          .map((line: string) => line.split('Disallow:')[1].trim());

        for (const disallowPath of disallowLines) {
          if (disallowPath && urlPath.startsWith(disallowPath)) {
            this.logger.warn(`Robots.txt không cho phép crawl path: ${urlPath}`);
            allowed = false;
            break;
          }
        }
      }

      // Cache kết quả
      this.robotsCache.set(domain, {
        allowed,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL
      });

      this.logger.debug(`Robots.txt cached for domain: ${domain}, allowed: ${allowed}`);
      return allowed;
    } catch (error) {
      // Nếu không tìm thấy robots.txt, cho phép crawl và cache kết quả
      this.logger.warn(`Không thể kiểm tra robots.txt: ${error.message}`);

      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;

      // Cache cho phép crawl khi không có robots.txt
      this.robotsCache.set(domain, {
        allowed: true,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL
      });

      return true;
    }
  }



  /**
   * Chuẩn hóa URL
   * @param url URL cần chuẩn hóa
   * @returns URL đã chuẩn hóa
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // Loại bỏ fragment
      urlObj.hash = '';

      // Loại bỏ các query params không cần thiết
      const paramsToRemove = ['utm_source', 'utm_medium', 'utm_campaign', 'fbclid', 'gclid'];
      const params = new URLSearchParams(urlObj.search);

      paramsToRemove.forEach(param => {
        if (params.has(param)) {
          params.delete(param);
        }
      });

      urlObj.search = params.toString();

      // Đảm bảo URL kết thúc bằng / nếu không có path
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.toString();
    } catch (error) {
      // Nếu không thể chuẩn hóa, trả về URL gốc
      return url;
    }
  }

  /**
   * Thực hiện HTTP request với retry
   * @param url URL cần request
   * @param maxRetries Số lần retry tối đa
   * @returns Response data
   */
  private async fetchWithRetry(url: string, maxRetries = 3): Promise<any> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching data from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'chrome',
            referer: url
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 60000, // 60 giây timeout - Tăng để tránh timeout
            maxRedirects: 5 // Cho phép tối đa 5 lần chuyển hướng
          });

          const response = await firstValueFrom(this.httpService.get(url, requestConfig));

          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(`Attempt ${attemptNumber} failed for ${url}: ${error.message}`);

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (error.response && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
              this.logger.warn(`Not retrying due to status code: ${error.response.status}`);
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(`Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`);
            return true;
          }
        }
      );

      return result;
    } catch (error) {
      this.logger.error(`All retry attempts failed for ${url}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetch HTML từ URL với retry, chỉ lấy thẻ head
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML của thẻ head
   */
  private async fetchHeadWithRetry(url: string, maxRetries = 3): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching head from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'firefox',
            referer: url
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 30000, // 30 giây timeout - Tăng để tránh timeout
            maxRedirects: 5,
            responseType: 'text'
          });

          // Sử dụng axios với responseType: 'text' để lấy HTML
          const response = await firstValueFrom(this.httpService.get(url, requestConfig));

          const html = response.data;

          // Chỉ trích xuất phần head từ HTML
          const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
          if (headMatch && headMatch[1]) {
            return `<head>${headMatch[1]}</head>`;
          }

          // Nếu không tìm thấy thẻ head, trả về toàn bộ HTML
          return html;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(`Attempt ${attemptNumber} failed for ${url}: ${error.message}`);

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (error.response && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
              this.logger.warn(`Not retrying due to status code: ${error.response.status}`);
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(`Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`);
            return true;
          }
        }
      );

      return result;
    } catch (error) {
      this.logger.error(`All retry attempts failed for ${url}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetch HTML có giới hạn kích thước với streaming
   * @param url URL cần fetch
   * @param maxSize Kích thước tối đa (bytes)
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã giới hạn kích thước
   */
  private async fetchLimitedHtml(url: string, maxSize = 50 * 1024, maxRetries = 3): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching limited HTML (${maxSize} bytes) from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'safari',
            referer: url
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 15000, // 15 giây timeout
            maxRedirects: 5,
            responseType: 'stream'
          });

          // Sử dụng axios với responseType: 'stream' để streaming
          const response = await firstValueFrom(this.httpService.get(url, requestConfig));

          return new Promise<string>((resolve, reject) => {
            let htmlBuffer = '';
            let bytesReceived = 0;
            let isComplete = false;

            response.data.on('data', (chunk: Buffer) => {
              if (isComplete) return;

              const chunkStr = chunk.toString('utf8');
              bytesReceived += chunk.length;

              // Kiểm tra nếu đã đạt giới hạn
              if (bytesReceived >= maxSize) {
                const remainingBytes = maxSize - (bytesReceived - chunk.length);
                if (remainingBytes > 0) {
                  htmlBuffer += chunkStr.substring(0, remainingBytes);
                }
                isComplete = true;
                response.data.destroy(); // Dừng stream
                this.logger.log(`Stream stopped at ${maxSize} bytes for memory optimization`);
                resolve(htmlBuffer);
                return;
              }

              htmlBuffer += chunkStr;

              // Kiểm tra xem đã có đủ thông tin metadata chưa (head tag)
              if (htmlBuffer.includes('</head>') && htmlBuffer.includes('<title>')) {
                isComplete = true;
                response.data.destroy(); // Dừng stream sớm
                this.logger.log(`Stream stopped early after finding head tag (${bytesReceived} bytes)`);
                resolve(htmlBuffer);
                return;
              }
            });

            response.data.on('end', () => {
              if (!isComplete) {
                resolve(htmlBuffer);
              }
            });

            response.data.on('error', (error: Error) => {
              reject(error);
            });

            // Timeout cho stream
            setTimeout(() => {
              if (!isComplete) {
                isComplete = true;
                response.data.destroy();
                this.logger.warn(`Stream timeout for ${url}, using partial data (${bytesReceived} bytes)`);
                resolve(htmlBuffer);
              }
            }, 10000); // 10 giây timeout cho stream
          });
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(`Attempt ${attemptNumber} failed for ${url}: ${error.message}`);

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (error.response && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
              this.logger.warn(`Not retrying due to status code: ${error.response.status}`);
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(`Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`);
            return true;
          }
        }
      );

      return result;
    } catch (error) {
      this.logger.error(`All retry attempts failed for ${url}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetch HTML với streaming thông minh - dừng sớm khi tìm thấy metadata với error handling thông minh
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML tối ưu cho metadata extraction
   */
  private async fetchStreamingHtml(url: string, maxRetries = 3): Promise<string> {
    let lastError: any;

    try {
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching streaming HTML from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'edge',
            referer: url
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 15000,
            maxRedirects: 5,
            responseType: 'stream'
          });

          const response = await firstValueFrom(this.httpService.get(url, requestConfig));

          return new Promise<string>((resolve, reject) => {
            let htmlBuffer = '';
            let bytesReceived = 0;
            let foundHead = false;
            let foundJsonLd = false;
            let isComplete = false;
            const MAX_SIZE = 500 * 1024; // 500KB tối đa để lấy nhiều content hơn

            response.data.on('data', (chunk: Buffer) => {
              if (isComplete) return;

              const chunkStr = chunk.toString('utf8');
              bytesReceived += chunk.length;
              htmlBuffer += chunkStr;

              // Kiểm tra các điều kiện dừng sớm
              if (!foundHead && htmlBuffer.includes('</head>')) {
                foundHead = true;
                this.logger.debug(`Found head tag at ${bytesReceived} bytes`);
              }

              if (!foundJsonLd && htmlBuffer.includes('application/ld+json')) {
                foundJsonLd = true;
                this.logger.debug(`Found JSON-LD at ${bytesReceived} bytes`);
              }

              // Dừng sớm nếu đã có đủ thông tin hoặc quá lớn
              if ((foundHead && (!this.needsJsonLd(url) || foundJsonLd)) || bytesReceived >= MAX_SIZE) {
                isComplete = true;
                response.data.destroy();
                this.logger.log(`Stream completed early: head=${foundHead}, jsonLd=${foundJsonLd}, size=${bytesReceived}`);
                resolve(htmlBuffer);
                return;
              }
            });

            response.data.on('end', () => {
              if (!isComplete) {
                this.logger.log(`Stream ended naturally at ${bytesReceived} bytes`);
                resolve(htmlBuffer);
              }
            });

            response.data.on('error', (error: Error) => {
              reject(error);
            });

            // Timeout cho stream
            setTimeout(() => {
              if (!isComplete) {
                isComplete = true;
                response.data.destroy();
                this.logger.warn(`Stream timeout for ${url}, using partial data (${bytesReceived} bytes)`);
                resolve(htmlBuffer);
              }
            }, 12000); // 12 giây timeout
          });
        },
        {
          numOfAttempts: maxRetries,
          startingDelay: 1000,
          timeMultiple: 2,
          maxDelay: 30000,
          delayFirstAttempt: false,
          jitter: 'full',
          retry: (error: any, attemptNumber: number) => {
            lastError = error;
            const crawlError = this.categorizeError(error, url);

            this.logger.warn(`Attempt ${attemptNumber} failed for ${url}: ${crawlError.type} - ${crawlError.message}`);

            // Sử dụng retry strategy thông minh
            const retryStrategy = this.createRetryStrategy(crawlError);
            return retryStrategy.retry ? retryStrategy.retry(error, attemptNumber) : crawlError.retryable;
          }
        }
      );

      return result;
    } catch (error) {
      const crawlError = this.categorizeError(lastError || error, url);
      this.logger.error(`All streaming attempts failed for ${url}: ${crawlError.type} - ${crawlError.message}`);
      throw error;
    }
  }

  /**
   * Fetch HTML với phương pháp thích ứng dựa trên loại trang web với streaming optimization
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã tối ưu
   */
  private async fetchOptimalHtml(url: string, maxRetries = 3): Promise<string> {
    try {
      // Kiểm tra URL để quyết định phương pháp phù hợp
      if (this.isSimpleWebsite(url)) {
        // Trang web đơn giản, chỉ cần phần head
        this.logger.log(`Using head-only fetch for simple website: ${url}`);
        return this.fetchHeadWithRetry(url, maxRetries);
      } else if (this.needsJsonLd(url) || this.isDynamicWebsite(url)) {
        // Trang web phức tạp, sử dụng streaming thông minh
        this.logger.log(`Using smart streaming fetch for complex website: ${url}`);
        return this.fetchStreamingHtml(url, maxRetries);
      } else {
        // Mặc định, sử dụng streaming với giới hạn nhỏ
        this.logger.log(`Using default streaming fetch for: ${url}`);
        return this.fetchLimitedHtml(url, 50 * 1024, maxRetries);
      }
    } catch (error) {
      this.logger.error(`Failed to fetch optimal HTML for ${url}: ${error.message}`);
      // Fallback to head-only if optimal fetch fails
      this.logger.log(`Falling back to head-only fetch for ${url}`);
      return this.fetchHeadWithRetry(url, maxRetries);
    }
  }

  /**
   * Trích xuất metadata từ thẻ head
   * @param html HTML của trang web
   * @param url URL của trang web
   * @returns Metadata đã trích xuất
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title từ nhiều nguồn
    let title = $('title').text().trim();
    if (!title) {
      title = $('meta[property="og:title"]').attr('content') ||
              $('meta[name="twitter:title"]').attr('content') ||
              $('meta[itemprop="name"]').attr('content') ||
              $('h1').first().text().trim() || '';
    }

    // Trích xuất content (description) từ nhiều nguồn
    let content = $('meta[name="description"]').attr('content') || '';
    if (!content) {
      content = $('meta[property="og:description"]').attr('content') ||
                $('meta[name="twitter:description"]').attr('content') ||
                $('meta[itemprop="description"]').attr('content') ||
                $('meta[property="description"]').attr('content') || '';
    }

    // Trích xuất tags (keywords) từ nhiều nguồn
    const tags = $('meta[name="keywords"]').attr('content') ||
                 $('meta[property="article:tag"]').attr('content') ||
                 $('meta[property="keywords"]').attr('content') || '';

    this.logger.debug(`Extracted metadata from head: title="${title}", content="${content.substring(0, 50)}...", tags="${tags}"`);

    return {
      url,
      title,
      content,
      tags
    };
  }

  /**
   * Trích xuất metadata từ JSON-LD
   * @param html HTML của trang web
   * @returns Dữ liệu JSON-LD đã phân tích
   */
  private extractJsonLdMetadata(html: string): Array<Record<string, any>> {
    const $ = cheerio.load(html);
    const jsonLdScripts = $('script[type="application/ld+json"]');

    if (jsonLdScripts.length === 0) return [];

    try {
      const jsonLdData: Array<Record<string, any>> = [];
      jsonLdScripts.each((_, element) => {
        try {
          const jsonContent = $(element).html();
          if (jsonContent) {
            const parsed = JSON.parse(jsonContent);
            jsonLdData.push(parsed);
          }
        } catch (e) {
          this.logger.warn(`Failed to parse JSON-LD: ${e.message}`);
        }
      });

      this.logger.debug(`Extracted ${jsonLdData.length} JSON-LD objects`);
      return jsonLdData;
    } catch (error) {
      this.logger.warn(`Error extracting JSON-LD: ${error.message}`);
      return [];
    }
  }

  /**
   * Kiểm tra xem URL có phải là trang web đơn giản không
   * @param url URL cần kiểm tra
   * @returns true nếu là trang web đơn giản
   */
  private isSimpleWebsite(url: string): boolean {
    try {
      const hostname = new URL(url).hostname;
      const simplePatterns = [
        'wikipedia.org', 'github.com', 'medium.com', 'news.', 'blog.',
        'gov.', 'edu.', 'org'
      ];
      return simplePatterns.some(pattern => hostname.includes(pattern));
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra xem URL có cần trích xuất JSON-LD không
   * @param url URL cần kiểm tra
   * @returns true nếu cần trích xuất JSON-LD
   */
  private needsJsonLd(url: string): boolean {
    try {
      const urlString = url.toLowerCase();
      const jsonLdPatterns = [
        'product', 'shop', 'store', 'ecommerce', 'article', 'movie',
        'restaurant', 'hotel', 'review', 'business'
      ];
      return jsonLdPatterns.some(pattern => urlString.includes(pattern));
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra xem URL có phải là trang web động không
   * @param url URL cần kiểm tra
   * @returns true nếu là trang web động
   */
  private isDynamicWebsite(url: string): boolean {
    try {
      const hostname = new URL(url).hostname;
      const dynamicDomains = [
        'shopee', 'lazada', 'tiki', 'sendo', 'facebook', 'twitter',
        'instagram', 'tiktok', 'youtube', 'amazon', 'ebay'
      ];
      return dynamicDomains.some(domain => hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra metadata có hợp lệ không (có title hoặc content)
   * @param metadata Metadata cần kiểm tra
   * @returns true nếu metadata hợp lệ, false nếu không
   */
  private isValidMetadata(metadata: ExtractedMetadata): boolean {
    // Kiểm tra URL có tồn tại
    const hasUrl = Boolean(metadata.url && metadata.url.trim() !== '');

    // Kiểm tra title có tồn tại và không rỗng
    const hasTitle = Boolean(metadata.title && metadata.title.trim() !== '');

    // Kiểm tra content có tồn tại và không rỗng
    const hasContent = Boolean(metadata.content && metadata.content.trim() !== '');

    // Nới lỏng validation: chỉ cần có URL và ít nhất title HOẶC content
    const isValid = hasUrl && (hasTitle || hasContent);

    // Log kết quả kiểm tra chi tiết
    if (!isValid) {
      this.logger.warn(`❌ Invalid metadata for URL ${metadata.url}: hasUrl=${hasUrl}, hasTitle=${hasTitle}, hasContent=${hasContent}`);
      this.logger.debug(`   Title: "${metadata.title}"`);
      this.logger.debug(`   Content: "${metadata.content?.substring(0, 100)}..."`);
    } else {
      this.logger.debug(`✅ Valid metadata for URL ${metadata.url}: hasTitle=${hasTitle}, hasContent=${hasContent}`);
    }

    return isValid;
  }

  /**
   * Lưu metadata vào cơ sở dữ liệu
   * @param userId ID của người dùng
   * @param metadata Metadata đã trích xuất
   * @returns true nếu lưu thành công, false nếu không
   */
  private async saveMetadata(userId: number, metadata: ExtractedMetadata): Promise<boolean> {
    try {
      // Kiểm tra metadata có hợp lệ không
      if (!this.isValidMetadata(metadata)) {
        this.logger.warn(`Skipping save for invalid metadata: ${metadata.url}`);
        return false;
      }

      // Kiểm tra xem URL đã tồn tại trong cơ sở dữ liệu chưa
      const existingUrl = await this.urlRepository.findOne({
        where: { url: metadata.url, ownedBy: userId }
      });

      if (existingUrl) {
        // Nếu đã tồn tại, cập nhật metadata
        existingUrl.title = metadata.title;
        existingUrl.content = metadata.content;
        // Lưu tags vào trường content nếu cần
        if (metadata.tags) {
          existingUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
        }
        existingUrl.updatedAt = Date.now();

        await this.urlRepository.save(existingUrl);
        this.logger.log(`Đã cập nhật metadata cho URL: ${metadata.url}`);
      } else {
        // Nếu chưa tồn tại, tạo mới
        const newUrl = new Url();
        newUrl.url = metadata.url;
        newUrl.title = metadata.title;
        newUrl.content = metadata.content;
        // Lưu tags vào trường content nếu cần
        if (metadata.tags) {
          newUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
        }
        newUrl.ownedBy = userId;
        newUrl.createdAt = Date.now();
        newUrl.updatedAt = Date.now();

        await this.urlRepository.save(newUrl);
        this.logger.log(`Đã lưu metadata mới cho URL: ${metadata.url}`);
      }

      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi lưu metadata cho URL ${metadata.url}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lưu batch metadata vào cơ sở dữ liệu với hiệu suất cao và logging chi tiết
   * @param userId ID của người dùng
   * @param metadataList Danh sách metadata cần lưu
   * @returns Số lượng metadata đã lưu thành công
   */
  private async saveBatchMetadata(userId: number, metadataList: ExtractedMetadata[]): Promise<number> {
    if (!metadataList || metadataList.length === 0) {
      this.logger.warn(`saveBatchMetadata: Không có metadata để lưu`);
      return 0;
    }

    try {
      this.logger.log(`🔄 Bắt đầu lưu batch ${metadataList.length} metadata cho user ${userId}`);

      // Log URLs sẽ được xử lý
      const urlsToProcess = metadataList.map(m => m.url);
      this.logger.debug(`URLs cần xử lý: ${urlsToProcess.slice(0, 5).join(', ')}${urlsToProcess.length > 5 ? '...' : ''}`);

      // Lọc metadata hợp lệ
      const validMetadata = metadataList.filter(metadata => {
        const isValid = this.isValidMetadata(metadata);
        if (!isValid) {
          this.logger.warn(`❌ Metadata không hợp lệ cho URL: ${metadata.url} - title: "${metadata.title}", content: "${metadata.content?.substring(0, 50)}..."`);
        }
        return isValid;
      });

      this.logger.log(`✅ Metadata hợp lệ: ${validMetadata.length}/${metadataList.length}`);

      if (validMetadata.length === 0) {
        this.logger.warn(`❌ Không có metadata hợp lệ nào để lưu`);
        return 0;
      }

      // Lấy danh sách URL đã tồn tại
      const urls = validMetadata.map(m => m.url);
      this.logger.debug(`🔍 Kiểm tra ${urls.length} URLs đã tồn tại trong DB...`);

      const existingUrls = await this.urlRepository.find({
        where: { url: In(urls), ownedBy: userId }
      });

      this.logger.log(`📊 Tìm thấy ${existingUrls.length} URLs đã tồn tại trong DB`);

      if (existingUrls.length > 0) {
        this.logger.debug(`URLs đã tồn tại: ${existingUrls.map(u => u.url).slice(0, 3).join(', ')}${existingUrls.length > 3 ? '...' : ''}`);
      }

      const existingUrlMap = new Map(existingUrls.map(url => [url.url, url]));

      // Phân loại URL cần update và URL cần tạo mới
      const urlsToUpdate: Url[] = [];
      const urlsToCreate: Url[] = [];
      const currentTime = Date.now();

      for (const metadata of validMetadata) {
        const existingUrl = existingUrlMap.get(metadata.url);

        if (existingUrl) {
          // Cập nhật URL đã tồn tại
          existingUrl.title = metadata.title;
          existingUrl.content = metadata.content;
          if (metadata.tags) {
            existingUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
          }
          existingUrl.updatedAt = currentTime;
          urlsToUpdate.push(existingUrl);
          this.logger.debug(`🔄 Sẽ cập nhật URL: ${metadata.url}`);
        } else {
          // Tạo URL mới
          const newUrl = new Url();
          newUrl.url = metadata.url;
          newUrl.title = metadata.title;
          newUrl.content = metadata.content;
          if (metadata.tags) {
            newUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
          }
          newUrl.ownedBy = userId;
          newUrl.createdAt = currentTime;
          newUrl.updatedAt = currentTime;
          urlsToCreate.push(newUrl);
          this.logger.debug(`➕ Sẽ tạo mới URL: ${metadata.url}`);
        }
      }

      this.logger.log(`📝 Phân loại hoàn thành: ${urlsToUpdate.length} URLs cập nhật, ${urlsToCreate.length} URLs tạo mới`);

      // Thực hiện batch operations
      let savedCount = 0;

      if (urlsToUpdate.length > 0) {
        try {
          await this.urlRepository.save(urlsToUpdate);
          savedCount += urlsToUpdate.length;
          this.logger.log(`✅ Đã cập nhật ${urlsToUpdate.length} URLs`);
        } catch (updateError) {
          this.logger.error(`❌ Lỗi khi cập nhật URLs: ${updateError.message}`);
        }
      }

      if (urlsToCreate.length > 0) {
        try {
          await this.urlRepository.save(urlsToCreate);
          savedCount += urlsToCreate.length;
          this.logger.log(`✅ Đã tạo mới ${urlsToCreate.length} URLs`);
        } catch (createError) {
          this.logger.error(`❌ Lỗi khi tạo mới URLs: ${createError.message}`);
        }
      }

      this.logger.log(`🎉 Hoàn thành lưu batch: ${savedCount}/${metadataList.length} metadata thành công (${validMetadata.length} hợp lệ)`);

      // Log chi tiết nếu có sự khác biệt
      if (savedCount !== metadataList.length) {
        const invalidCount = metadataList.length - validMetadata.length;
        const failedCount = validMetadata.length - savedCount;
        this.logger.warn(`⚠️  Phân tích: ${invalidCount} metadata không hợp lệ, ${failedCount} metadata lưu thất bại`);
      }

      return savedCount;
    } catch (error) {
      this.logger.error(`❌ Lỗi khi lưu batch metadata: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      return 0;
    }
  }

  /**
   * Trích xuất URLs từ các thuộc tính khác nhau
   * @param $ Cheerio instance
   * @param selector CSS selector
   * @param attribute Thuộc tính chứa URL
   * @param baseUrl Base URL để resolve relative URLs
   * @param childUrls Set để lưu URLs
   */
  private extractUrlsFromAttribute($: any, selector: string, attribute: string, baseUrl: URL, childUrls: Set<string>): void {
    $(selector).each((_: any, element: any) => {
      const attrValue = $(element).attr(attribute);
      if (attrValue) {
        const processedUrl = this.processUrl(attrValue, baseUrl);
        if (processedUrl) {
          childUrls.add(processedUrl);
        }
      }
    });
  }

  /**
   * Kiểm tra xem URL có phải là file không nên crawl không
   * @param url URL cần kiểm tra
   * @returns true nếu nên bỏ qua, false nếu có thể crawl
   */
  private shouldSkipUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();

      // Bỏ qua các file extension không phù hợp
      const skipExtensions = [
        '.xml', '.rss', '.atom', '.json', '.txt', '.csv',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
        '.zip', '.rar', '.tar', '.gz', '.7z',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp',
        '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
        '.css', '.js', '.ico', '.woff', '.woff2', '.ttf', '.eot'
      ];

      for (const ext of skipExtensions) {
        if (pathname.endsWith(ext)) {
          return true;
        }
      }

      // Bỏ qua các URL pattern không phù hợp (giảm bớt để crawl nhiều URL hơn)
      const skipPatterns = [
        '/wp-admin/',
        '/wp-json/',
        '/feed/',
        '/rss/',
        '/robots.txt',
        '/favicon.ico',
        '/api/',
        '/ajax/'
      ];

      for (const pattern of skipPatterns) {
        if (pathname.includes(pattern) || urlObj.search.includes(pattern)) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return true; // Bỏ qua URL lỗi
    }
  }

  /**
   * Xử lý và chuẩn hóa URL
   * @param href URL cần xử lý
   * @param baseUrl Base URL
   * @returns URL đã chuẩn hóa hoặc null nếu không hợp lệ
   */
  private processUrl(href: string, baseUrl: URL): string | null {
    try {
      // Bỏ qua các URL không hợp lệ
      if (!href ||
          href.startsWith('#') ||
          href.startsWith('javascript:') ||
          href.startsWith('mailto:') ||
          href.startsWith('tel:') ||
          href.startsWith('sms:') ||
          href.startsWith('data:') ||
          href.includes('void(0)') ||
          href.trim() === '') {
        return null;
      }

      // Decode HTML entities
      href = href.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');

      // Xử lý URL để có URL tuyệt đối
      let absoluteUrl: string;

      if (href.startsWith('http://') || href.startsWith('https://')) {
        absoluteUrl = href;
      } else if (href.startsWith('//')) {
        absoluteUrl = `${baseUrl.protocol}${href}`;
      } else if (href.startsWith('/')) {
        absoluteUrl = `${baseUrl.origin}${href}`;
      } else {
        let basePath = baseUrl.pathname;
        if (!basePath.endsWith('/')) {
          basePath = basePath.substring(0, basePath.lastIndexOf('/') + 1);
        }
        absoluteUrl = `${baseUrl.origin}${basePath}${href}`;
      }

      // Chuẩn hóa URL
      const normalizedUrl = this.normalizeUrl(absoluteUrl);

      // Kiểm tra xem có nên bỏ qua URL này không
      if (this.shouldSkipUrl(normalizedUrl)) {
        return null;
      }

      // Kiểm tra domain
      const urlObj = new URL(normalizedUrl);
      const urlDomain = this.extractMainDomain(urlObj.hostname);
      const baseDomain = this.extractMainDomain(baseUrl.hostname);

      if (urlDomain === baseDomain) {
        return normalizedUrl;
      }

      return null;
    } catch (urlError) {
      return null;
    }
  }

  /**
   * Trích xuất URLs từ JSON-LD và structured data
   * @param html HTML content
   * @param baseUrl Base URL
   * @returns Array of URLs
   */
  private extractUrlsFromStructuredData(html: string, baseUrl: URL): string[] {
    const urls: string[] = [];

    try {
      // Trích xuất từ JSON-LD
      const jsonLdMatches = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([\s\S]*?)<\/script>/gi);
      if (jsonLdMatches) {
        for (const match of jsonLdMatches) {
          try {
            const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '');
            const data = JSON.parse(jsonContent);
            this.extractUrlsFromJsonLd(data, baseUrl, urls);
          } catch (e) {
            // Ignore invalid JSON
          }
        }
      }

      // Trích xuất từ Open Graph
      const ogUrlMatch = html.match(/<meta[^>]*property=["']og:url["'][^>]*content=["']([^"']+)["']/i);
      if (ogUrlMatch && ogUrlMatch[1]) {
        const processedUrl = this.processUrl(ogUrlMatch[1], baseUrl);
        if (processedUrl) {
          urls.push(processedUrl);
        }
      }

      // Trích xuất từ canonical link
      const canonicalMatch = html.match(/<link[^>]*rel=["']canonical["'][^>]*href=["']([^"']+)["']/i);
      if (canonicalMatch && canonicalMatch[1]) {
        const processedUrl = this.processUrl(canonicalMatch[1], baseUrl);
        if (processedUrl) {
          urls.push(processedUrl);
        }
      }

    } catch (error) {
      this.logger.debug(`Error extracting URLs from structured data: ${error.message}`);
    }

    return urls;
  }

  /**
   * Trích xuất URLs từ JSON-LD data
   * @param data JSON-LD data
   * @param baseUrl Base URL
   * @param urls Array to collect URLs
   */
  private extractUrlsFromJsonLd(data: any, baseUrl: URL, urls: string[]): void {
    if (!data) return;

    // Handle arrays
    if (Array.isArray(data)) {
      for (const item of data) {
        this.extractUrlsFromJsonLd(item, baseUrl, urls);
      }
      return;
    }

    // Handle objects
    if (typeof data === 'object') {
      for (const [key, value] of Object.entries(data)) {
        if (key === 'url' || key === '@id' || key === 'sameAs') {
          if (typeof value === 'string') {
            const processedUrl = this.processUrl(value, baseUrl);
            if (processedUrl) {
              urls.push(processedUrl);
            }
          } else if (Array.isArray(value)) {
            for (const item of value) {
              if (typeof item === 'string') {
                const processedUrl = this.processUrl(item, baseUrl);
                if (processedUrl) {
                  urls.push(processedUrl);
                }
              }
            }
          }
        } else {
          this.extractUrlsFromJsonLd(value, baseUrl, urls);
        }
      }
    }
  }

  /**
   * Trích xuất URLs từ sitemap.xml nếu có
   * @param baseUrl Base URL
   * @returns Array of URLs from sitemap
   */
  private async extractUrlsFromSitemap(baseUrl: URL): Promise<string[]> {
    const urls: string[] = [];

    try {
      const sitemapUrls = [
        `${baseUrl.origin}/sitemap.xml`,
        `${baseUrl.origin}/sitemap_index.xml`,
        `${baseUrl.origin}/robots.txt` // Để tìm sitemap trong robots.txt
      ];

      for (const sitemapUrl of sitemapUrls) {
        try {
          await this.waitForRateLimit(sitemapUrl);
          const response = await firstValueFrom(this.httpService.get(sitemapUrl, {
            timeout: 10000,
            headers: { 'User-Agent': 'Mozilla/5.0' }
          }));

          const content = response.data;

          if (sitemapUrl.endsWith('robots.txt')) {
            // Tìm sitemap trong robots.txt
            const sitemapMatches = content.match(/Sitemap:\s*(.+)/gi);
            if (sitemapMatches) {
              for (const match of sitemapMatches) {
                const sitemapMatch = match.match(/Sitemap:\s*(.+)/i);
                if (sitemapMatch && sitemapMatch[1]) {
                  const processedUrl = this.processUrl(sitemapMatch[1].trim(), baseUrl);
                  if (processedUrl) {
                    urls.push(processedUrl);
                  }
                }
              }
            }
          } else {
            // Parse XML sitemap
            const urlMatches = content.match(/<loc>(.*?)<\/loc>/gi);
            if (urlMatches) {
              for (const match of urlMatches) {
                const urlMatch = match.match(/<loc>(.*?)<\/loc>/i);
                if (urlMatch && urlMatch[1]) {
                  const processedUrl = this.processUrl(urlMatch[1], baseUrl);
                  if (processedUrl) {
                    urls.push(processedUrl);
                  }
                }
              }
            }
          }
        } catch (error) {
          // Ignore sitemap errors
          this.logger.debug(`Could not fetch sitemap ${sitemapUrl}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.debug(`Error extracting URLs from sitemap: ${error.message}`);
    }

    return urls.slice(0, 50); // Giới hạn 50 URLs từ sitemap
  }

  /**
   * Smart crawling với auto-detection của loại trang web
   * @param url URL cần crawl
   * @param maxRetries Số lần retry tối đa
   * @returns HTML content và URLs
   */
  private async smartCrawl(url: string, maxRetries = 3): Promise<{
    html: string;
    urls: string[];
    metadata?: any;
  }> {
    try {
      this.logger.log(`🧠 Smart crawling: ${url}`);

      // Phát hiện loại trang web
      const websiteType = await this.advancedCrawlerService.detectWebsiteType(url);
      this.logger.log(`📊 Website type detected: ${websiteType.type} (${websiteType.framework || 'unknown'}) - Confidence: ${Math.round(websiteType.confidence * 100)}%`);

      if (websiteType.needsBrowser && websiteType.confidence > 0.4) { // Giảm threshold từ 0.7 xuống 0.4
        // Sử dụng browser automation cho trang web phức tạp
        this.logger.log(`🚀 Using browser automation for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`);

        const result = await this.advancedCrawlerService.crawlWithBrowser(url, {
          waitTime: this.getOptimalWaitTime(websiteType),
          scrollToBottom: this.shouldScrollToBottom(websiteType),
          extractUrls: true,
          takeScreenshot: false,
          waitForSelector: this.getWaitSelector(websiteType)
        });

        return {
          html: result.html,
          urls: result.urls,
          metadata: result.metadata
        };
      } else {
        // Sử dụng traditional HTTP crawling
        this.logger.log(`📄 Using traditional HTTP crawling for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`);
        const html = await this.fetchOptimalHtml(url, maxRetries);
        const urls = await this.extractChildUrlsFromHtml(html, url);

        return { html, urls };
      }
    } catch (error) {
      this.logger.warn(`Smart crawl failed for ${url}, falling back to traditional method: ${error.message}`);

      // Fallback to traditional method
      const html = await this.fetchOptimalHtml(url, maxRetries);
      const urls = await this.extractChildUrlsFromHtml(html, url);

      return { html, urls };
    }
  }

  /**
   * Lấy thời gian chờ tối ưu dựa trên loại website
   */
  private getOptimalWaitTime(websiteType: any): number {
    switch (websiteType.type) {
      case 'spa':
        return websiteType.framework === 'React' ? 4000 : 3000;
      case 'complex':
        return 5000;
      case 'dynamic':
        return 2000;
      default:
        return 1000;
    }
  }

  /**
   * Kiểm tra có nên scroll to bottom không
   */
  private shouldScrollToBottom(websiteType: any): boolean {
    return websiteType.type === 'dynamic' ||
           websiteType.type === 'complex' ||
           (websiteType.framework && websiteType.framework.includes('React'));
  }

  /**
   * Lấy selector để chờ dựa trên framework
   */
  private getWaitSelector(websiteType: any): string | undefined {
    if (websiteType.framework === 'React') {
      return '[data-reactroot], #root > *, .App';
    }
    if (websiteType.framework === 'Vue') {
      return '#app > *, [data-v-]';
    }
    if (websiteType.framework === 'Angular') {
      return 'app-root, [ng-version]';
    }
    if (websiteType.framework === 'Haravan/Shopify' || websiteType.framework === 'Shopify') {
      return '.main-content, .content, .container, nav, .navbar, .menu';
    }
    if (websiteType.framework === 'jQuery') {
      return 'nav, .navbar, .menu, .main-content, .content';
    }
    if (websiteType.type === 'spa') {
      return 'main, .main-content, .container';
    }
    if (websiteType.type === 'dynamic') {
      return 'nav, .navbar, .menu, .content, main';
    }
    return undefined;
  }

  /**
   * Trích xuất URLs từ HTML content
   * @param html HTML content
   * @param url Base URL
   * @returns Danh sách URLs
   */
  private async extractChildUrlsFromHtml(html: string, url: string): Promise<string[]> {
    const $ = cheerio.load(html);
    const baseUrl = new URL(url);
    const childUrls = new Set<string>();

    this.logger.debug(`Trích xuất URLs từ HTML của ${url}`);

    // 1. Trích xuất từ các thẻ <a> truyền thống
    this.extractUrlsFromAttribute($, 'a[href]', 'href', baseUrl, childUrls);

    // 2. Trích xuất từ các thẻ có data-href, data-url
    this.extractUrlsFromAttribute($, '[data-href]', 'data-href', baseUrl, childUrls);
    this.extractUrlsFromAttribute($, '[data-url]', 'data-url', baseUrl, childUrls);
    this.extractUrlsFromAttribute($, '[data-link]', 'data-link', baseUrl, childUrls);
    this.extractUrlsFromAttribute($, '[data-target]', 'data-target', baseUrl, childUrls);

    // 3. Trích xuất từ các thẻ có onclick với URL
    $('[onclick]').each((_: any, element: any) => {
      const onclick = $(element).attr('onclick');
      if (onclick) {
        const patterns = [
          /(?:location\.href|window\.location|goto|redirect)\s*=\s*['"`]([^'"`]+)['"`]/gi,
          /window\.open\s*\(\s*['"`]([^'"`]+)['"`]/gi,
          /navigate\s*\(\s*['"`]([^'"`]+)['"`]/gi,
          /href\s*=\s*['"`]([^'"`]+)['"`]/gi
        ];

        for (const pattern of patterns) {
          const urlMatches = onclick.match(pattern);
          if (urlMatches) {
            for (const match of urlMatches) {
              const urlMatch = match.match(/['"`]([^'"`]+)['"`]/);
              if (urlMatch && urlMatch[1]) {
                const processedUrl = this.processUrl(urlMatch[1], baseUrl);
                if (processedUrl) {
                  childUrls.add(processedUrl);
                }
              }
            }
          }
        }
      }
    });

    // 4. Trích xuất từ form actions
    this.extractUrlsFromAttribute($, 'form[action]', 'action', baseUrl, childUrls);

    // 5. Trích xuất từ iframe src
    this.extractUrlsFromAttribute($, 'iframe[src]', 'src', baseUrl, childUrls);

    // 6. Trích xuất từ link rel="next", "prev", "alternate"
    this.extractUrlsFromAttribute($, 'link[rel="next"]', 'href', baseUrl, childUrls);
    this.extractUrlsFromAttribute($, 'link[rel="prev"]', 'href', baseUrl, childUrls);
    this.extractUrlsFromAttribute($, 'link[rel="alternate"]', 'href', baseUrl, childUrls);

    // 7. Trích xuất từ structured data (JSON-LD, Open Graph, etc.)
    const structuredUrls = this.extractUrlsFromStructuredData(html, baseUrl);
    structuredUrls.forEach(structuredUrl => childUrls.add(structuredUrl));

    // 8. Trích xuất từ JavaScript variables
    const scriptMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
    if (scriptMatches) {
      for (const script of scriptMatches) {
        const patterns = [
          /['"`](https?:\/\/[^'"`\s]+)['"`]/g,
          /url\s*:\s*['"`]([^'"`]+)['"`]/g,
          /href\s*:\s*['"`]([^'"`]+)['"`]/g,
          /link\s*:\s*['"`]([^'"`]+)['"`]/g
        ];

        for (const pattern of patterns) {
          const jsUrlMatches = script.match(pattern);
          if (jsUrlMatches) {
            for (const jsUrl of jsUrlMatches) {
              const cleanUrl = jsUrl.replace(/.*['"`]([^'"`]+)['"`].*/, '$1');
              if (cleanUrl && (cleanUrl.startsWith('http') || cleanUrl.startsWith('/'))) {
                const processedUrl = this.processUrl(cleanUrl, baseUrl);
                if (processedUrl) {
                  childUrls.add(processedUrl);
                }
              }
            }
          }
        }
      }
    }

    // 9. Trích xuất từ navigation menus và các selectors mở rộng
    const navSelectors = [
      'nav a[href]', '.navigation a[href]', '.menu a[href]', '.navbar a[href]',
      '.nav-menu a[href]', '.main-menu a[href]', '.sidebar a[href]',
      '.breadcrumb a[href]', '.pagination a[href]',
      // Thêm các selectors cho e-commerce
      '.product-item a[href]', '.product-link[href]', '.category-link[href]',
      '.collection-item a[href]', '.item-link[href]', '.product a[href]',
      // Thêm các selectors chung
      '.content a[href]', '.main a[href]', '.container a[href]',
      '.grid a[href]', '.list a[href]', '.items a[href]'
    ];

    for (const selector of navSelectors) {
      this.extractUrlsFromAttribute($, selector, 'href', baseUrl, childUrls);
    }

    return Array.from(childUrls);
  }

  /**
   * Trích xuất URLs với nhiều chiến lược khác nhau
   * @param html HTML content
   * @param url Base URL
   * @returns Danh sách URLs
   */
  private async extractChildUrlsAggressive(html: string, url: string): Promise<string[]> {
    const $ = cheerio.load(html);
    const baseUrl = new URL(url);
    const childUrls = new Set<string>();

    this.logger.debug(`🔍 Aggressive URL extraction từ HTML của ${url}`);

    // 1. Tất cả các thẻ a
    $('a').each((_: any, element: any) => {
      const href = $(element).attr('href');
      if (href) {
        const processedUrl = this.processUrl(href, baseUrl);
        if (processedUrl) {
          childUrls.add(processedUrl);
        }
      }
    });

    // 2. Tìm URLs trong text content (cho các website dynamic)
    const textContent = $.text();
    const urlRegex = /https?:\/\/[^\s<>"']+/g;
    const foundUrls = textContent.match(urlRegex);
    if (foundUrls) {
      for (const foundUrl of foundUrls) {
        try {
          const urlObj = new URL(foundUrl);
          if (this.extractMainDomain(urlObj.hostname) === this.extractMainDomain(baseUrl.hostname)) {
            const normalizedUrl = this.normalizeUrl(foundUrl);
            if (!this.shouldSkipUrl(normalizedUrl)) {
              childUrls.add(normalizedUrl);
            }
          }
        } catch (e) {
          // Invalid URL
        }
      }
    }

    // 3. Tìm relative URLs trong text
    const relativeUrlRegex = /\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+/g;
    const relativeUrls = textContent.match(relativeUrlRegex);
    if (relativeUrls) {
      for (const relativeUrl of relativeUrls) {
        if (relativeUrl.length > 2 && !relativeUrl.includes(' ')) {
          const processedUrl = this.processUrl(relativeUrl, baseUrl);
          if (processedUrl) {
            childUrls.add(processedUrl);
          }
        }
      }
    }

    // 4. Tìm trong script tags
    $('script').each((_: any, element: any) => {
      const scriptContent = $(element).html();
      if (scriptContent) {
        const patterns = [
          /"(\/[^"]+)"/g,
          /'(\/[^']+)'/g,
          /url:\s*["']([^"']+)["']/g,
          /href:\s*["']([^"']+)["']/g
        ];

        for (const pattern of patterns) {
          let match;
          while ((match = pattern.exec(scriptContent)) !== null) {
            const foundUrl = match[1];
            if (foundUrl && foundUrl.startsWith('/')) {
              const processedUrl = this.processUrl(foundUrl, baseUrl);
              if (processedUrl) {
                childUrls.add(processedUrl);
              }
            }
          }
        }
      }
    });

    this.logger.debug(`🔍 Aggressive extraction found ${childUrls.size} URLs`);
    return Array.from(childUrls);
  }

  /**
   * Trích xuất các URL con từ một URL với smart crawling
   * @param url URL cần trích xuất
   * @returns Danh sách các URL con
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      // Sử dụng smart crawling
      const result = await this.smartCrawl(url);

      if (result.urls && result.urls.length > 0) {
        // Nếu smart crawl đã trích xuất URLs, sử dụng kết quả đó
        this.logger.log(`Smart crawl extracted ${result.urls.length} URLs from ${url}`);
        return result.urls;
      } else {
        // Fallback: sử dụng cả normal và aggressive extraction
        this.logger.log(`Fallback: extracting URLs from HTML for ${url}`);
        const normalUrls = await this.extractChildUrlsFromHtml(result.html, url);
        const aggressiveUrls = await this.extractChildUrlsAggressive(result.html, url);

        // Combine và deduplicate
        const allUrls = [...new Set([...normalUrls, ...aggressiveUrls])];
        this.logger.log(`Combined extraction: ${normalUrls.length} normal + ${aggressiveUrls.length} aggressive = ${allUrls.length} total URLs`);
        return allUrls;
      }
    } catch (error) {
      this.logger.warn(`Error in extractChildUrls for ${url}: ${error.message}`);

      // Final fallback: traditional method
      try {
        const html = await this.fetchOptimalHtml(url);
        return await this.extractChildUrlsFromHtml(html, url);
      } catch (fallbackError) {
        this.logger.error(`All extraction methods failed for ${url}: ${fallbackError.message}`);
        return [];
      }
    }
  }

  /**
   * Legacy method - Trích xuất các URL con từ một URL với nhiều phương pháp
   * @param url URL cần trích xuất
   * @returns Danh sách các URL con
   */
  private async extractChildUrlsLegacy(url: string): Promise<string[]> {
    try {
      // Fetch HTML với phương pháp thích ứng để lấy các URL con
      const html = await this.fetchOptimalHtml(url);
      const $ = cheerio.load(html);
      const baseUrl = new URL(url);
      const childUrls = new Set<string>();

      this.logger.debug(`Trích xuất URLs từ ${url}`);

      // 1. Trích xuất từ các thẻ <a> truyền thống
      this.extractUrlsFromAttribute($, 'a[href]', 'href', baseUrl, childUrls);

      // 2. Trích xuất từ các thẻ có data-href, data-url
      this.extractUrlsFromAttribute($, '[data-href]', 'data-href', baseUrl, childUrls);
      this.extractUrlsFromAttribute($, '[data-url]', 'data-url', baseUrl, childUrls);
      this.extractUrlsFromAttribute($, '[data-link]', 'data-link', baseUrl, childUrls);
      this.extractUrlsFromAttribute($, '[data-target]', 'data-target', baseUrl, childUrls);

      // 3. Trích xuất từ các thẻ có onclick với URL
      $('[onclick]').each((_: any, element: any) => {
        const onclick = $(element).attr('onclick');
        if (onclick) {
          // Tìm URLs trong onclick events - mở rộng pattern
          const patterns = [
            /(?:location\.href|window\.location|goto|redirect)\s*=\s*['"`]([^'"`]+)['"`]/gi,
            /window\.open\s*\(\s*['"`]([^'"`]+)['"`]/gi,
            /navigate\s*\(\s*['"`]([^'"`]+)['"`]/gi,
            /href\s*=\s*['"`]([^'"`]+)['"`]/gi
          ];

          for (const pattern of patterns) {
            const urlMatches = onclick.match(pattern);
            if (urlMatches) {
              for (const match of urlMatches) {
                const urlMatch = match.match(/['"`]([^'"`]+)['"`]/);
                if (urlMatch && urlMatch[1]) {
                  const processedUrl = this.processUrl(urlMatch[1], baseUrl);
                  if (processedUrl) {
                    childUrls.add(processedUrl);
                  }
                }
              }
            }
          }
        }
      });

      // 4. Trích xuất từ form actions
      this.extractUrlsFromAttribute($, 'form[action]', 'action', baseUrl, childUrls);

      // 5. Trích xuất từ iframe src
      this.extractUrlsFromAttribute($, 'iframe[src]', 'src', baseUrl, childUrls);

      // 6. Trích xuất từ link rel="next", "prev", "alternate"
      this.extractUrlsFromAttribute($, 'link[rel="next"]', 'href', baseUrl, childUrls);
      this.extractUrlsFromAttribute($, 'link[rel="prev"]', 'href', baseUrl, childUrls);
      this.extractUrlsFromAttribute($, 'link[rel="alternate"]', 'href', baseUrl, childUrls);

      // 7. Trích xuất từ structured data (JSON-LD, Open Graph, etc.)
      const structuredUrls = this.extractUrlsFromStructuredData(html, baseUrl);
      structuredUrls.forEach(structuredUrl => childUrls.add(structuredUrl));

      // 8. Trích xuất từ JavaScript variables (mở rộng)
      const scriptMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
      if (scriptMatches) {
        for (const script of scriptMatches) {
          // Tìm URLs trong JavaScript với nhiều pattern
          const patterns = [
            /['"`](https?:\/\/[^'"`\s]+)['"`]/g,
            /url\s*:\s*['"`]([^'"`]+)['"`]/g,
            /href\s*:\s*['"`]([^'"`]+)['"`]/g,
            /link\s*:\s*['"`]([^'"`]+)['"`]/g
          ];

          for (const pattern of patterns) {
            const jsUrlMatches = script.match(pattern);
            if (jsUrlMatches) {
              for (const jsUrl of jsUrlMatches) {
                const cleanUrl = jsUrl.replace(/.*['"`]([^'"`]+)['"`].*/, '$1');
                if (cleanUrl && (cleanUrl.startsWith('http') || cleanUrl.startsWith('/'))) {
                  const processedUrl = this.processUrl(cleanUrl, baseUrl);
                  if (processedUrl) {
                    childUrls.add(processedUrl);
                  }
                }
              }
            }
          }
        }
      }

      // 9. Trích xuất từ CSS (background-image URLs)
      const cssMatches = html.match(/url\(['"`]?([^'"`\)]+)['"`]?\)/gi);
      if (cssMatches) {
        for (const cssMatch of cssMatches) {
          const urlMatch = cssMatch.match(/url\(['"`]?([^'"`\)]+)['"`]?\)/i);
          if (urlMatch && urlMatch[1] && (urlMatch[1].startsWith('http') || urlMatch[1].startsWith('/'))) {
            const processedUrl = this.processUrl(urlMatch[1], baseUrl);
            if (processedUrl) {
              childUrls.add(processedUrl);
            }
          }
        }
      }

      // 10. Trích xuất từ meta refresh
      const metaRefresh = $('meta[http-equiv="refresh"]').attr('content');
      if (metaRefresh) {
        const urlMatch = metaRefresh.match(/url=(.+)/i);
        if (urlMatch && urlMatch[1]) {
          const processedUrl = this.processUrl(urlMatch[1], baseUrl);
          if (processedUrl) {
            childUrls.add(processedUrl);
          }
        }
      }

      // 11. Trích xuất từ navigation menus (common selectors)
      const navSelectors = [
        'nav a[href]',
        '.navigation a[href]',
        '.menu a[href]',
        '.navbar a[href]',
        '.nav-menu a[href]',
        '.main-menu a[href]',
        '.sidebar a[href]',
        '.breadcrumb a[href]',
        '.pagination a[href]'
      ];

      for (const selector of navSelectors) {
        this.extractUrlsFromAttribute($, selector, 'href', baseUrl, childUrls);
      }

      // 12. Nếu là trang chủ, thử trích xuất từ sitemap
      if (baseUrl.pathname === '/' || baseUrl.pathname === '') {
        try {
          const sitemapUrls = await this.extractUrlsFromSitemap(baseUrl);
          sitemapUrls.forEach(sitemapUrl => childUrls.add(sitemapUrl));
        } catch (error) {
          this.logger.debug(`Could not extract from sitemap: ${error.message}`);
        }
      }

      const urlArray = Array.from(childUrls);
      this.logger.log(`Đã trích xuất ${urlArray.length} URLs từ ${url}`);

      // Log một vài URLs đầu tiên để debug
      if (urlArray.length > 0) {
        this.logger.debug(`URLs đầu tiên: ${urlArray.slice(0, 5).join(', ')}`);
      }

      // Sắp xếp URLs theo độ ưu tiên (trang chủ, navigation trước)
      return this.prioritizeUrls(urlArray, baseUrl);
    } catch (error) {
      this.logger.warn(`Lỗi khi trích xuất URL con từ ${url}: ${error.message}`);
      return [];
    }
  }

  /**
   * Sắp xếp URLs theo độ ưu tiên
   * @param urls Array of URLs
   * @param baseUrl Base URL
   * @returns Sorted array of URLs
   */
  private prioritizeUrls(urls: string[], baseUrl: URL): string[] {
    return urls.sort((a, b) => {
      try {
        const urlA = new URL(a);
        const urlB = new URL(b);

        // Ưu tiên trang chủ và các trang chính
        const priorityPaths = ['/', '/about', '/contact', '/products', '/services', '/blog', '/news'];
        const pathA = urlA.pathname.toLowerCase();
        const pathB = urlB.pathname.toLowerCase();

        const priorityA = priorityPaths.indexOf(pathA);
        const priorityB = priorityPaths.indexOf(pathB);

        if (priorityA !== -1 && priorityB !== -1) {
          return priorityA - priorityB;
        }
        if (priorityA !== -1) return -1;
        if (priorityB !== -1) return 1;

        // Ưu tiên URLs ngắn hơn (thường là trang quan trọng hơn)
        const depthA = pathA.split('/').length;
        const depthB = pathB.split('/').length;

        return depthA - depthB;
      } catch (error) {
        return 0;
      }
    });
  }

  /**
   * Xử lý batch URLs song song với smart crawling
   * @param batch Batch URLs cần xử lý
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Array metadata đã trích xuất
   */
  private async processConcurrentUrlsWithSmartCrawling(
    batch: Array<{url: string, depth: number}>,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{url: string, depth: number}>,
    errors: string[]
  ): Promise<ExtractedMetadata[]> {
    const results: ExtractedMetadata[] = [];

    // Xử lý song song với Promise.allSettled để không bị block bởi lỗi
    const promises = batch.map(async (item) => {
      try {
        return await this.processSingleUrlWithSmartCrawling(
          item.url,
          item.depth,
          userId,
          crawlDto,
          visitedUrls,
          urlsToVisit,
          errors
        );
      } catch (error) {
        const errorMsg = `Lỗi khi xử lý URL ${item.url}: ${error.message}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
        return null;
      }
    });

    const settledResults = await Promise.allSettled(promises);

    // Thu thập kết quả thành công
    for (const result of settledResults) {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      } else if (result.status === 'rejected') {
        const errorMsg = `Promise rejected: ${result.reason}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
      }
    }

    return results;
  }

  /**
   * Xử lý một URL đơn lẻ với smart crawling và metadata extraction
   * @param url URL cần xử lý
   * @param depth Độ sâu hiện tại
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Metadata đã trích xuất hoặc null nếu thất bại
   */
  private async processSingleUrlWithSmartCrawling(
    url: string,
    depth: number,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{url: string, depth: number}>,
    errors: string[]
  ): Promise<ExtractedMetadata | null> {
    try {
      this.logger.log(`🧠 Smart processing URL: ${url} (độ sâu: ${depth})`);

      // Kiểm tra metadata cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Metadata cache hit for URL: ${normalizedUrl}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          try {
            await this.waitForRateLimit(url);
            const childUrls = await this.extractChildUrls(url); // Đã sử dụng smart crawling
            for (const childUrl of childUrls) {
              if (!visitedUrls.has(childUrl) && urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)) {
                urlsToVisit.push({url: childUrl, depth: depth + 1});
              }
            }
          } catch (childError) {
            this.logger.warn(`Lỗi khi trích xuất child URLs từ cache: ${childError.message}`);
          }
        }

        return cached.metadata;
      }

      // Áp dụng rate limiting trước khi crawl
      await this.waitForRateLimit(url);

      // Sử dụng smart crawling thay vì fetchOptimalHtml
      const crawlResult = await this.smartCrawl(url);

      // Trích xuất metadata từ HTML
      const metadata = this.extractHeadMetadata(crawlResult.html, url);

      // Trích xuất metadata từ JSON-LD nếu có
      const jsonLdData = this.extractJsonLdMetadata(crawlResult.html);

      // Kết hợp metadata từ JSON-LD nếu có
      if (jsonLdData && jsonLdData.length > 0) {
        const jsonLd = jsonLdData[0];

        if (!metadata.title && jsonLd.name) {
          metadata.title = jsonLd.name;
        }

        if (!metadata.content && jsonLd.description) {
          metadata.content = jsonLd.description;
        }

        if (!metadata.tags && jsonLd.keywords) {
          if (Array.isArray(jsonLd.keywords)) {
            metadata.tags = jsonLd.keywords.join(', ');
          } else if (typeof jsonLd.keywords === 'string') {
            metadata.tags = jsonLd.keywords;
          }
        }
      }

      // Cache metadata
      this.metadataCache.set(normalizedUrl, {
        metadata,
        expiry: Date.now() + this.METADATA_CACHE_TTL
      });

      // Trích xuất child URLs nếu chưa đạt độ sâu tối đa
      if (depth < crawlDto.depth) {
        try {
          // Sử dụng URLs từ smart crawl nếu có, nếu không thì extract từ HTML
          let childUrls: string[] = [];

          if (crawlResult.urls && crawlResult.urls.length > 0) {
            childUrls = crawlResult.urls;
            this.logger.log(`Smart crawl đã trích xuất ${childUrls.length} child URLs`);
          } else {
            // Sử dụng cả normal và aggressive extraction
            const normalUrls = await this.extractChildUrlsFromHtml(crawlResult.html, url);
            const aggressiveUrls = await this.extractChildUrlsAggressive(crawlResult.html, url);
            childUrls = [...new Set([...normalUrls, ...aggressiveUrls])];
            this.logger.log(`Combined extraction: ${normalUrls.length} normal + ${aggressiveUrls.length} aggressive = ${childUrls.length} total child URLs`);
          }

          for (const childUrl of childUrls) {
            if (!visitedUrls.has(childUrl) && urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)) {
              urlsToVisit.push({url: childUrl, depth: depth + 1});
            }
          }
        } catch (childError) {
          this.logger.warn(`Lỗi khi trích xuất child URLs: ${childError.message}`);
        }
      }

      return metadata;
    } catch (error) {
      const errorMsg = `Lỗi khi xử lý URL ${url}: ${error.message}`;
      this.logger.warn(errorMsg);
      errors.push(errorMsg);
      return null;
    }
  }

  /**
   * Xử lý một URL đơn lẻ với metadata extraction và cache (Legacy method)
   * @param url URL cần xử lý
   * @param depth Độ sâu hiện tại
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Metadata đã trích xuất hoặc null nếu thất bại
   */
  private async processSingleUrl(
    url: string,
    depth: number,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{url: string, depth: number}>,
    errors: string[]
  ): Promise<ExtractedMetadata | null> {
    try {
      this.logger.log(`Xử lý URL: ${url} (độ sâu: ${depth})`);

      // Kiểm tra metadata cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Metadata cache hit for URL: ${normalizedUrl}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          try {
            // Áp dụng rate limiting cho child URL extraction
            await this.waitForRateLimit(url);
            const childUrls = await this.extractChildUrls(url);
            for (const childUrl of childUrls) {
              if (!visitedUrls.has(childUrl) && urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)) {
                urlsToVisit.push({url: childUrl, depth: depth + 1});
              }
            }
          } catch (childError) {
            this.logger.warn(`Lỗi khi trích xuất child URLs từ cache: ${childError.message}`);
          }
        }

        return cached.metadata;
      }

      // Áp dụng rate limiting trước khi fetch
      await this.waitForRateLimit(url);

      // Fetch HTML từ URL với phương pháp thích ứng
      const html = await this.fetchOptimalHtml(url);

      // Trích xuất metadata từ thẻ head
      const metadata = this.extractHeadMetadata(html, url);

      // Trích xuất metadata từ JSON-LD nếu có
      const jsonLdData = this.extractJsonLdMetadata(html);

      // Kết hợp metadata từ JSON-LD nếu có
      if (jsonLdData && jsonLdData.length > 0) {
        const jsonLd = jsonLdData[0];

        // Bổ sung title nếu chưa có
        if (!metadata.title && jsonLd.name) {
          metadata.title = jsonLd.name;
        }

        // Bổ sung content nếu chưa có
        if (!metadata.content && jsonLd.description) {
          metadata.content = jsonLd.description;
        }

        // Bổ sung tags nếu có
        if (!metadata.tags && jsonLd.keywords) {
          if (Array.isArray(jsonLd.keywords)) {
            metadata.tags = jsonLd.keywords.join(', ');
          } else if (typeof jsonLd.keywords === 'string') {
            metadata.tags = jsonLd.keywords;
          }
        }
      }

      // Cache metadata nếu hợp lệ
      if (this.isValidMetadata(metadata)) {
        this.metadataCache.set(normalizedUrl, {
          metadata,
          expiry: Date.now() + this.METADATA_CACHE_TTL
        });
        this.logger.debug(`Metadata cached for URL: ${normalizedUrl}`);
      }

      // Nếu chưa đạt độ sâu tối đa, trích xuất các URL con
      if (depth < crawlDto.depth) {
        // Rate limiting đã được áp dụng ở trên, không cần áp dụng lại
        const childUrls = await this.extractChildUrls(url);

        // Thêm các URL con vào hàng đợi
        for (const childUrl of childUrls) {
          if (!visitedUrls.has(childUrl) && urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)) {
            urlsToVisit.push({url: childUrl, depth: depth + 1});
          }
        }
      }

      return metadata;
    } catch (error) {
      const errorMsg = `Lỗi khi xử lý URL ${url}: ${error.message}`;
      this.logger.warn(errorMsg);
      errors.push(errorMsg);
      return null;
    }
  }

  /**
   * Xử lý song song một batch URLs với concurrency limit và batch save
   * @param urlBatch Batch URLs cần xử lý
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Mảng metadata đã xử lý thành công
   */
  private async processConcurrentUrls(
    urlBatch: Array<{url: string, depth: number}>,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{url: string, depth: number}>,
    errors: string[]
  ): Promise<ExtractedMetadata[]> {
    const promises = urlBatch.map(({url, depth}) =>
      this.processSingleUrl(url, depth, userId, crawlDto, visitedUrls, urlsToVisit, errors)
    );

    const results = await Promise.allSettled(promises);
    const successfulResults: ExtractedMetadata[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        successfulResults.push(result.value);
      } else if (result.status === 'rejected') {
        const errorMsg = `Lỗi xử lý URL ${urlBatch[index].url}: ${result.reason}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
      }
    });

    // Batch save tất cả metadata thành công
    if (successfulResults.length > 0) {
      const savedCount = await this.saveBatchMetadata(userId, successfulResults);
      this.logger.log(`Batch save: ${savedCount}/${successfulResults.length} metadata đã lưu thành công`);

      // Chỉ trả về những metadata đã lưu thành công
      if (savedCount < successfulResults.length) {
        this.logger.warn(`Một số metadata không được lưu thành công: ${successfulResults.length - savedCount}`);
      }
    }

    return successfulResults;
  }

  /**
   * Crawl URL và các URL con để lấy metadata từ thẻ head với xử lý song song và progress tracking
   * @param userId ID của người dùng
   * @param crawlDto Thông tin URL cần crawl
   * @param sessionId ID session để track progress (optional)
   * @returns Danh sách các URL với metadata
   */
  async crawlUrl(userId: number, crawlDto: CrawlDto, sessionId?: string): Promise<{
    status: string,
    message: string,
    urlsProcessed?: number,
    // results?: ExtractedMetadata[],
    errors?: string[]
  }> {
    // Mảng lưu trữ các lỗi gặp phải
    const errors: string[] = [];

    try {
      this.logger.log(`===== BẮT ĐẦU CRAWL URL =====`);
      this.logger.log(`User: ${userId}, URL: ${crawlDto.url}, Độ sâu: ${crawlDto.depth}`);

      // Kiểm tra URL có hợp lệ không
      try {
        const urlObj = new URL(crawlDto.url);
        this.logger.log(`URL hợp lệ: ${urlObj.href}`);

        // Chuẩn hóa URL
        const normalizedUrl = this.normalizeUrl(crawlDto.url);
        if (normalizedUrl !== crawlDto.url) {
          this.logger.log(`URL đã được chuẩn hóa: ${normalizedUrl}`);
          crawlDto.url = normalizedUrl;
        }

        // Kiểm tra robots.txt nếu không bỏ qua
        if (!crawlDto.ignoreRobotsTxt) {
          const isAllowed = await this.checkRobotsPermission(crawlDto.url);
          if (!isAllowed) {
            const robotsMsg = `URL không được phép crawl theo robots.txt: ${crawlDto.url}`;
            this.logger.error(robotsMsg);
            errors.push(robotsMsg);
            throw new AppException(
              URL_ERROR_CODES.URL_CRAWL_FAILED,
              robotsMsg
            );
          }
        } else {
          this.logger.log(`Bỏ qua kiểm tra robots.txt theo yêu cầu người dùng`);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }

        const errorMsg = `URL không đúng định dạng: ${error.message}`;
        this.logger.error(errorMsg);
        errors.push(errorMsg);
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_FORMAT,
          errorMsg
        );
      }

      // Giới hạn số lượng URL tối đa và concurrency - Tối ưu cho tốc độ với environment variables
      const MAX_URLS = crawlDto.maxUrls || 20;
      const CONCURRENCY_LIMIT = parseInt(process.env.CRAWL_CONCURRENCY_LIMIT || '5') || 5; // Configurable từ env
      this.logger.log(`Giới hạn tối đa: ${MAX_URLS} URLs, độ sâu tối đa: ${crawlDto.depth}, concurrency: ${CONCURRENCY_LIMIT}`);

      // Khởi tạo các biến cần thiết
      const visitedUrls = new Set<string>();
      const urlsToVisit: Array<{url: string, depth: number}> = [{url: crawlDto.url, depth: 0}];
      const processedUrls: ExtractedMetadata[] = [];
      const crawlErrors: CrawlError[] = [];

      // Khởi tạo progress tracking
      const startTime = Date.now();
      const progress: CrawlProgress = {
        totalUrls: 1, // Bắt đầu với 1 URL
        processedUrls: 0,
        successfulUrls: 0,
        failedUrls: 0,
        currentDepth: 0,
        startTime,
        errors: crawlErrors
      };

      // Tạo session ID nếu chưa có
      const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;

      this.logger.log(`===== BẮT ĐẦU VÒNG LẶP CRAWL URL VỚI XỬ LÝ SONG SONG [${trackingSessionId}] =====`);

      // Cập nhật progress ban đầu
      if (sessionId) {
        this.updateProgress(trackingSessionId, progress);
      }

      // Crawl URLs với xử lý song song
      while (urlsToVisit.length > 0 && visitedUrls.size < MAX_URLS) {
        // Cập nhật total URLs estimate
        progress.totalUrls = Math.min(visitedUrls.size + urlsToVisit.length, MAX_URLS);

        // Lấy batch URLs để xử lý song song
        const currentBatch: Array<{url: string, depth: number}> = [];

        // Lấy tối đa CONCURRENCY_LIMIT URLs chưa được xử lý
        while (currentBatch.length < CONCURRENCY_LIMIT && urlsToVisit.length > 0) {
          const currentItem = urlsToVisit.shift();
          if (!currentItem) continue;

          const {url} = currentItem;

          // Bỏ qua nếu URL đã được xử lý
          if (visitedUrls.has(url)) continue;

          // Đánh dấu URL đã được xử lý
          visitedUrls.add(url);
          currentBatch.push(currentItem);
        }

        if (currentBatch.length === 0) break;

        // Cập nhật current depth và URL
        progress.currentDepth = Math.max(...currentBatch.map(item => item.depth));
        progress.currentUrl = currentBatch[0].url;

        this.logger.log(`Xử lý batch ${currentBatch.length} URLs song song: ${currentBatch.map(item => item.url).join(', ')}`);

        // Xử lý batch URLs song song với smart crawling
        const batchResults = await this.processConcurrentUrlsWithSmartCrawling(
          currentBatch,
          userId,
          crawlDto,
          visitedUrls,
          urlsToVisit,
          errors
        );

        // Cập nhật progress
        progress.processedUrls += currentBatch.length;
        progress.successfulUrls += batchResults.length;
        progress.failedUrls += (currentBatch.length - batchResults.length);

        // Lưu batch metadata vào database
        if (batchResults.length > 0) {
          try {
            const savedCount = await this.saveBatchMetadata(userId, batchResults);
            this.logger.log(`💾 Batch save: ${savedCount}/${batchResults.length} metadata đã lưu vào database thành công`);

            if (savedCount < batchResults.length) {
              this.logger.warn(`⚠️ Một số metadata không được lưu thành công: ${batchResults.length - savedCount}`);
            }
          } catch (saveError) {
            this.logger.error(`❌ Lỗi khi lưu batch metadata: ${saveError.message}`);
            errors.push(`Lỗi lưu database: ${saveError.message}`);
          }
        }

        // Thêm kết quả thành công vào danh sách
        processedUrls.push(...batchResults);

        this.logger.log(`Hoàn thành batch: ${batchResults.length}/${currentBatch.length} URLs thành công`);

        // Cập nhật progress callback
        if (sessionId) {
          this.updateProgress(trackingSessionId, progress);
        }
      }

      // Tạo thông báo kết quả
      let resultMessage = '';
      if (processedUrls.length > 0) {
        resultMessage = `Đã crawl thành công ${processedUrls.length} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      } else {
        resultMessage = `Không crawl được URL nào từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      }

      this.logger.log(`===== KẾT THÚC CRAWL URL [${trackingSessionId}] =====`);
      this.logger.log(resultMessage);

      // Final progress update
      if (sessionId) {
        progress.currentUrl = undefined;
        this.updateProgress(trackingSessionId, progress);
        // Cleanup callback sau 30 giây
        setTimeout(() => {
          this.unregisterProgressCallback(trackingSessionId);
        }, 30000);
      }

      // Browser cleanup sau khi crawl hoàn thành
      try {
        this.logger.log(`🧹 Performing browser cleanup after crawl completion`);
        await this.advancedCrawlerService.closeBrowser();
      } catch (cleanupError) {
        this.logger.warn(`Browser cleanup warning: ${cleanupError.message}`);
      }

      return {
        status: 'success',
        message: resultMessage,
        urlsProcessed: processedUrls.length,
        // results: processedUrls,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      // Xử lý lỗi
      const errorMsg = error instanceof AppException ? error.message : `Lỗi không xác định: ${error.message}`;
      this.logger.error(`Crawl URL thất bại: ${errorMsg}`);

      // Cleanup progress tracking on error
      if (sessionId) {
        const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;
        this.unregisterProgressCallback(trackingSessionId);
      }

      // Emergency browser cleanup on error
      try {
        this.logger.log(`🚨 Emergency browser cleanup due to error`);
        await this.advancedCrawlerService.closeBrowser();
      } catch (cleanupError) {
        this.logger.warn(`Emergency cleanup warning: ${cleanupError.message}`);
      }

      return {
        status: 'error',
        message: errorMsg,
        urlsProcessed: 0,
        errors: errors.length > 0 ? [...errors, errorMsg] : [errorMsg]
      };
    }
  }



  /**
   * Tìm URL theo ID và kiểm tra quyền truy cập
   * @param userId ID của người dùng
   * @param id ID của URL
   * @returns Thông tin URL
   */
  async findUrlById(userId: number, id: string): Promise<Url> {
    try {
      this.logger.log(`Finding URL with ID: ${id} for user: ${userId}`);

      if (!id) {
        this.logger.error('URL ID is required');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'ID URL là bắt buộc'
        );
      }

      // Tìm URL theo ID
      const url = await this.urlCustomRepository.findUrlById(id);

      // Debug log - hiển thị toàn bộ thông tin URL
      this.logger.log(`URL data: ${JSON.stringify(url)}`);

      // Nếu không tìm thấy URL
      if (!url) {
        this.logger.warn(`URL with ID: ${id} not found`);
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          `URL với ID ${id} không tồn tại`
        );
      }

      // Kiểm tra quyền truy cập - đảm bảo so sánh số với số
      // Đảm bảo url.ownedBy là số hợp lệ
      const urlOwnerId = isNaN(Number(url.ownedBy)) ? 0 : Number(url.ownedBy);
      const currentUserId = Number(userId);

      this.logger.log(`Comparing ownership: URL ownedBy=${urlOwnerId} (${typeof url.ownedBy}), userId=${currentUserId} (${typeof userId})`);

      // So sánh số với số để tránh vấn đề về kiểu dữ liệu
      if (urlOwnerId !== currentUserId) {
        this.logger.warn(`User ${userId} does not have access to URL ${id}. URL owned by: ${url.ownedBy}`);
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền truy cập URL này'
        );
      }

      this.logger.log(`URL with ID: ${id} found for user: ${userId}`);
      this.logger.debug(`URL details: ${JSON.stringify(url)}`);

      return url;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding URL by ID: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy thông tin URL: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách URL của người dùng với phân trang và tìm kiếm
   * @param userId ID của người dùng
   * @param page Số trang
   * @param limit Số lượng kết quả mỗi trang
   * @param sortField Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @param keyword Từ khóa tìm kiếm
   * @param type Loại URL cần lọc
   * @param tags Các thẻ cần lọc
   * @returns Danh sách URL đã phân trang
   */
  async findUrlsByOwner(
    userId: number,
    page: number = 1,
    limit: number = 10,
    sortField: string = 'createdAt',
    sortDirection: SortDirection = SortDirection.DESC,
    keyword?: string,
    type?: string,
    tags?: string[]
  ): Promise<PaginatedResult<Url>> {
    try {
      this.logger.log(`Finding URLs for user: ${userId}, page: ${page}, limit: ${limit}`);

      // Kiểm tra tham số đầu vào
      if (!userId) {
        this.logger.error('User ID is required');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'ID người dùng là bắt buộc'
        );
      }

      if (page < 1) {
        this.logger.warn(`Invalid page number: ${page}, using default: 1`);
        page = 1;
      }

      if (limit < 1) {
        this.logger.warn(`Invalid limit: ${limit}, using default: 10`);
        limit = 10;
      }

      // Tạo đối tượng query params
      const queryParams: FindAllUrlDto = {
        page,
        limit,
        sortBy: sortField,
        sortDirection,
        keyword,
        type,
        tags
      };

      this.logger.debug(`Query params: ${JSON.stringify(queryParams)}`);

      // Sử dụng repository để lấy danh sách URL
      const result = await this.urlCustomRepository.findUrlsByOwner(userId, queryParams);

      this.logger.log(`Found ${result.items.length} URLs for user: ${userId}`);
      if (result.items.length > 0) {
        this.logger.debug(`First URL: ${JSON.stringify(result.items[0])}`);
      }

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding URLs for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`
      );
    }
  }

  /**
   * Tìm kiếm URL theo từ khóa
   * @param userId ID của người dùng
   * @param keyword Từ khóa tìm kiếm
   * @param limit Số lượng kết quả tối đa
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(userId: number, keyword: string, limit: number = 10): Promise<Url[]> {
    try {
      this.logger.log(`Searching URLs for user: ${userId}, keyword: ${keyword}, limit: ${limit}`);

      // Kiểm tra tham số đầu vào
      if (!keyword || keyword.trim() === '') {
        this.logger.warn('Empty keyword provided for search');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_SEARCH_PARAMS,
          'Từ khóa tìm kiếm không được để trống'
        );
      }

      // Tìm kiếm URL theo từ khóa
      const urls = await this.urlCustomRepository.searchUrls(keyword, limit);

      // Lọc kết quả theo người sở hữu
      const filteredUrls = urls.filter(url => url.ownedBy === userId);

      this.logger.log(`Found ${filteredUrls.length} URLs matching keyword: ${keyword} for user: ${userId}`);
      if (filteredUrls.length > 0) {
        this.logger.debug(`First search result: ${JSON.stringify(filteredUrls[0])}`);
      }

      return filteredUrls;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error searching URLs for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`
      );
    }
  }

  /**
   * Tạo URL mới
   * @param userId ID của người dùng
   * @param createUrlDto Thông tin URL cần tạo
   * @returns URL đã tạo
   */
  async createUrl(userId: number, createUrlDto: CreateUrlDto): Promise<Url> {
    try {
      // Kiểm tra URL có hợp lệ không
      try {
        new URL(createUrlDto.url);
      } catch (error) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_FORMAT,
          'URL không đúng định dạng'
        );
      }

      // Kiểm tra URL đã tồn tại chưa trong toàn bộ hệ thống
      const existingUrl = await this.urlRepository.findOne({
        where: {
          url: createUrlDto.url
        }
      });

      if (existingUrl) {
        throw new AppException(
          URL_ERROR_CODES.URL_ALREADY_EXISTS,
          'URL này đã tồn tại trong hệ thống'
        );
      }

      // Kiểm tra title và content không được để trống
      if (!createUrlDto.title || createUrlDto.title.trim() === '') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Tiêu đề URL không được để trống'
        );
      }

      if (!createUrlDto.content || createUrlDto.content.trim() === '') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Nội dung URL không được để trống'
        );
      }

      // Tạo URL mới
      const newUrl = this.urlRepository.create({
        ...createUrlDto,
        ownedBy: userId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      // Lưu URL vào database
      return this.urlRepository.save(newUrl);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_CREATION_FAILED,
        `Không thể tạo URL: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin URL
   * @param id ID của URL
   * @param userId ID của người dùng
   * @param updateUrlDto Thông tin cần cập nhật
   * @returns URL đã cập nhật
   */
  async updateUrl(id: string, userId: number, updateUrlDto: UpdateUrlDto): Promise<Url> {
    try {
      // Tìm URL theo ID và kiểm tra quyền truy cập
      const url = await this.findUrlById(userId, id);

      // Kiểm tra URL có hợp lệ không nếu có cập nhật URL
      if (updateUrlDto.url) {
        try {
          new URL(updateUrlDto.url);
        } catch (error) {
          throw new AppException(
            URL_ERROR_CODES.URL_INVALID_FORMAT,
            'URL không đúng định dạng'
          );
        }

        // Kiểm tra URL mới đã tồn tại chưa trong toàn bộ hệ thống (nếu khác URL cũ)
        if (updateUrlDto.url !== url.url) {
          const existingUrl = await this.urlRepository.findOne({
            where: {
              url: updateUrlDto.url
            }
          });

          if (existingUrl) {
            throw new AppException(
              URL_ERROR_CODES.URL_ALREADY_EXISTS,
              'URL này đã tồn tại trong hệ thống'
            );
          }
        }
      }

      // Kiểm tra title không được để trống nếu có cập nhật title
      if (updateUrlDto.title !== undefined && (updateUrlDto.title === null || updateUrlDto.title.trim() === '')) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Tiêu đề URL không được để trống'
        );
      }

      // Kiểm tra content không được để trống nếu có cập nhật content
      if (updateUrlDto.content !== undefined && (updateUrlDto.content === null || updateUrlDto.content.trim() === '')) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Nội dung URL không được để trống'
        );
      }

      // Cập nhật thông tin URL
      Object.assign(url, {
        ...updateUrlDto,
        updatedAt: Date.now()
      });

      // Lưu URL vào database
      return this.urlRepository.save(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật URL: ${error.message}`
      );
    }
  }

  /**
   * Xóa nhiều URL
   * @param ids Mảng ID của các URL cần xóa
   * @param userId ID của người dùng
   */
  async deleteUrls(ids: string[], userId: number): Promise<void> {
    try {
      this.logger.log(`Deleting URLs with IDs: ${JSON.stringify(ids)} for user: ${userId}`);

      if (!ids || ids.length === 0) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Danh sách ID không được để trống',
        );
      }

      // Kiểm tra tất cả URL tồn tại và thuộc về user
      const urls = await this.urlRepository.find({
        where: {
          id: In(ids),
          ownedBy: userId
        }
      });

      if (urls.length !== ids.length) {
        const foundIds = urls.map(url => url.id);
        const notFoundIds = ids.filter(id => !foundIds.includes(id));
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          `Không tìm thấy URL với ID: ${notFoundIds.join(', ')} hoặc bạn không có quyền truy cập`,
        );
      }

      // Xóa tất cả URL
      await this.urlRepository.remove(urls);

      this.logger.log(`Successfully deleted ${urls.length} URLs for user: ${userId}`);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_DELETE_FAILED,
        `Không thể xóa URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa URL (deprecated - sử dụng deleteUrls thay thế)
   * @param id ID của URL
   * @param userId ID của người dùng
   */
  async deleteUrl(id: string, userId: number): Promise<void> {
    return this.deleteUrls([id], userId);
  }
}