import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Url } from '../../entities/url.entity';
import { FindAllUrlDto } from '../dto/find-all-url.dto';

// Mock SortDirection enum
enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

// Mock UrlRepository class
class UrlRepository {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
}

// Mock UrlUserService
class UrlUserService {
  constructor(
    private readonly urlRepository: any,
    private readonly urlCustomRepository: UrlRepository
  ) {}

  async findUrlsByOwner(
    userId: number,
    page: number = 1,
    limit: number = 10,
    sortField: string = 'createdAt',
    sortDirection: SortDirection = SortDirection.DESC,
    keyword?: string,
    type?: string,
    tags?: string[]
  ) {
    return this.urlCustomRepository.findUrlsByOwner(userId, {
      page,
      limit,
      sortBy: sortField,
      sortDirection,
      keyword,
      type,
      tags
    });
  }

  async searchUrls(userId: number, keyword: string, limit: number = 10) {
    const urls = await this.urlCustomRepository.searchUrls(keyword, limit);
    return urls.filter(url => url.ownedBy === userId);
  }
}

describe('Kiểm thử tìm kiếm URL của người dùng', () => {
  let service: UrlUserService;
  let urlCustomRepository: UrlRepository;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
      content: 'Test content 2',
      tags: ['test', 'nestjs'],
    },
    {
      ...mockUrl,
      id: 'test-id-3',
      url: 'https://example.com/3',
      title: 'NestJS Tutorial',
      content: 'Learn NestJS',
      tags: ['nestjs', 'tutorial'],
      ownedBy: 2, // Thuộc về người dùng khác
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls.filter(url => url.ownedBy === 1),
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    // Tạo repository và service
    const repository = {};
    urlCustomRepository = new UrlRepository();
    service = new UrlUserService(repository, urlCustomRepository);

    // Setup mock methods
    urlCustomRepository.findUrlsByOwner.mockResolvedValue(mockPaginatedResult);
    urlCustomRepository.searchUrls.mockResolvedValue(mockUrls);
  });

  describe('findUrlsByOwner - Tìm kiếm URL theo nhiều tiêu chí', () => {
    it('Phải trả về danh sách URL của người dùng với phân trang', async () => {
      const result = await service.findUrlsByOwner(1);
      expect(result).toEqual(mockPaginatedResult);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.any(Object));
    });

    it('Phải tìm kiếm được URL theo từ khóa', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        keyword: 'test',
      };

      await service.findUrlsByOwner(
        1,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        queryParams.keyword
      );

      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.objectContaining({
        keyword: 'test'
      }));
    });

    it('Phải tìm kiếm được URL theo loại', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        type: 'web',
      };

      await service.findUrlsByOwner(
        1,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        undefined,
        queryParams.type
      );

      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.objectContaining({
        type: 'web'
      }));
    });

    it('Phải tìm kiếm được URL theo thẻ', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        tags: ['nestjs', 'tutorial'],
      };

      await service.findUrlsByOwner(
        1,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        undefined,
        undefined,
        queryParams.tags
      );

      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.objectContaining({
        tags: ['nestjs', 'tutorial']
      }));
    });

    it('Phải tìm kiếm được URL với nhiều tiêu chí kết hợp', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        keyword: 'nestjs',
        type: 'web',
        tags: ['tutorial'],
      };

      await service.findUrlsByOwner(
        1,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        queryParams.keyword,
        queryParams.type,
        queryParams.tags
      );

      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.objectContaining({
        keyword: 'nestjs',
        type: 'web',
        tags: ['tutorial']
      }));
    });
  });

  describe('searchUrls - Tìm kiếm nhanh URL theo từ khóa', () => {
    it('Phải trả về danh sách URL phù hợp với từ khóa', async () => {
      const result = await service.searchUrls(1, 'test', 10);

      // Chỉ trả về URL của người dùng hiện tại
      expect(result).toEqual(mockUrls.filter(url => url.ownedBy === 1));
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 10);
    });

    it('Phải lọc kết quả theo người dùng hiện tại', async () => {
      // Giả lập kết quả tìm kiếm có URL của người dùng khác
      urlCustomRepository.searchUrls.mockResolvedValueOnce(mockUrls);

      const result = await service.searchUrls(1, 'nestjs', 10);

      // Chỉ trả về URL của người dùng hiện tại
      expect(result.every(url => url.ownedBy === 1)).toBe(true);
      expect(result).not.toContainEqual(expect.objectContaining({ ownedBy: 2 }));
    });

    it('Phải giới hạn số lượng kết quả trả về', async () => {
      await service.searchUrls(1, 'test', 5);
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 5);
    });
  });
});
