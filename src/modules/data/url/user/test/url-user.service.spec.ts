// <PERSON><PERSON> báo các enum và type cần thiết
const SortDirectionEnum = {
  ASC: 'ASC' as const,
  DESC: 'DESC' as const
};

// <PERSON><PERSON> báo các interface cần thiết
interface CreateUrlDto {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
}

interface UpdateUrlDto {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
}

// Mock UrlUserException class
class UrlUserException extends Error {
  constructor(errorCode: any, message?: string) {
    super(message || errorCode?.message || 'URL user exception');
    this.name = 'UrlUserException';
  }
}

// Mock UrlRepository class
class UrlRepository {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
}

// Mock UrlUserService class
class UrlUserServiceMock {
  constructor(
    private readonly urlRepository: any,
    private readonly urlCustomRepository: UrlRepository
  ) {}

  async findUrlById(userId: number, id: string) {
    const url = await this.urlCustomRepository.findUrlById(id);
    if (!url) {
      throw new UrlUserException(null, 'URL không tồn tại');
    }
    if (url.ownedBy !== userId) {
      throw new UrlUserException(null, 'Bạn không có quyền truy cập URL này');
    }
    return url;
  }

  async findUrlsByOwner(
    userId: number,
    page: number = 1,
    limit: number = 10,
    sortField: string = 'createdAt',
    sortDirection: string = SortDirectionEnum.DESC,
    keyword?: string,
    type?: string,
    tags?: string[]
  ) {
    if (!userId) {
      throw new UrlUserException(null, 'Không tìm thấy thông tin người dùng');
    }

    return this.urlCustomRepository.findUrlsByOwner(userId, {
      page,
      limit,
      sortBy: sortField,
      sortDirection,
      keyword,
      type,
      tags
    });
  }

  async createUrl(userId: number, createUrlDto: CreateUrlDto) {
    try {
      new URL(createUrlDto.url);
    } catch (error) {
      throw new UrlUserException(null, 'URL không đúng định dạng');
    }

    if (!createUrlDto.title || createUrlDto.title.trim() === '') {
      throw new UrlUserException(null, 'Tiêu đề URL không được để trống');
    }

    if (!createUrlDto.content || createUrlDto.content.trim() === '') {
      throw new UrlUserException(null, 'Nội dung URL không được để trống');
    }

    const existingUrl = await this.urlRepository.findOne?.({
      where: {
        url: createUrlDto.url
      }
    });

    if (existingUrl) {
      throw new UrlUserException(null, 'URL này đã tồn tại trong hệ thống');
    }

    const newUrl = this.urlRepository.create?.({
      ...createUrlDto,
      ownedBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    return this.urlRepository.save?.(newUrl);
  }

  async updateUrl(id: string, userId: number, updateUrlDto: UpdateUrlDto) {
    const url = await this.findUrlById(userId, id);

    if (updateUrlDto.url) {
      try {
        new URL(updateUrlDto.url);
      } catch (error) {
        throw new UrlUserException(null, 'URL không đúng định dạng');
      }

      // Kiểm tra URL mới đã tồn tại chưa trong toàn bộ hệ thống (nếu khác URL cũ)
      if (updateUrlDto.url !== url.url) {
        const existingUrl = await this.urlRepository.findOne?.({
          where: {
            url: updateUrlDto.url
          }
        });

        if (existingUrl) {
          throw new UrlUserException(null, 'URL này đã tồn tại trong hệ thống');
        }
      }
    }

    if (updateUrlDto.title !== undefined && (updateUrlDto.title === null || updateUrlDto.title.trim() === '')) {
      throw new UrlUserException(null, 'Tiêu đề URL không được để trống');
    }

    if (updateUrlDto.content !== undefined && (updateUrlDto.content === null || updateUrlDto.content.trim() === '')) {
      throw new UrlUserException(null, 'Nội dung URL không được để trống');
    }

    Object.assign(url, {
      ...updateUrlDto,
      updatedAt: Date.now()
    });

    return this.urlRepository.save?.(url);
  }

  async deleteUrl(id: string, userId: number) {
    const url = await this.findUrlById(userId, id);
    return this.urlRepository.remove?.(url);
  }
}

// Không cần mock SqlHelper, chúng ta sẽ mock repository và service

describe('Kiểm thử Service URL của người dùng', () => {
  let service: UrlUserServiceMock;
  let repository: any;
  let urlCustomRepository: UrlRepository;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    // Tạo mock repository
    repository = {
      create: jest.fn().mockImplementation((dto) => dto),
      save: jest.fn().mockImplementation((url) => Promise.resolve({ id: 'test-id', ...url })),
      findOne: jest.fn().mockImplementation((options) => {
        if (options?.where?.id === 'test-id' && options?.where?.ownedBy === 1) {
          return Promise.resolve(mockUrl);
        }
        // Kiểm tra URL đã tồn tại trong toàn bộ hệ thống
        if (options?.where?.url === 'https://example.com') {
          return Promise.resolve(mockUrl);
        }
        return Promise.resolve(null);
      }),
      remove: jest.fn().mockResolvedValue(mockUrl),
    };

    // Tạo mock custom repository
    urlCustomRepository = new UrlRepository();
    urlCustomRepository.findUrlById.mockImplementation((id) => {
      if (id === 'test-id') {
        return Promise.resolve(mockUrl);
      }
      if (id === 'another-user-id') {
        return Promise.resolve({ ...mockUrl, ownedBy: 2 });
      }
      return Promise.resolve(null);
    });
    urlCustomRepository.findUrlsByOwner.mockResolvedValue(mockPaginatedResult);
    urlCustomRepository.searchUrls.mockResolvedValue(mockUrls);

    // Tạo service với các repository đã mock
    service = new UrlUserServiceMock(repository, urlCustomRepository);
  });

  it('Service phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('findUrlById - Lấy URL theo ID', () => {
    it('Phải trả về URL nếu nó tồn tại và thuộc về người dùng', async () => {
      const result = await service.findUrlById(1, 'test-id');
      expect(result).toEqual(mockUrl);
      expect(urlCustomRepository.findUrlById).toHaveBeenCalledWith('test-id');
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.findUrlById(1, 'non-existent-id')).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu URL thuộc về người dùng khác', async () => {
      await expect(service.findUrlById(1, 'another-user-id')).rejects.toThrow(UrlUserException);
    });
  });

  describe('findUrlsByOwner - Lấy danh sách URL của người dùng', () => {
    it('Phải trả về danh sách URL có phân trang của người dùng', async () => {
      const result = await service.findUrlsByOwner(1, 1, 10, 'createdAt', SortDirectionEnum.DESC);
      expect(result).toEqual(mockPaginatedResult);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.any(Object));
    });

    it('Phải sử dụng giá trị mặc định nếu không cung cấp tham số', async () => {
      const result = await service.findUrlsByOwner(1);
      expect(result).toEqual(mockPaginatedResult);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(1, expect.any(Object));
    });

    it('Phải ném ngoại lệ nếu không cung cấp userId', async () => {
      await expect(service.findUrlsByOwner(null as unknown as number)).rejects.toThrow(UrlUserException);
    });
  });

  describe('createUrl - Tạo URL mới', () => {
    it('Phải tạo được URL mới', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: 'New content',
        type: 'web',
        tags: ['test'],
      };

      // Mock findOne to return null (URL doesn't exist yet)
      repository.findOne.mockResolvedValueOnce(null);

      const result = await service.createUrl(1, createUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.create).toHaveBeenCalledWith({
        ...createUrlDto,
        ownedBy: 1,
        createdAt: expect.any(Number),
        updatedAt: expect.any(Number),
      });
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu định dạng URL không hợp lệ', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'invalid-url',
        title: 'Invalid URL',
        content: 'Invalid content',
        type: 'web',
        tags: ['test'],
      };

      await expect(service.createUrl(1, createUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu URL đã tồn tại trong hệ thống', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'https://example.com',
        title: 'Existing URL',
        content: 'Existing content',
        type: 'web',
        tags: ['test'],
      };

      await expect(service.createUrl(1, createUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề trống', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'https://newexample.com',
        title: '',
        content: 'New content',
        type: 'web',
        tags: ['test'],
      };

      await expect(service.createUrl(1, createUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu nội dung trống', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: '',
        type: 'web',
        tags: ['test'],
      };

      await expect(service.createUrl(1, createUrlDto)).rejects.toThrow(UrlUserException);
    });
  });

  describe('updateUrl - Cập nhật URL', () => {
    it('Phải cập nhật được URL đã tồn tại', async () => {
      const updateUrlDto: UpdateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      const result = await service.updateUrl('test-id', 1, updateUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      const updateUrlDto: UpdateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('non-existent-id', 1, updateUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu URL thuộc về người dùng khác', async () => {
      const updateUrlDto: UpdateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('another-user-id', 1, updateUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu định dạng URL cập nhật không hợp lệ', async () => {
      const updateUrlDto: UpdateUrlDto = {
        url: 'invalid-url',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', 1, updateUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu URL cập nhật đã tồn tại trong hệ thống', async () => {
      // Mock URL mới đã tồn tại trong hệ thống
      repository.findOne.mockImplementationOnce(() => Promise.resolve({ id: 'another-id', url: 'https://newurl.com' }));

      const updateUrlDto: UpdateUrlDto = {
        url: 'https://newurl.com',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', 1, updateUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlDto = {
        title: '',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', 1, updateUrlDto)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu nội dung cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlDto = {
        title: 'Updated URL',
        content: '',
      };

      await expect(service.updateUrl('test-id', 1, updateUrlDto)).rejects.toThrow(UrlUserException);
    });
  });

  describe('deleteUrl - Xóa URL', () => {
    it('Phải xóa được URL đã tồn tại', async () => {
      await service.deleteUrl('test-id', 1);
      expect(repository.remove).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.deleteUrl('non-existent-id', 1)).rejects.toThrow(UrlUserException);
    });

    it('Phải ném ngoại lệ nếu URL thuộc về người dùng khác', async () => {
      await expect(service.deleteUrl('another-user-id', 1)).rejects.toThrow(UrlUserException);
    });
  });
});
