import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, ArrayMinSize } from 'class-validator';

export class DeleteUrlsAdminDto {
  @ApiProperty({
    description: 'Danh sách ID của các URL cần xóa',
    example: ['550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001'],
    type: [String],
  })
  @IsArray({ message: 'IDs phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất một ID để xóa' })
  @IsString({ each: true, message: 'Mỗi ID phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'ID không được để trống' })
  ids: string[];
}
