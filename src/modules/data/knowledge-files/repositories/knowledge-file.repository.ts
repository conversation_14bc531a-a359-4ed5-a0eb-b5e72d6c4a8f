import { Injectable, Logger } from '@nestjs/common';
import { Brackets, DataSource, In, Like, Repository, SelectQueryBuilder } from 'typeorm';
import { KnowledgeFile } from '../entities/knowledge-file.entity';
import { OwnerType } from '@shared/enums';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryFileDto } from '../user/dto';
import { KnowledgeFileStatus } from '../enums/knowledge-file-status.enum';

@Injectable()
export class KnowledgeFileRepository extends Repository<KnowledgeFile> {
  private readonly logger = new Logger(KnowledgeFileRepository.name);

  constructor(private dataSource: DataSource) {
    super(KnowledgeFile, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho knowledge file
   * @returns SelectQueryBuilder<KnowledgeFile>
   */
  private createBaseQuery(): SelectQueryBuilder<KnowledgeFile> {
    return this.createQueryBuilder('kf');
  }

  /**
   * Tìm file theo ID và người dùng
   * @param id ID của file
   * @param userId ID của người dùng
   * Tìm các file theo danh sách ID và người dùng
   * @param fileIds Danh sách ID của các file
   * @param userId ID của người dùng
   * @returns Danh sách các file
   */
  async findByIdsAndUserId(fileIds: string[], userId: number): Promise<KnowledgeFile[]> {
    try {
      return this.find({
        where: {
          id: In(fileIds),
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm các file theo ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm các file theo danh sách ID và admin
   * @param fileIds Danh sách ID của các file
   * @param employeeId ID của admin
   * @returns Danh sách các file
   */
  async findByIdsAndEmployeeId(fileIds: string[], employeeId: number): Promise<KnowledgeFile[]> {
    try {
      return this.find({
        where: {
          id: In(fileIds),
          ownedBy: employeeId,
          ownerType: OwnerType.ADMIN,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm các file theo ID và employeeId: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm file theo ID và admin
   * @param fileId ID của file
   * @param employeeId ID của admin
   * @returns File nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndEmployeeId(fileId: string, employeeId: number): Promise<KnowledgeFile | null> {
    try {
      return this.findOne({
        where: {
          id: fileId,
          ownedBy: employeeId,
          ownerType: OwnerType.ADMIN,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm file theo ID và employeeId: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm file theo ID và người dùng
   * @param fileId ID của file
   * @param userId ID của người dùng
   * @returns File nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndUserId(fileId: string, userId: number): Promise<KnowledgeFile | null> {
    try {
      return this.findOne({
        where: {
          id: fileId,
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm file theo ID và userId: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách file với phân trang và lọc nâng cao
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @param excludeStatus Trạng thái file cần loại trừ
   * @param fileIds Danh sách ID của các file (nếu có)
   * @returns Danh sách file với phân trang
   */
  async findAllWithPaginationAndFilters(
    queryDto: QueryFileDto,
    userId: number,
    excludeStatus?: KnowledgeFileStatus,
    fileIds?: string[],
  ): Promise<PaginatedResult<KnowledgeFile>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        extensions,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      // Tạo query builder
      const queryBuilder = this.createBaseQuery()
        .where('kf.owned_by = :userId', { userId })
        .andWhere('kf.owner_type = :ownerType', { ownerType: OwnerType.USER });

      // Thêm điều kiện loại trừ trạng thái nếu có
      if (excludeStatus) {
        queryBuilder.andWhere('kf.status != :excludeStatus', { excludeStatus });
      }

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('kf.name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Thêm điều kiện lọc theo định dạng file nếu có
      if (extensions) {
        const extensionList = extensions
          .split(',')
          .map((ext) => ext.trim().toLowerCase());

        if (extensionList.length > 0) {
          queryBuilder.andWhere(new Brackets((qb) => {
            extensionList.forEach((ext, index) => {
              if (index === 0) {
                qb.where('LOWER(kf.name) LIKE :ext' + index, { ['ext' + index]: `%.${ext}` });
              } else {
                qb.orWhere('LOWER(kf.name) LIKE :ext' + index, { ['ext' + index]: `%.${ext}` });
              }
            });
          }));
        }
      }

      // Thêm điều kiện lọc theo danh sách file ID nếu có
      if (fileIds && fileIds.length > 0) {
        queryBuilder.andWhere('kf.id IN (:...fileIds)', { fileIds });
      }

      // Áp dụng sắp xếp
      const sortColumn = this.getSortColumn(sortBy);
      const direction = sortDirection === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`kf.${sortColumn}`, direction);

      // Thực hiện đếm tổng số bản ghi
      const totalItemsQuery = queryBuilder.clone();
      const totalItems = await totalItemsQuery.getCount();

      // Áp dụng phân trang
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Thực hiện truy vấn
      const items = await queryBuilder.getMany();

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách file với bộ lọc: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi tên trường sắp xếp thành tên cột trong database
   * @param sortBy Tên trường sắp xếp
   * @returns Tên cột trong database
   */
  private getSortColumn(sortBy?: string): string {
    if (!sortBy) return 'created_at';

    switch (sortBy.toLowerCase()) {
      case 'name':
        return 'name';
      case 'storage':
        return 'storage';
      case 'createdat':
      case 'created_at':
      case 'createat':
      default:
        return 'created_at';
    }
  }

  /**
   * Tạo và lưu file tri thức mới
   * @param fileData Dữ liệu file cần tạo
   * @returns File đã được lưu
   */
  async createAndSaveKnowledgeFile(fileData: Partial<KnowledgeFile>): Promise<KnowledgeFile> {
    try {
      // Tạo bản ghi file
      const knowledgeFile = this.create(fileData);

      // Lưu vào database
      return await this.save(knowledgeFile);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo và lưu file tri thức: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo và lưu nhiều file tri thức
   * @param filesData Danh sách dữ liệu file cần tạo
   * @returns Danh sách file đã được lưu
   */
  async createAndSaveMultipleKnowledgeFiles(filesData: Partial<KnowledgeFile>[]): Promise<KnowledgeFile[]> {
    try {
      // Tạo các bản ghi file
      const knowledgeFiles = filesData.map(fileData => this.create(fileData));

      // Lưu vào database
      return await this.save(knowledgeFiles);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo và lưu nhiều file tri thức: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa mềm file tri thức theo ID (cập nhật trạng thái thành DELETED)
   * @param fileId ID của file cần xóa
   * @returns File đã được cập nhật
   */
  async softDeleteKnowledgeFile(fileId: string): Promise<KnowledgeFile | null> {
    try {
      // Tìm file cần xóa
      const file = await this.findOne({ where: { id: fileId } });

      if (!file) {
        return null;
      }

      // Cập nhật trạng thái thành DELETED
      file.status = KnowledgeFileStatus.DELETED;

      // Lưu thay đổi vào database
      return await this.save(file);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm file tri thức: ${error.message}`, error.stack);
      throw error;
    }
  }
}
