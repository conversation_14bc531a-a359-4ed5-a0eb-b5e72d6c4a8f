import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { AssignFilesDto } from '../../dto/assign-files.dto';

describe('AssignFilesDto', () => {
  it('nên xác thực DTO hợp lệ với danh sách fileIds', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một fileId', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi xác thực với fileIds không phải là mảng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi xác thực với mảng fileIds rỗng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: [],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('nên thất bại khi xác thực khi thiếu fileIds', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
  });

  it('nên thất bại khi xác thực với fileIds chứa phần tử không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 123],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi xác thực với fileIds chứa chuỗi rỗng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', ''],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });
});
