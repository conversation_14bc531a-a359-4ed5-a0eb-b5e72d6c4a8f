import { HttpStatus } from '@nestjs/common';

/**
 * Interface chuẩn cho API Response
 * Sử dụng generic type T để định nghĩa kiểu dữ liệu của result
 */
export class ApiResponse<T> {
  /**
   * Mã trạng thái
   * - 0: Thành công
   * - Khác 0: Mã lỗi cụ thể
   */
  public code: number;

  /**
   * Thông điệp mô tả kết quả
   */
  public message?: string;

  /**
   * Dữ liệu trả về
   */
  public result?: T;

  /**
   * Constructor cho API Response
   * @param result Dữ liệu trả về
   * @param message Thông điệp mô tả kết quả
   * @param code Mã trạng thái nội bộ
   */
  constructor(
      result: T,
      message: string = 'Success',
      code: number = HttpStatus.OK,
  ) {
    this.code = code;
    this.message = message;
    this.result = result;
  }

  // === Các phương thức tĩnh (Static factory methods) để tạo response dễ dàng hơn ===

  /**
   * Tạo response thành công chuẩn (200 OK)
   * @param data Dữ liệu trả về
   * @param message Thông điệp (mặc định 'Success')
   * @returns ApiResponse với code = 0, status = 200
   */
  static success<T>(data: T, message: string = 'Success'): ApiResponse<T> {
    return new ApiResponse<T>(data, message, HttpStatus.OK);
  }

  /**
   * Tạo response thành công với mã Created (201)
   * @param data Dữ liệu đã tạo
   * @param message Thông điệp (mặc định 'Created Successfully')
   * @returns ApiResponse với code = 0, status = 201
   */
  static created<T>(data: T, message: string = 'Created Successfully'): ApiResponse<T> {
    return new ApiResponse<T>(data, message, HttpStatus.CREATED);
  }

  /**
   * Tạo response cập nhật thành công (200 OK)
   * @param data Dữ liệu đã được cập nhật
   * @param message Thông điệp (mặc định 'Updated Successfully')
   * @returns ApiResponse với code = 0, status = 200
   */
  static updated<T>(data: T, message: string = 'Updated Successfully'): ApiResponse<T> {
    return new ApiResponse<T>(data, message, HttpStatus.OK);
  }

  /**
   * Tạo response xóa thành công (200 OK hoặc 204 No Content)
   * @param data Dữ liệu đã xóa (nếu có)
   * @param message Thông điệp (mặc định 'Deleted Successfully')
   * @param useNoContent Nếu true, sử dụng status 204 thay vì 200
   * @returns ApiResponse với code = 0, status = 200 hoặc 204
   */
  static deleted<T = null>(
      data: T = null as T,
      message: string = 'Deleted Successfully',
      useNoContent: boolean = false
  ): ApiResponse<T> {
    const status = useNoContent ? HttpStatus.NO_CONTENT : HttpStatus.OK;
    return new ApiResponse<T>(data, message, status);
  }

  /**
   * Tạo response thành công với dữ liệu phân trang (200 OK)
   * @param result
   * @param message Thông điệp (mặc định 'Success')
   * @returns ApiResponse với code = 0, status = 200 và result là đối tượng phân trang
   */
  static paginated<T>(
      result: PaginatedResult<T>,
      message: string = 'Success'
  ): ApiResponse<PaginatedResult<T>> {
    return new ApiResponse<PaginatedResult<T>>(
        result,
        message,
        HttpStatus.OK
    );
  }

  /**
   * Tạo response không có nội dung (204 No Content)
   * @param message Thông điệp (mặc định 'No Content')
   * @returns ApiResponse với code = 0, status = 204
   */
  static noContent(message: string = 'No Content'): ApiResponse<null> {
    return new ApiResponse<null>(null, message, HttpStatus.NO_CONTENT);
  }
}


/**
 * Interface định nghĩa cấu trúc metadata của kết quả phân trang
 */
export interface PaginationMeta {
  /**
   * Tổng số bản ghi
   */
  totalItems: number;

  /**
   * Số lượng bản ghi trên trang hiện tại
   */
  itemCount: number;

  /**
   * Số lượng bản ghi trên mỗi trang
   */
  itemsPerPage: number;

  /**
   * Tổng số trang
   */
  totalPages: number;

  /**
   * Trang hiện tại
   */
  currentPage: number;
}

/**
 * Interface định nghĩa kết quả trả về cho dữ liệu phân trang
 */
export interface PaginatedResult<T> {
  /** Danh sách các item */
  items: T[];
  /** Thông tin phân trang */
  meta: PaginationMeta;
}
