import { Injectable } from '@nestjs/common';

@Injectable()
export class RedisService {
  async get(key: string): Promise<string | null> {
    return null;
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    return;
  }

  async del(key: string): Promise<void> {
    return;
  }

  async exists(key: string): Promise<boolean> {
    return false;
  }

  async ttl(key: string): Promise<number> {
    return -1;
  }

  async expire(key: string, ttl: number): Promise<void> {
    return;
  }

  async keys(pattern: string): Promise<string[]> {
    return [];
  }

  async flushall(): Promise<void> {
    return;
  }
}
