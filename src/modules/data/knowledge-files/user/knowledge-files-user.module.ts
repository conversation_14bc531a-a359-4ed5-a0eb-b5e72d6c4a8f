import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KnowledgeFileUserController, VectorStoreUserController } from './controllers';
import { KnowledgeFileUserService, VectorStoreUserService } from './services';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../entities';
import { KnowledgeFileRepository, VectorStoreRepository, VectorStoreFileRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { KnowledgeFileUserValidationHelper } from './helpers';

@Module({
  imports: [TypeOrmModule.forFeature([KnowledgeFile, VectorStore, VectorStoreFile])],
  controllers: [KnowledgeFileUserController, VectorStoreUserController],
  providers: [
    KnowledgeFileUserService,
    VectorStoreUserService,
    KnowledgeFileRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    KnowledgeFileUserValidationHelper,
    S3Service,
    OpenAiService
  ],
  exports: [KnowledgeFileUserService, VectorStoreUserService],
})
export class KnowledgeFilesUserModule {}
