import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions';
import { VectorStore, KnowledgeFile } from '../entities';
import { OwnerType } from '@shared/enums';
import { BatchCreateFilesDto, QueryFileDto } from '../admin/dto';

@Injectable()
export class ValidationHelper {
  /**
   * Kiểm tra vector store có tồn tại và thuộc về người dùng
   * @param vectorStore Vector store cần kiểm tra
   * @param ownerId ID của người sở hữu
   * @param ownerType Loại người sở hữu
   * @throws AppException nếu vector store không tồn tại hoặc không thuộc về người dùng
   */
  validateVectorStoreExists(vectorStore: VectorStore | null, ownerId: number, ownerType: OwnerType): void {
    if (!vectorStore) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        'Vector store không tồn tại hoặc bạn không có quyền truy cập'
      );
    }

    // Kiểm tra quyền sở hữu
    if (vectorStore.ownerId !== ownerId || vectorStore.ownerType !== ownerType) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ACCESS_DENIED,
        'Bạn không có quyền truy cập vector store này'
      );
    }
  }

  /**
   * Kiểm tra file có tồn tại và thuộc về người dùng
   * @param files Danh sách file cần kiểm tra
   * @param fileIds Danh sách ID file cần kiểm tra
   * @param ownerId ID của người sở hữu
   * @param ownerType Loại người sở hữu
   * @throws AppException nếu không có file nào tồn tại hoặc không thuộc về người dùng
   */
  validateFilesExist(files: KnowledgeFile[], fileIds: string[], ownerId: number, ownerType: OwnerType): void {
    if (files.length === 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.FILE_NOT_FOUND,
        'Không có file nào tồn tại hoặc thuộc quyền sở hữu của bạn'
      );
    }

    // Xác định các file không tồn tại hoặc không thuộc quyền sở hữu
    const foundFileIds = files.map(file => file.id);
    const unauthorizedFileIds = fileIds.filter(id => !foundFileIds.includes(id));

    if (unauthorizedFileIds.length > 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.FILE_NOT_FOUND,
        `Không tìm thấy file hoặc không có quyền truy cập: ${unauthorizedFileIds[0]}`
      );
    }
  }

  /**
   * Kiểm tra giá trị OpenAI vector store ID
   * @param openAiVectorStore Kết quả từ OpenAI
   * @throws AppException nếu không nhận được ID vector store từ OpenAI
   */
  validateOpenAiVectorStoreId(openAiVectorStore: { vectorStoreId?: string } | null): void {
    if (!openAiVectorStore || !openAiVectorStore.vectorStoreId) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        'Không nhận được ID vector store từ OpenAI'
      );
    }
  }

  /**
   * Kiểm tra vector store ID hợp lệ
   * @param vectorStoreId ID của vector store
   * @throws AppException nếu ID không hợp lệ
   */
  validateVectorStoreId(vectorStoreId: string | null | undefined): void {
    if (!vectorStoreId) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        'ID vector store không được để trống'
      );
    }
  }

  /**
   * Kiểm tra danh sách file ID hợp lệ
   * @param fileIds Danh sách ID file
   * @throws AppException nếu danh sách rỗng hoặc không hợp lệ
   */
  validateFileIds(fileIds: string[] | null | undefined): void {
    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.FILE_NOT_FOUND,
        'Danh sách file không được để trống'
      );
    }
  }

  /**
   * Kiểm tra file ID hợp lệ
   * @param fileId ID của file
   * @throws AppException nếu ID không hợp lệ
   */
  validateFileId(fileId: string | null | undefined): void {
    if (!fileId) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.FILE_NOT_FOUND,
        'ID file không được để trống'
      );
    }
  }

  /**
   * Kiểm tra dữ liệu đầu vào cho BatchCreateFilesDto
   * @param dto Dữ liệu đầu vào
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  validateBatchCreateFilesDto(dto: BatchCreateFilesDto | null | undefined): void {
    if (!dto) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
        'Dữ liệu đầu vào không được để trống'
      );
    }

    if (!dto.files || !Array.isArray(dto.files) || dto.files.length === 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
        'Danh sách file không được để trống'
      );
    }

    // Kiểm tra từng file trong danh sách
    dto.files.forEach((file, index) => {
      if (!file.name) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
          `Tên file tại vị trí ${index + 1} không được để trống`
        );
      }

      if (!file.mime) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
          `Loại file (MIME) tại vị trí ${index + 1} không được để trống`
        );
      }

      if (!file.storage || file.storage <= 0) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
          `Dung lượng file tại vị trí ${index + 1} phải lớn hơn 0`
        );
      }
    });
  }

  /**
   * Kiểm tra dữ liệu đầu vào cho QueryFileDto
   * @param queryDto Dữ liệu đầu vào
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  validateQueryFileDto(queryDto: QueryFileDto | null | undefined): void {
    if (!queryDto) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
        'Tham số truy vấn không được để trống'
      );
    }

    // Kiểm tra page và limit
    if (queryDto.page && (isNaN(queryDto.page) || queryDto.page < 1)) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
        'Số trang phải là số nguyên dương'
      );
    }

    if (queryDto.limit && (isNaN(queryDto.limit) || queryDto.limit < 1)) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
        'Số lượng item trên một trang phải là số nguyên dương'
      );
    }

    // Kiểm tra vectorStoreId nếu có
    if (queryDto.vectorStoreId) {
      this.validateVectorStoreId(queryDto.vectorStoreId);
    }
  }

  /**
   * Kiểm tra file có tồn tại không
   * @param file File cần kiểm tra
   * @throws AppException nếu file không tồn tại
   */
  validateFileExists(file: KnowledgeFile | null): void {
    if (!file) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
        'File không tồn tại hoặc bạn không có quyền truy cập'
      );
    }
  }

  /**
   * Kiểm tra file có thuộc về người dùng không
   * @param file File cần kiểm tra
   * @param ownerId ID của người sở hữu
   * @param ownerType Loại người sở hữu
   * @throws AppException nếu file không thuộc về người dùng
   */
  validateFileOwnership(file: KnowledgeFile, ownerId: number, ownerType: OwnerType): void {
    if (file.ownedBy !== ownerId || file.ownerType !== ownerType) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
        'Bạn không có quyền truy cập file này'
      );
    }
  }
}
