import { Column, Entity, PrimaryColumn } from 'typeorm';
import { OwnerType } from '@shared/enums';

/**
 * Entity đại diện cho bảng vector_stores trong cơ sở dữ liệu
 * <PERSON><PERSON><PERSON> thông tin các kho vector phục vụ AI/RAG
 */
@Entity('vector_stores')
export class VectorStore {
  /**
   * ID duy nhất (UUID hoặc mã định danh)
   */
  @PrimaryColumn({ name: 'id', length: 100 })
  id: string;

  /**
   * Tên vector store
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * Dung lượng đã dùng (bytes/tokens)
   */
  @Column({ name: 'storage', type: 'bigint', default: 0 })
  storage: number;

  /**
   * Loại chủ sở hữu: user hoặc employee
   */
  @Column({
    name: 'owner_type',
    type: 'enum',
    enum: OwnerType,
    default: OwnerType.USER
  })
  ownerType: OwnerType;

  /**
   * ID của user hoặc employee
   */
  @Column({ name: 'owner_id' })
  ownerId: number;

  /**
   * Thời điểm tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật
   */
  @Column({
    name: 'update_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updateAt: number;
}
