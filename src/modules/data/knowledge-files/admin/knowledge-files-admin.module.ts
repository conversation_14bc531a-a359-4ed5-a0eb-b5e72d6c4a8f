import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  KnowledgeFileAdminController,
  VectorStoreAdminController,
} from './controllers';
import { KnowledgeFileAdminService, VectorStoreAdminService } from './services';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../entities';
import {
  KnowledgeFileRepository,
  VectorStoreRepository,
  VectorStoreFileRepository,
} from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { ValidationHelper } from '../helpers/validation.helper';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeFile, VectorStore, VectorStoreFile]),
  ],
  controllers: [KnowledgeFileAdminController, VectorStoreAdminController],
  providers: [
    KnowledgeFileAdminService,
    VectorStoreAdminService,
    KnowledgeFileRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    S3Service,
    OpenAiService,
    ValidationHelper,
  ],
  exports: [KnowledgeFileAdminService, VectorStoreAdminService],
})
export class KnowledgeFilesAdminModule {}
