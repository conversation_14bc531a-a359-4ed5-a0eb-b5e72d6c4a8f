# Module Data

## Tổng quan

Module Data cung cấp các chức năng quản lý dữ liệu trong hệ thống. Module này bao gồm các submodule như Media (quản lý tệp đa phương tiện), URL (quản lý URL), Knowledge Files (quản lý tệp kiến thức) và Functions (quản lý các hàm).

## Cấu trúc module

```
data/
├── media/                  # Submodule quản lý tệp đa phương tiện
│   ├── admin/              # Chức năng quản lý dành cho admin
│   └── user/               # Chức năng dành cho người dùng
├── url/                    # Submodule quản lý URL
│   ├── admin/              # Chức năng quản lý dành cho admin
│   └── user/               # Chức năng dành cho người dùng
├── knowledge-files/        # Submodule quản lý tệp kiến thức
│   ├── admin/              # Chức năng quản lý dành cho admin
│   └── user/               # Chức năng dành cho người dùng
├── tools/              # Submodule quản lý các hàm
│   ├── admin/              # Chức năng quản lý dành cho admin
│   ├── user/               # Chức năng dành cho người dùng
│   ├── entities/           # Entities mapping với database
│   ├── repositories/       # Repositories tương tác với database
│   └── schemas/            # Schemas định nghĩa cấu trúc response API
└── data.module.ts          # Module definition
```

## Submodules

### Media Module
Quản lý tệp đa phương tiện như hình ảnh, video, âm thanh, v.v.

### URL Module
Quản lý URL, bao gồm rút gọn URL, theo dõi click, v.v.

### Knowledge Files Module
Quản lý tệp kiến thức, bao gồm tài liệu, PDF, v.v. được sử dụng để huấn luyện AI.

### Functions Module
Quản lý các hàm và chức năng có thể được sử dụng bởi các agent AI.

## Chức năng chính

### Quản lý tệp đa phương tiện
- Tải lên, cập nhật, xóa tệp
- Phân loại tệp theo loại
- Tối ưu hóa tệp

### Quản lý URL
- Tạo, cập nhật, xóa URL
- Rút gọn URL
- Theo dõi lượt click

### Quản lý tệp kiến thức
- Tải lên, cập nhật, xóa tệp kiến thức
- Phân tích nội dung tệp
- Trích xuất thông tin từ tệp

### Quản lý hàm
- Tạo, cập nhật, xóa hàm
- Định nghĩa tham số và kết quả trả về
- Gọi hàm từ agent AI

## API Endpoints

### Media Endpoints

- `POST /data/media/upload` - Tải lên tệp đa phương tiện
- `GET /data/media` - Lấy danh sách tệp đa phương tiện
- `GET /data/media/:id` - Lấy thông tin chi tiết tệp
- `DELETE /data/media/:id` - Xóa tệp

### URL Endpoints

- `POST /data/url` - Tạo URL mới
- `GET /data/url` - Lấy danh sách URL
- `GET /data/url/:id` - Lấy thông tin chi tiết URL
- `DELETE /data/url/:id` - Xóa URL

### Knowledge Files Endpoints

- `POST /data/knowledge-files/upload` - Tải lên tệp kiến thức
- `GET /data/knowledge-files` - Lấy danh sách tệp kiến thức
- `GET /data/knowledge-files/:id` - Lấy thông tin chi tiết tệp
- `DELETE /data/knowledge-files/:id` - Xóa tệp

### Functions Endpoints

- `POST /data/functions` - Tạo hàm mới
- `GET /data/functions` - Lấy danh sách hàm
- `GET /data/functions/:id` - Lấy thông tin chi tiết hàm
- `DELETE /data/functions/:id` - Xóa hàm
- `POST /data/functions/:id/invoke` - Gọi hàm

## Cách sử dụng

### Tải lên tệp đa phương tiện

```typescript
// Tải lên tệp đa phương tiện
const media = await mediaService.upload(file, {
  type: 'image',
  tags: ['product', 'thumbnail'],
  description: 'Product thumbnail image'
});
```

### Tạo URL rút gọn

```typescript
// Tạo URL rút gọn
const url = await urlService.create({
  originalUrl: 'https://example.com/very/long/url/that/needs/to/be/shortened',
  expiresAt: new Date('2023-12-31')
});
```

### Tải lên tệp kiến thức

```typescript
// Tải lên tệp kiến thức
const knowledgeFile = await knowledgeFilesService.upload(file, {
  type: 'pdf',
  tags: ['manual', 'product'],
  description: 'Product user manual'
});
```

### Tạo và gọi hàm

```typescript
// Tạo hàm mới
const func = await functionsService.create({
  name: 'getWeather',
  description: 'Get weather information for a location',
  parameters: [
    { name: 'location', type: 'string', description: 'Location name or coordinates' }
  ],
  returnType: 'object'
});

// Gọi hàm
const result = await functionsService.invoke(func.id, {
  location: 'New York'
});
```

## Liên kết với các module khác

- **Agent Module**: Sử dụng tệp kiến thức và hàm trong agent AI
- **User Module**: Quản lý quyền sở hữu của tệp và hàm
- **Auth Module**: Xác thực và phân quyền
