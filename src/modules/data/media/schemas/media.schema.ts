import { ApiProperty } from '@nestjs/swagger';
import { Media } from '../entities/media.entity';

export class MediaSchema {
  @ApiProperty({
    description: 'ID của media',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên media',
    example: 'Hình ảnh sản phẩm XYZ',
  })
  name: string;

  @ApiProperty({
    description: '<PERSON>ô tả về tài nguyên media',
    example: '<PERSON><PERSON><PERSON> là hình ảnh chất lượng cao của sản phẩm XYZ, chụp từ nhiều góc độ khác nhau.',
  })
  description: string;

  @ApiProperty({
    description: 'Kích thước media (bytes)',
    example: 1024000,
  })
  size: number;

  @ApiProperty({
    description: 'Các thẻ phân loại media',
    example: ['product', 'image', 'high-quality'],
    nullable: true,
  })
  tags: string[];

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> định danh trên hệ thống lưu trữ',
    example: 'media/products/123e4567-e89b-12d3-a456-426614174000.jpg',
  })
  storageKey: string;

  @ApiProperty({
    description: 'Mã người sở hữu media',
    example: 1,
  })
  ownedBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bản ghi (unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;

  constructor(partial: Partial<Media>) {
    Object.assign(this, partial);
  }
}

export class MediaListResponseSchema {
  @ApiProperty({
    description: 'Danh sách media',
    type: [MediaSchema],
  })
  items: MediaSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số media',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số media trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số media trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
