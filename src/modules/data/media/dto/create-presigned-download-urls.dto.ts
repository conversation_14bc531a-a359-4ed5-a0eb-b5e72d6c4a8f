import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsString } from 'class-validator';

export class CreatePresignedDownloadUrlsDto {
  @ApiProperty({
    description: 'List of keys to get presigned download URLs',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  keys: string[];

  @ApiProperty({ description: 'Expiration time in milliseconds' })
  @IsNumber()
  expirationTimeInMillis: number;
}
