const { defaults } = require('jest-config');

module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../..',
  testRegex: 'src/modules/data/media/tests/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/data/media/**/*.(t|j)s'],
  coverageDirectory: './coverage/media',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
  },
  setupFilesAfterEnv: ['<rootDir>/src/modules/data/media/tests/jest.setup.js'],
  passWithNoTests: true,
}
