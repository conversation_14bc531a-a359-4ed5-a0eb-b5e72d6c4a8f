import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin khách hàng chuyển đổi
 */
export class ConversionCustomerResponseDto {
  @ApiProperty({
    description: 'ID của khách hàng',
    example: 1,
    nullable: true
  })
  id?: number;

  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn <PERSON>ăn <PERSON>'
  })
  name: string;

  @ApiProperty({
    description: 'Email khách hàng',
    example: {
      primary: '<EMAIL>',
      verified: true,
      alternates: ['<EMAIL>']
    },
    nullable: true
  })
  email?: Record<string, any>;

  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0987654321',
    nullable: true
  })
  phone?: string;

  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true
  })
  avatar?: string;

  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true
  })
  platform?: string;

  @ApiProperty({
    description: 'Metadata tùy chỉnh',
    example: {
      age: 30,
      interests: ['thể thao', 'âm nhạc'],
      lastInteraction: 1672531200000
    },
    nullable: true
  })
  metadata?: Record<string, any>;
}

/**
 * DTO cho việc trả về thông tin chuyển đổi
 */
export class ConversionResponseDto {
  @ApiProperty({
    description: 'ID của bản ghi chuyển đổi',
    example: 1,
    nullable: true
  })
  id?: number;

  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 2,
    nullable: true
  })
  convertCustomerId?: number;

  @ApiProperty({
    description: 'Thông tin khách hàng được chuyển đổi',
    type: ConversionCustomerResponseDto,
    nullable: true
  })
  customer?: ConversionCustomerResponseDto;

  @ApiProperty({
    description: 'Loại chuyển đổi',
    example: 'online',
    nullable: true
  })
  conversionType?: string;

  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'Facebook',
    nullable: true
  })
  source?: string;

  @ApiProperty({
    description: 'Ghi chú',
    example: 'Khách hàng quan tâm đến sản phẩm X',
    nullable: true
  })
  notes?: string;

  @ApiProperty({
    description: 'Nội dung bổ sung',
    example: {
      interestedProducts: [1, 2, 3],
      conversationHistory: [
        { time: 1672531200000, message: 'Xin chào' },
        { time: 1672531260000, message: 'Tôi cần tư vấn về sản phẩm X' }
      ]
    },
    nullable: true
  })
  content?: Record<string, any>;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1672531200000,
    nullable: true
  })
  createdAt?: number;
}

/**
 * DTO cho việc trả về kết quả tìm kiếm chuyển đổi
 */
export class ConversionSearchResponseDto {
  @ApiProperty({
    description: 'Danh sách chuyển đổi tìm thấy',
    type: [ConversionResponseDto]
  })
  conversions: ConversionResponseDto[];

  @ApiProperty({
    description: 'Tổng số kết quả',
    example: 25
  })
  total: number;

  @ApiProperty({
    description: 'Bộ lọc đã áp dụng',
    example: {
      conversionType: 'online',
      source: 'Facebook',
      dateRange: { start: 1672531200000, end: 1675209600000 }
    },
    nullable: true
  })
  filters?: Record<string, any>;
}
