import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { OrderPlanHistory } from '@modules/subscription/entities';
import { PaginatedResult } from '@/common/response';
import { QueryDto } from '@common/dto';
import { SqlHelper } from '@common/helpers';

@Injectable()
export class OrderPlanHistoryRepository {
  private sqlHelper: SqlHelper;

  constructor(
    @InjectRepository(OrderPlanHistory)
    private readonly repository: Repository<OrderPlanHistory>,
    private readonly dataSource: DataSource
  ) {
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * Tạo lịch sử đơn hàng mới
   * @param orderHistory Thông tin lịch sử đơn hàng
   * @returns Lịch sử đơn hàng đã tạo
   */
  async createOrderHistory(
    orderHistory: Partial<OrderPlanHistory>,
  ): Promise<OrderPlanHistory> {
    const newOrderHistory = this.repository.create(orderHistory);
    return this.repository.save(newOrderHistory);
  }

  /**
   * Lấy danh sách lịch sử đơn hàng với phân trang
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findOrderHistories(paginationParams: QueryDto): Promise<PaginatedResult<OrderPlanHistory>> {
    return this.sqlHelper.getPaginatedData(this.repository, paginationParams, {
      alias: 'order_history'
    });
  }
}
