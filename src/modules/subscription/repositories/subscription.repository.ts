import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Subscription } from '@modules/subscription/entities';
import {SubscriptionStatus} from "@modules/subscription/enums";
import {QueryDto} from "@common/dto";
import {PaginatedResult} from "@common/response/api-response-dto";
import { SqlHelper } from '@common/helpers';

@Injectable()
export class SubscriptionRepository extends Repository<Subscription> {

  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(Subscription, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * Lấy danh sách đăng ký với bộ lọc
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findSubscriptions(paginationParams: QueryDto): Promise<PaginatedResult<Subscription>> {

    // Sử dụng helper function để lấy kết quả phân trang
    return this.sqlHelper.getPaginatedData(this, paginationParams, {alias: 'subscriptions'});
  }

  /**
   * Lấy danh sách đăng ký của người dùng
   * @param userId ID của người dùng
   * @param status Trạng thái đăng ký (tùy chọn)
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findByUser(userId: number, status: SubscriptionStatus | null, paginationParams: QueryDto): Promise<PaginatedResult<Subscription>> {
    // Tạo search params với filter theo userId và status (nếu có)
    const filters: Record<string, any> = { userId };

    if (status) {
      filters.status = status;
    }

    // Sử dụng helper function để lấy kết quả phân trang
    return this.sqlHelper.getPaginatedData(this, paginationParams, {alias: 'subscriptions'});
  }

  /**
   * Lấy chi tiết đăng ký theo ID
   * @param id ID của đăng ký
   * @returns Chi tiết đăng ký
   */
  async findSubscriptionById(id: number): Promise<Subscription | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Cập nhật trạng thái đăng ký
   * @param id ID của đăng ký
   * @param status Trạng thái mới
   * @returns Đăng ký đã cập nhật
   */
  async updateStatus(id: number, status: SubscriptionStatus): Promise<Subscription | null> {
    const subscription = await this.findSubscriptionById(id);

    if (!subscription) {
      return null;
    }

    subscription.status = status;
    subscription.updatedAt = Date.now();

    return this.save(subscription);
  }

  /**
   * Cập nhật cài đặt tự động gia hạn
   * @param id ID của đăng ký
   * @param autoRenew Cài đặt tự động gia hạn mới
   * @returns Đăng ký đã cập nhật
   */
  async updateAutoRenew(id: number, autoRenew: boolean): Promise<Subscription | null> {
    const subscription = await this.findSubscriptionById(id);

    if (!subscription) {
      return null;
    }

    subscription.autoRenew = autoRenew;
    subscription.updatedAt = Date.now();

    return this.save(subscription);
  }

  /**
   * Thay đổi gói đăng ký
   * @param id ID của đăng ký
   * @param planPricingId ID của tùy chọn giá mới
   * @param usageLimit Giới hạn sử dụng mới
   * @param usageUnit Đơn vị sử dụng mới
   * @returns Đăng ký đã cập nhật
   */
  async changePlan(
    id: number,
    planPricingId: number,
    usageLimit: number,
    usageUnit: string
  ): Promise<Subscription | null> {
    const subscription = await this.findSubscriptionById(id);

    if (!subscription) {
      return null;
    }

    subscription.planPricingId = planPricingId;
    subscription.usageLimit = usageLimit;
    subscription.currentUsage = 0;
    subscription.remainingValue = usageLimit;
    subscription.usageUnit = usageUnit;
    subscription.updatedAt = Date.now();

    return this.save(subscription);
  }
}
