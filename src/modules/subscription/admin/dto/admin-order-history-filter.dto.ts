import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus } from '@modules/subscription/enums/order-status.enum';
import { QueryDto } from '@common/dto';

export class AdminOrderHistoryFilterDto extends QueryDto {
  @ApiProperty({
    description: 'ID của người dùng',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'ID của gói dịch vụ',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatus,
    required: false,
    example: OrderStatus.COMPLETED
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({
    description: 'Thờ<PERSON> gian bắt đầu (Unix timestamp)',
    required: false,
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  startDate?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    required: false,
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  endDate?: number;
}
