import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Plan } from '@modules/subscription/entities';
import { PlanRepository } from '@modules/subscription/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { QueryDto, SortDirection } from '@common/dto';
import { AdminPlanFilterDto, CreatePlanDto, UpdatePlanDto } from '../dto';

@Injectable()
export class AdminPlanService {
  private readonly logger = new Logger(AdminPlanService.name);

  constructor(
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    private readonly planCustomRepository: PlanRepository
  ) {}

  /**
   * Lấy danh sách gói dịch vụ với bộ lọc
   * @param filterDto Bộ lọc
   * @returns Danh sách gói dịch vụ đã phân trang
   */
  async findPlans(filterDto: AdminPlanFilterDto): Promise<PaginatedResult<Plan>> {
    try {
      const { name, packageType, page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = filterDto;

      // Tạo tham số phân trang
      const paginationParams: QueryDto = {
        page,
        limit,
        sortBy,
        sortDirection
      };

      // Tạo điều kiện tìm kiếm
      const where: any = {};

      if (name) {
        where.name = name;
      }

      if (packageType) {
        where.packageType = packageType;
      }

      // Lấy danh sách gói dịch vụ
      return this.planCustomRepository.findPlans(paginationParams);
    } catch (error) {
      this.logger.error(`Error finding plans: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách gói dịch vụ'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết gói dịch vụ
   * @param id ID của gói dịch vụ
   * @returns Thông tin chi tiết gói dịch vụ
   */
  async findPlanById(id: number): Promise<Plan> {
    try {
      const plan = await this.planRepository.findOne({ where: { id } });

      if (!plan) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy gói dịch vụ với ID ${id}`
        );
      }

      return plan;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding plan by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin gói dịch vụ'
      );
    }
  }

  /**
   * Tạo gói dịch vụ mới
   * @param createPlanDto Thông tin gói dịch vụ cần tạo
   * @returns Gói dịch vụ đã tạo
   */
  async createPlan(createPlanDto: CreatePlanDto): Promise<Plan> {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Tạo gói dịch vụ mới
      const newPlan = this.planRepository.create({
        ...createPlanDto,
        createdAt: now,
        updatedAt: now
      });

      // Lưu vào database
      return this.planRepository.save(newPlan);
    } catch (error) {
      this.logger.error(`Error creating plan: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo gói dịch vụ mới'
      );
    }
  }

  /**
   * Cập nhật thông tin gói dịch vụ
   * @param id ID của gói dịch vụ
   * @param updatePlanDto Thông tin cần cập nhật
   * @returns Gói dịch vụ đã cập nhật
   */
  async updatePlan(id: number, updatePlanDto: UpdatePlanDto): Promise<Plan> {
    try {
      // Kiểm tra gói dịch vụ tồn tại
      const plan = await this.findPlanById(id);

      // Cập nhật thông tin
      const now = Math.floor(Date.now() / 1000);
      const updatedPlan = {
        ...plan,
        ...updatePlanDto,
        updatedAt: now
      };

      // Lưu vào database
      await this.planRepository.update(id, updatedPlan);

      // Trả về thông tin đã cập nhật
      return this.findPlanById(id);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating plan: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật gói dịch vụ'
      );
    }
  }

  /**
   * Xóa gói dịch vụ
   * @param id ID của gói dịch vụ
   */
  async deletePlan(id: number): Promise<void> {
    try {
      // Kiểm tra gói dịch vụ tồn tại
      const plan = await this.findPlanById(id);

      // Xóa gói dịch vụ
      await this.planRepository.remove(plan);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting plan: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa gói dịch vụ'
      );
    }
  }
}
