import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlanPricing } from '@modules/subscription/entities';
import { Plan } from '../entities/plan.entity';
import { Subscription } from '@modules/subscription/entities';
import { UsageLog } from '../entities/usage-log.entity';
import { OrderPlanHistory } from '@modules/subscription/entities';
import { PlanPricingUserController, PlanUserController, SubscriptionUserController } from './controllers';
import { PlanPricingUserService, PlanUserService, SubscriptionUserService } from './services';
import { PlanPricingRepository, PlanRepository, SubscriptionRepository, UsageLogRepository, OrderPlanHistoryRepository } from '../repositories';
import { AuthModule } from '@modules/auth/auth.module';
import { ServicesModule } from '@shared/services/services.module';
import {User} from "@modules/user/entities";

@Module({
  imports: [
    TypeOrmModule.forFeature([PlanPricing, Plan, Subscription, UsageLog, User, OrderPlanHistory]),
    AuthModule,
    ServicesModule
  ],
  controllers: [PlanPricingUserController, PlanUserController, SubscriptionUserController],
  providers: [
    PlanPricingUserService,
    PlanPricingRepository,
    PlanUserService,
    PlanRepository,
    SubscriptionUserService,
    SubscriptionRepository,
    UsageLogRepository,
    OrderPlanHistoryRepository
  ],
  exports: [PlanPricingUserService, PlanUserService, SubscriptionUserService],
})
export class SubscriptionUserModule {}
