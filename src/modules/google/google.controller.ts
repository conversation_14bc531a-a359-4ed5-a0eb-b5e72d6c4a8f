import { Controller, Get, Post, Body, Query, Param, UseInterceptors, UploadedFile, Res } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { GoogleApiService, GoogleStorageService, GoogleVisionService, GoogleTranslateService } from '@/shared/services/google';
import { ApiResponseDto } from '@/common/response';
// Định nghĩa kiểu cho Express.Multer.File
declare global {
  namespace Express {
    namespace Multer {
      interface File {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
      }
    }
  }
}

@ApiTags('Google API')
@Controller('google')
export class GoogleController {
  constructor(
    private readonly googleApiService: GoogleApiService,
    private readonly googleStorageService: GoogleStorageService,
    private readonly googleVisionService: GoogleVisionService,
    private readonly googleTranslateService: GoogleTranslateService,
  ) {}

  @Get('auth/url')
  @ApiOperation({ summary: 'Lấy URL xác thực Google' })
  @ApiResponse({ status: 200, description: 'URL xác thực Google' })
  getAuthUrl(@Query('scopes') scopes: string): ApiResponseDto<{ url: string }> {
    const scopesList = scopes ? scopes.split(',') : ['https://www.googleapis.com/auth/userinfo.profile', 'https://www.googleapis.com/auth/userinfo.email'];
    const url = this.googleApiService.generateAuthUrl(scopesList);
    return ApiResponseDto.success({ url }, 'Lấy URL xác thực Google thành công');
  }

  @Get('auth/callback')
  @ApiOperation({ summary: 'Callback xác thực Google' })
  @ApiResponse({ status: 200, description: 'Token xác thực Google' })
  async authCallback(@Query('code') code: string): Promise<ApiResponseDto<any>> {
    const tokens = await this.googleApiService.getToken(code);
    this.googleApiService.setCredentials(tokens);
    const userInfo = await this.googleApiService.getUserInfo();
    return ApiResponseDto.success({ tokens, userInfo }, 'Xác thực Google thành công');
  }

  @Post('translate')
  @ApiOperation({ summary: 'Dịch văn bản' })
  @ApiResponse({ status: 200, description: 'Văn bản đã dịch' })
  async translateText(
    @Body() body: { text: string; targetLanguage: string; sourceLanguage?: string },
  ): Promise<ApiResponseDto<{ translatedText: string; detectedLanguage?: { language: string; confidence: number } }>> {
    const { text, targetLanguage, sourceLanguage } = body;

    let detectedLanguage: { language: string; confidence: number } | undefined;
    if (!sourceLanguage) {
      detectedLanguage = await this.googleTranslateService.detectLanguage(text);
    }

    const translatedText = await this.googleTranslateService.translateText(
      text,
      targetLanguage,
      sourceLanguage || detectedLanguage?.language,
    );

    return ApiResponseDto.success({ translatedText, detectedLanguage }, 'Dịch văn bản thành công');
  }

  @Post('vision/detect-text')
  @ApiOperation({ summary: 'Phát hiện văn bản trong hình ảnh' })
  @ApiResponse({ status: 200, description: 'Văn bản được phát hiện' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async detectText(@UploadedFile() file: Express.Multer.File): Promise<ApiResponseDto<{ texts: string[] }>> {
    const texts = await this.googleVisionService.detectText(file.buffer);
    return ApiResponseDto.success({ texts }, 'Phát hiện văn bản thành công');
  }

  @Post('storage/upload')
  @ApiOperation({ summary: 'Tải file lên Google Cloud Storage' })
  @ApiResponse({ status: 200, description: 'URL của file đã tải lên' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        destination: {
          type: 'string',
          example: 'uploads/image.jpg',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('destination') destination: string,
  ): Promise<ApiResponseDto<{ url: string }>> {
    const url = await this.googleStorageService.uploadBuffer(
      file.buffer,
      destination || `uploads/${Date.now()}-${file.originalname}`,
      file.mimetype,
    );
    return ApiResponseDto.success({ url }, 'Tải file lên thành công');
  }

  @Get('storage/signed-url')
  @ApiOperation({ summary: 'Tạo URL có chữ ký để tải file lên' })
  @ApiResponse({ status: 200, description: 'URL có chữ ký' })
  async getSignedUrl(
    @Query('destination') destination: string,
    @Query('contentType') contentType: string,
    @Query('expiresIn') expiresIn: number,
  ): Promise<ApiResponseDto<{ url: string }>> {
    const url = await this.googleStorageService.getSignedUploadUrl(
      destination,
      contentType,
      expiresIn || 900,
    );
    return ApiResponseDto.success({ url }, 'Tạo URL có chữ ký thành công');
  }

  @Get('storage/download/:filename')
  @ApiOperation({ summary: 'Tạo URL có chữ ký để tải file xuống' })
  @ApiResponse({ status: 200, description: 'URL có chữ ký' })
  async getDownloadUrl(
    @Param('filename') filename: string,
    @Query('expiresIn') expiresIn: number,
    @Res() res: Response,
  ): Promise<void> {
    const url = await this.googleStorageService.getSignedDownloadUrl(
      filename,
      expiresIn || 3600,
    );
    res.redirect(url);
  }
}
