# Model Training Module

## Overview

The Model Training module provides functionality for managing AI model training in the system. It supports base models, fine-tuning models, and data for fine-tuning.

## Database Structure

The module uses the following database tables:

- `base_models`: Foundation models available in the system
- `fine_tuning_models`: Fine-tuned models created from base models
- `data_fine_tuning_models`: Relationship between fine-tuning models and training data
- `data_fine_tuning`: Training data for fine-tuning models

## Module Structure

The module is organized into the following components:

### Entities

- `BaseModel`: Entity for base/foundation models
- `FineTuningModel`: Entity for fine-tuned models
- `DataFineTuningModel`: Entity for the relationship between models and training data
- `DataFineTuning`: Entity for training data

### DTOs

- `CreateBaseModelDto`: DTO for creating a new base model
- `UpdateBaseModelDto`: DTO for updating an existing base model
- `CreateFineTuningModelDto`: DTO for creating a new fine-tuning model
- `UpdateFineTuningModelDto`: DTO for updating an existing fine-tuning model
- `UpdateDataFineTuningModelDto`: DTO for updating the relationship between models and data
- `CreateDataFineTuningDto`: DTO for creating new training data
- `UpdateDataFineTuningDto`: DTO for updating existing training data

### Repositories

- `BaseModelRepository`: Custom repository for base models
- `FineTuningModelRepository`: Custom repository for fine-tuning models
- `DataFineTuningModelRepository`: Custom repository for model-data relationships
- `DataFineTuningRepository`: Custom repository for training data

### Services

#### Admin Services

- `BaseModelAdminService`: Service for admin operations on base models
- `FineTuningModelAdminService`: Service for admin operations on fine-tuning models
- `DataFineTuningAdminService`: Service for admin operations on training data

#### User Services

- `BaseModelUserService`: Service for user operations on base models
- `FineTuningModelUserService`: Service for user operations on fine-tuning models
- `DataFineTuningUserService`: Service for user operations on training data

### Controllers

#### Admin Controllers

- `BaseModelAdminController`: Controller for admin operations on base models
- `FineTuningModelAdminController`: Controller for admin operations on fine-tuning models
- `DataFineTuningAdminController`: Controller for admin operations on training data

#### User Controllers

- `BaseModelUserController`: Controller for user operations on base models
- `FineTuningModelUserController`: Controller for user operations on fine-tuning models
- `DataFineTuningUserController`: Controller for user operations on training data

## Usage

### Managing Base Models (Admin)

Administrators can create and manage base models:

```typescript
const createBaseModelDto = {
  name: 'GPT-4 Turbo',
  providerId: 1,
  description: 'OpenAI GPT-4 Turbo model',
  config: {
    apiEndpoint: 'https://api.openai.com/v1/chat/completions',
    apiVersion: '2023-05-15',
    modelName: 'gpt-4-turbo'
  },
  baseInputRate: 0.01,
  baseOutputRate: 0.03,
  baseTrainRate: 0.0,
  fineTuningInputRate: 0.012,
  fineTuningOutputRate: 0.036,
  fineTuningTrainRate: 0.8
};

// POST to /admin/base-models
```

### Creating Fine-Tuning Models (User)

Users can create fine-tuning models based on base models:

```typescript
const createFineTuningModelDto = {
  name: 'My Custom Assistant',
  baseModelId: 'base-model-uuid',
  description: 'Custom assistant for my business',
  config: {
    epochs: 3,
    batchSize: 4,
    learningRate: 0.0001
  }
};

// POST to /user/fine-tuning-models/user/{userId}
```

### Managing Training Data (User)

Users can create and manage training data for fine-tuning:

```typescript
const createDataFineTuningDto = {
  name: 'Customer Support Dataset',
  description: 'Training data for customer support',
  trainData: [
    { input: 'How do I reset my password?', output: 'You can reset your password by...' },
    // More training examples
  ],
  validationData: [
    { input: 'Where can I find my account settings?', output: 'Your account settings are located...' },
    // More validation examples
  ]
};

// POST to /user/data-fine-tuning/user/{userId}
```

## API Endpoints

See the Swagger documentation for a complete list of API endpoints.
