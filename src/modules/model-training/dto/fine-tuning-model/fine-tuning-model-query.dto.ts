import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho các trường sắp xếp của fine-tuning model
 */
export enum FineTuningModelSortByEnum {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  STATUS = 'status',
}

/**
 * DTO cho việc truy vấn danh sách fine-tuning model
 */
export class FineTuningModelQueryDto extends QueryDto {

  /**
   * Cờ đánh dấu mô hình có ở chế độ riêng tư hay không
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> theo quyền riêng tư (true: riêng tư, false: công khai)',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isPrivate?: boolean;

  /**
   * ID của nhà cung cấp (UUID)
   */
  @ApiPropertyOptional({
    description: 'ID của nhà cung cấp (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  providerId?: string;

  /**
   * Trường cần sắp xếp
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: FineTuningModelSortByEnum,
    example: FineTuningModelSortByEnum.CREATED_AT,
    default: FineTuningModelSortByEnum.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(FineTuningModelSortByEnum)
  sortBy?: FineTuningModelSortByEnum = FineTuningModelSortByEnum.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
