import { QueryDto, SortDirection } from "@common/dto";
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { BaseModelDeletedSortByEnum } from '../../constants/base-model-sort-by.enum';

/**
 * Lớp mở rộng QueryDto cho việc truy vấn base model
 * Lưu ý: Đã loại bỏ trường status vì API chỉ trả về các model có trạng thái APPROVED
 */
export class BaseModelDeletedQueryDto extends QueryDto {
    /**
     * Trường cần sắp xếp
     */
    @ApiProperty({
        description: 'Trường cần sắp xếp',
        enum: BaseModelDeletedSortByEnum,
        example: BaseModelDeletedSortByEnum.DELETED_AT,
        default: BaseModelDeletedSortByEnum.DELETED_AT,
        required: false,
    })
    @IsOptional()
    @IsEnum(BaseModelDeletedSortByEnum)
    sortBy?: BaseModelDeletedSortByEnum = BaseModelDeletedSortByEnum.DELETED_AT;

    /**
     * Hướng sắp xếp
     */
    @ApiProperty({
        description: 'Hướng sắp xếp',
        enum: SortDirection,
        example: SortDirection.DESC,
        default: SortDirection.DESC,
        required: false,
    })
    @IsOptional()
    @IsEnum(SortDirection)
    sortDirection?: SortDirection = SortDirection.DESC;
}
