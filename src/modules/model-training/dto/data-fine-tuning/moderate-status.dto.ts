import { ApiProperty } from "@nestjs/swagger";
import { IsEnum } from "class-validator";
import { DataFineTunningStatusEnum } from "../../constants/data-fine-tunning.enum";

export class ModerateStatusDto {
  /**
   * Trạng thái phê duyệt
   */
  @ApiProperty({
    description: 'Trạng thái phê duyệt',
    enum: ['APPROVED', 'REJECTED'],
    example: 'APPROVED',
  })
  @IsEnum(['APPROVED', 'REJECTED'])
  status: DataFineTunningStatusEnum;
}