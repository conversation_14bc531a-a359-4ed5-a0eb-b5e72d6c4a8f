import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, IsBoolean, ArrayNotEmpty, IsUUID, IsEnum, IsString } from 'class-validator';
import { DataFineTunningStatusEnum } from '@/modules/model-training/constants/data-fine-tunning.enum';
import { TrainingDataset } from '../../interfaces';

export class UpdateDataFineTuningDto {
    @ApiProperty({
        description: 'Name of the fine-tuning model',
        example: 'Custom Assistant v1',
    })
    @IsString()
    name: string;

    @ApiProperty({
        description: 'Detailed description of the model and its use case',
        example: 'A model fine-tuned for answering academic questions in Vietnamese.',
        required: false,
    })
    @IsOptional()
    @IsString()
    description: string;

    @ApiProperty({
        description: 'Training data (JSONB array)',
        required: false,
        type: 'array',
        example: [
            {
                messages: [
                    { role: 'system', content: 'Bạn là AI' },
                    { role: 'user', content: 'Xin chào' },
                    { role: 'assistant', content: 'Chào bạn' }
                ]
            }
        ]
    })
    @IsOptional()
    @IsArray()
    trainData: TrainingDataset;

    @ApiProperty({
        description: 'Validation data (JSONB array)',
        required: false,
        type: 'array',
        example: [
            {
                messages: [
                    { role: 'user', content: 'Hôm nay thời tiết thế nào?' },
                    { role: 'assistant', content: 'Tôi không thể kiểm tra thời tiết thời gian thực.' }
                ]
            }
        ]
    })
    @IsOptional()
    @IsArray()
    validationData?: TrainingDataset;
}

export class DeleteManyDto {
    @ApiProperty({
        description: 'Array of UUIDs to delete',
        example: [
            '2820b2d5-642a-4c65-a1d4-f12e92526f64',
            '324cecca-0b45-4e89-98c1-e56494aedd1a',
        ],
    })
    @IsArray()
    @ArrayNotEmpty()
    @IsUUID('all', { each: true })
    ids: string[];
}

// Using the existing DataFineTunningStatusEnum from enums/data-fine-tunning.enum.ts

export class DataFineTuningDto {
    @ApiProperty({
        description: 'Unique ID of the fine-tuning model',
        example: 'ft:gpt-3.5-turbo:your-org:custom-name:abcd1234efgh',
    })
    @IsUUID()
    id: string;

    @ApiProperty({
        description: 'Name of the fine-tuning model',
        example: 'Custom Assistant v1',
    })
    @IsString()
    name: string;

    @ApiProperty({
        description: 'Detailed description of the model and its use case',
        example: 'A model fine-tuned for answering academic questions in Vietnamese.',
        required: false,
    })
    @IsOptional()
    @IsString()
    description: string;

    @ApiProperty({
        description: 'Current status of the fine-tuning process',
        example: 'DRAFT',
        enum: DataFineTunningStatusEnum
    })
    @IsEnum(DataFineTunningStatusEnum)
    status: DataFineTunningStatusEnum;

    /**
     * Tên tác giả hoặc người tạo mô hình
     */
    @ApiProperty({
        description: 'Tên người tạo mô hình',
        example: 'Nguyễn Văn A',
        required: false,
    })
    @IsOptional()
    @IsString()
    author?: string;

    /**
     * URL ảnh đại diện (avatar) cho mô hình
     */
    @ApiProperty({
        description: 'URL của ảnh đại diện mô hình (nếu có)',
        example: 'https://example.com/avatar.png',
        required: false,
    })
    @IsOptional()
    @IsString()
    avatar?: string;
}