import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserProviderModel } from '../entities/user-provider-model.entity';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';

/**
 * Repository cho UserProviderModel
 */
@Injectable()
export class UserProviderModelRepository extends Repository<UserProviderModel> {
  constructor(private dataSource: DataSource) {
    super(UserProviderModel, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản
   * @returns SelectQueryBuilder
   */
  private createBaseQuery(): SelectQueryBuilder<UserProviderModel> {
    return this.createQueryBuilder('userProviderModel');
  }

  /**
   * Kiểm tra tên provider đã tồn tại chưa cho một user cụ thể
   * @param name Tên provider cần kiểm tra
   * @param userId ID của user
   * @param excludeId ID provider cần loại trừ (dùng khi update)
   * @returns boolean
   */
  async isNameExists(name: string, userId: number, excludeId?: string): Promise<boolean> {
    const query = this.createBaseQuery()
      .where('userProviderModel.name = :name', { name })
      .andWhere('userProviderModel.user_id = :userId', { userId })
      .andWhere('userProviderModel.deleted_at IS NULL');

    if (excludeId) {
      query.andWhere('userProviderModel.id != :id', { id: excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Lấy danh sách provider của một user có phân trang
   * @param userId ID của user
   * @param queryDto Query parameters
   * @returns Danh sách provider có phân trang
   */
  async findPaginatedByUserId(
    userId: number,
    queryDto: QueryDto
  ): Promise<PaginatedResult<UserProviderModel>> {
    const { page, limit, sortBy = 'created_at', sortDirection } = queryDto;

    const query = this.createBaseQuery()
      .where('userProviderModel.user_id = :userId', { userId })
      .andWhere('userProviderModel.deleted_at IS NULL')
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`userProviderModel.${sortBy}`, sortDirection);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      }
    };
  }

  /**
   * Lấy chi tiết provider theo ID và userId
   * @param id ID của provider
   * @param userId ID của user
   * @returns Chi tiết provider
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserProviderModel | null> {
    return this.createBaseQuery()
      .where('userProviderModel.id = :id', { id })
      .andWhere('userProviderModel.user_id = :userId', { userId })
      .andWhere('userProviderModel.deleted_at IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra provider có tồn tại không cho một user cụ thể
   * @param id ID của provider
   * @param userId ID của user
   * @returns boolean
   */
  async isExists(id: string, userId: number): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('userProviderModel.id = :id', { id })
      .andWhere('userProviderModel.user_id = :userId', { userId })
      .andWhere('userProviderModel.deleted_at IS NULL')
      .getCount();

    return count > 0;
  }

  /**
   * Xóa mềm provider
   * @param id ID của provider
   * @returns boolean
   */
  @Transactional()
  async softDeleteProvider(id: string): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(UserProviderModel)
      .set({
        deletedAt: () => '(EXTRACT(EPOCH FROM now()) * 1000)::bigint',
      })
      .where('id = :id', { id })
      .andWhere('deleted_at IS NULL')
      .execute();

    return result.affected ? result.affected > 0 : false;
  }
}
