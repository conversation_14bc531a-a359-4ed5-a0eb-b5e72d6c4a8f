import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { DataFineTuning } from '../entities/data-fine-tuning.entity';
import { PaginatedResult } from '@/common/response';
import { DataFineTuningQueryDto } from '../dto/data-fine-tuning/data-fine-tuning-query.dto';
import { DataFineTunningStatusEnum } from '../constants/data-fine-tunning.enum';
import { Transactional } from 'typeorm-transactional';
import { QueryDto } from '@/common/dto';

/**
 * Repository cho bảng data_fine_tuning
 * Cung cấp các phương thức truy vấn và thao tác với dữ liệu fine-tuning
 */
@Injectable()
export class DataFineTuningRepository extends Repository<DataFineTuning> {
  private readonly logger = new Logger(DataFineTuningRepository.name);

  constructor(private dataSource: DataSource) {
    super(DataFineTuning, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho data fine tuning
   * @returns SelectQueryBuilder<DataFineTuning> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<DataFineTuning> {
    return this.createQueryBuilder('dataFineTuning');
  }

  /**
   * Tìm kiếm dữ liệu fine-tuning với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi data_fine_tuning
   */
  async findPaginated(query: DataFineTuningQueryDto): Promise<PaginatedResult<DataFineTuning>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const queryBuilder = this.createBaseQuery()
      .select([
        'dataFineTuning.id',
        'dataFineTuning.name',
        'dataFineTuning.description',
        'dataFineTuning.status',
        'dataFineTuning.isForSale',
        'dataFineTuning.ownerBy',
        'dataFineTuning.createdAt',
        'dataFineTuning.updatedAt',
      ]);

    // Tìm kiếm theo tên hoặc mô tả
    if (query.search) {
      queryBuilder.andWhere(
        '(dataFineTuning.name ILIKE :search OR dataFineTuning.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Lọc theo trạng thái
    if (query.status) {
      queryBuilder.andWhere('dataFineTuning.status = :status', { status: query.status });
    }

    // Lọc theo trạng thái rao bán
    if (query.isForSale !== undefined) {
      queryBuilder.andWhere('dataFineTuning.isForSale = :isForSale', { isForSale: query.isForSale });
    }

    // Chỉ lấy các bản ghi chưa bị xóa
    queryBuilder.andWhere('dataFineTuning.deletedAt IS NULL');

    // Sắp xếp
    queryBuilder.orderBy('dataFineTuning.updatedAt', 'DESC');

    // Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm dữ liệu fine-tuning theo ID
   * @param id UUID của bản ghi cần tìm
   * @returns Thông tin chi tiết của bản ghi hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<DataFineTuning | null> {
    try {
      return await this.createBaseQuery()
        .select([
          'dataFineTuning.id',
          'dataFineTuning.name',
          'dataFineTuning.description',
          'dataFineTuning.trainData',
          'dataFineTuning.validationData',
          'dataFineTuning.status',
          'dataFineTuning.isForSale',
          'dataFineTuning.createdAt',
          'dataFineTuning.updatedAt',
        ])
        .where('dataFineTuning.id = :id', { id })
        .andWhere('dataFineTuning.deletedAt IS NULL')
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Xóa mềm một bản ghi dữ liệu fine-tuning
   * @param id UUID của bản ghi cần xóa
   */
  @Transactional()
  async softDeleteCustom(id: string): Promise<void> {
    await this.update(id, {
      deletedAt: Date.now(),
    });
  }

  /**
   * Xóa mềm nhiều bản ghi dữ liệu fine-tuning
   * @param ids Mảng UUID của các bản ghi cần xóa
   */
  @Transactional()
  async softDeleteMany(ids: string[]): Promise<void> {
    await this.createQueryBuilder()
      .update(DataFineTuning)
      .set({ deletedAt: Date.now() })
      .whereInIds(ids)
      .execute();
  }

  /**
   * Cập nhật trạng thái của bản ghi dữ liệu fine-tuning
   * @param id UUID của bản ghi cần cập nhật
   * @param status Trạng thái mới
   */
  @Transactional()
  async updateStatus(id: string, status: DataFineTunningStatusEnum): Promise<void> {
    await this.update(id, {
      status,
      updatedAt: Date.now(),
    });
  }

  /**
   * Tìm kiếm dữ liệu fine-tuning theo người sở hữu
   * @param ownerId ID của người sở hữu
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi data_fine_tuning
   */
  async findByOwner(ownerId: number, query: DataFineTuningQueryDto): Promise<PaginatedResult<DataFineTuning>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    // Tạo query builder với join đến bảng user_data_fine_tuning để lọc theo người dùng
    const queryBuilder = this.createBaseQuery()
      .select([
        'dataFineTuning.id',
        'dataFineTuning.name',
        'dataFineTuning.description',
        'dataFineTuning.status',
        'dataFineTuning.isForSale',
        'dataFineTuning.createdAt',
        'dataFineTuning.updatedAt',
      ])
      .innerJoin('user_data_fine_tuning', 'udf', 'udf.id = dataFineTuning.id')
      .where('udf.user_id = :ownerId', { ownerId })
      .andWhere('dataFineTuning.deletedAt IS NULL');

    // Tìm kiếm theo tên hoặc mô tả
    if (query.search) {
      queryBuilder.andWhere(
        '(dataFineTuning.name ILIKE :search OR dataFineTuning.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Lọc theo trạng thái
    if (query.status) {
      queryBuilder.andWhere('dataFineTuning.status = :status', { status: query.status });
    }

    // Sắp xếp
    queryBuilder.orderBy('dataFineTuning.updatedAt', 'DESC');

    // Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy danh sách dữ liệu fine-tuning của admin với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi data_fine_tuning của admin
   */
  async findAll(query: QueryDto): Promise<PaginatedResult<DataFineTuning>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    // Tạo query builder với join đến bảng admin_data_fine_tuning
    const queryBuilder = this.createBaseQuery()
      .select([
        'dataFineTuning.id',
        'dataFineTuning.name',
        'dataFineTuning.description',
        'dataFineTuning.status',
        'dataFineTuning.isForSale',
        'dataFineTuning.createdAt',
        'dataFineTuning.updatedAt',
      ])
      .innerJoin('admin_data_fine_tuning', 'adf', 'adf.id = dataFineTuning.id')
      .where('dataFineTuning.deletedAt IS NULL');

    // Tìm kiếm theo tên hoặc mô tả
    if (query.search) {
      queryBuilder.andWhere(
        '(dataFineTuning.name ILIKE :search OR dataFineTuning.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Sắp xếp
    queryBuilder.orderBy('dataFineTuning.updatedAt', 'DESC');

    // Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
