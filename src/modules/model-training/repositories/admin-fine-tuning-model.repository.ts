import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { Employee } from '@modules/employee/entities/employee.entity';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FineTuningModelQueryDto } from '../dto/fine-tuning-model/fine-tuning-model-query.dto';
import { AdminFineTuningModel } from '../entities/admin-fine-tuning-model.entity';
import { BaseModel } from '../entities/base-model.entity';
import { FineTuningModel } from '../entities/fine-tuning-model.entity';
import { CreateAdminFineTuningModelDto } from '../dto/fine-tuning-model/create-admin-fine-tuning-model.dto';
import { FineTuningModelIdValidatorHelper } from '../helpers/fine-tuning-model-id-validator.helper';
import { TypeProviderEnum } from '../constants/type-provider.enum';
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';

/**
 * Repository cho bảng admin_fine_tuning_models
 * Quản lý các thao tác CRUD và truy vấn liên quan đến mô hình fine-tuning của admin
 */
@Injectable()
export class AdminFineTuningModelRepository extends Repository<AdminFineTuningModel> {
  private readonly logger = new Logger(AdminFineTuningModelRepository.name);

  constructor(
    private dataSource: DataSource,
    private readonly fineTuningModelIdValidator: FineTuningModelIdValidatorHelper,
  ) {
    super(AdminFineTuningModel, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản với các join cần thiết
   * @returns SelectQueryBuilder đã được cấu hình với các join
   */
  private createBaseQuery(): SelectQueryBuilder<AdminFineTuningModel> {
    return this.createQueryBuilder('adminModel')
      .leftJoin(FineTuningModel, 'fineTuningModel', 'fineTuningModel.id = adminModel.id')
      .leftJoin(BaseModel, 'baseModel', 'baseModel.id = adminModel.model_base_id')
      .leftJoin(Employee, 'createdBy', 'createdBy.id = adminModel.created_by')
      .leftJoin(Employee, 'updatedBy', 'updatedBy.id = adminModel.updated_by')
      .leftJoin(Employee, 'deletedBy', 'deletedBy.id = adminModel.deleted_by');
  }

  /**
   * Tìm mô hình fine-tuning của admin theo ID với đầy đủ thông tin chi tiết
   * @param id ID của mô hình fine-tuning
   * @returns Thông tin chi tiết của mô hình fine-tuning hoặc null nếu không tìm thấy
   */
  async findByIdWithDetails(id: string): Promise<AdminFineTuningModel | null> {
    try {
      // Sử dụng raw query để tối ưu hiệu suất và tránh lỗi
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'afm.id',
          'afm.created_by AS "createdBy"',
          'afm.updated_by AS "updatedBy"',
          'afm.deleted_by AS "deletedBy"',
          'afm.model_base_id AS "modelBaseId"',
          'afm.is_private AS "isPrivate"',
          'ftm.name',
          'ftm.description',
          'ftm.token',
          'ftm.status',
          'ftm.detail_id AS "detailId"',
          'ftm.created_at AS "createdAt"',
          'ftm.updated_at AS "updatedAt"',
          'bm.name AS "baseModelName"',
          'creator.id AS "creatorId"',
          'creator.full_name AS "creatorName"',
          'creator.avatar AS "creatorAvatar"',
          'updater.id AS "updaterId"',
          'updater.full_name AS "updaterName"',
          'updater.avatar AS "updaterAvatar"',
          'deleter.id AS "deleterId"',
          'deleter.full_name AS "deleterName"',
          'deleter.avatar AS "deleterAvatar"'
        ])
        .from('admin_fine_tuning_models', 'afm')
        .leftJoin('fine_tuning_models', 'ftm', 'ftm.id = afm.id')
        .leftJoin('base_models', 'bm', 'bm.id = afm.model_base_id')
        .leftJoin('employees', 'creator', 'creator.id = afm.created_by')
        .leftJoin('employees', 'updater', 'updater.id = afm.updated_by')
        .leftJoin('employees', 'deleter', 'deleter.id = afm.deleted_by')
        .where('afm.id = :id', { id })
        .andWhere('ftm.deleted_at IS NULL');

      const rawResult = await queryBuilder.getRawOne();

      if (!rawResult) {
        return null;
      }

      // Chuyển đổi kết quả raw thành đối tượng AdminFineTuningModel
      const model = new AdminFineTuningModel();
      model.id = rawResult.id;
      model.createdBy = rawResult.createdBy;
      model.updatedBy = rawResult.updatedBy;
      model.deletedBy = rawResult.deletedBy;
      model.modelBaseId = rawResult.modelBaseId;
      model.isPrivate = rawResult.isPrivate;

      // Thêm thông tin từ các bảng liên kết
      Object.defineProperty(model, 'fineTuningModel', {
        enumerable: true,
        value: {
          name: rawResult.name,
          description: rawResult.description,
          token: rawResult.token,
          status: rawResult.status,
          detailId: rawResult.detailId,
          createdAt: rawResult.createdAt,
          updatedAt: rawResult.updatedAt
        }
      });

      Object.defineProperty(model, 'baseModel', {
        enumerable: true,
        value: {
          name: rawResult.baseModelName
        }
      });

      // Thêm thông tin người tạo
      if (rawResult.creatorId) {
        Object.defineProperty(model, 'creator', {
          enumerable: true,
          value: {
            id: rawResult.creatorId,
            fullName: rawResult.creatorName,
            avatar: rawResult.creatorAvatar
          }
        });
      }

      // Thêm thông tin người cập nhật
      if (rawResult.updaterId) {
        Object.defineProperty(model, 'updater', {
          enumerable: true,
          value: {
            id: rawResult.updaterId,
            fullName: rawResult.updaterName,
            avatar: rawResult.updaterAvatar
          }
        });
      }

      // Thêm thông tin người xóa
      if (rawResult.deleterId) {
        Object.defineProperty(model, 'deleter', {
          enumerable: true,
          value: {
            id: rawResult.deleterId,
            fullName: rawResult.deleterName,
            avatar: rawResult.deleterAvatar
          }
        });
      }

      return model;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm mô hình fine-tuning admin với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm tất cả mô hình fine-tuning của admin với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các mô hình fine-tuning của admin
   */
  async findAllPaginated(query: FineTuningModelQueryDto): Promise<PaginatedResult<AdminFineTuningModel>> {
    try {
      const page = query.page ?? 1;
      const limit = query.limit ?? 10;
      const skip = (page - 1) * limit;

      // Sử dụng raw query để tối ưu hiệu suất và tránh lỗi
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          '"afm"."id"',
          '"afm"."created_by" AS "createdBy"',
          '"afm"."updated_by" AS "updatedBy"',
          '"afm"."model_base_id" AS "modelBaseId"',
          '"afm"."is_private" AS "isPrivate"',
          '"ftm"."name"',
          '"ftm"."description"',
          '"ftm"."token"',
          '"ftm"."status"',
          '"ftm"."created_at" AS "createdAt"',
          '"ftm"."updated_at" AS "updatedAt"',
          '"e"."full_name" AS "creatorName"',
          '"e"."avatar" AS "creatorAvatar"'
        ])
        .from('admin_fine_tuning_models', 'afm')
        .leftJoin('fine_tuning_models', 'ftm', '"ftm"."id" = "afm"."id"')
        .leftJoin('base_models', 'bm', '"bm"."id" = "afm"."model_base_id"')
        .leftJoin('employees', 'e', '"e"."id" = "afm"."created_by"')
        .where('"ftm"."deleted_at" IS NULL');

      // Tìm kiếm theo tên hoặc mô tả
      if (query.search) {
        queryBuilder.andWhere(
          '("ftm"."name" ILIKE :search OR "ftm"."description" ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      // Lọc theo quyền riêng tư
      if (query.isPrivate !== undefined) {
        queryBuilder.andWhere('"afm"."is_private" = :isPrivate', { isPrivate: query.isPrivate });
      }

      // Đếm tổng số bản ghi
      const countQuery = this.dataSource
        .createQueryBuilder()
        .select('COUNT(DISTINCT "afm"."id")', 'count')
        .from('admin_fine_tuning_models', 'afm')
        .leftJoin('fine_tuning_models', 'ftm', '"ftm"."id" = "afm"."id"')
        .where('"ftm"."deleted_at" IS NULL');

      // Thêm điều kiện tìm kiếm vào count query
      if (query.search) {
        countQuery.andWhere(
          '("ftm"."name" ILIKE :search OR "ftm"."description" ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      if (query.isPrivate !== undefined) {
        countQuery.andWhere('"afm"."is_private" = :isPrivate', { isPrivate: query.isPrivate });
      }

      const totalResult = await countQuery.getRawOne();
      const totalItems = parseInt(totalResult?.count || '0', 10);

      // Sắp xếp
      const sortField = query.sortBy || 'createdAt';
      const sortDirection = query.sortDirection || 'DESC';

      // Chuyển đổi tên trường camelCase sang snake_case cho SQL
      const sortFieldMap: Record<string, string> = {
        'createdAt': 'ftm.created_at',
        'updatedAt': 'ftm.updated_at',
        'name': 'ftm.name',
        'status': 'ftm.status',
        'token': 'ftm.token'
      };

      const sqlSortField = sortFieldMap[sortField] || 'ftm.created_at';
      queryBuilder.orderBy(sqlSortField, sortDirection);

      // Phân trang
      queryBuilder.offset(skip).limit(limit);

      // Thực hiện truy vấn
      const rawItems = await queryBuilder.getRawMany();

      // Chuyển đổi kết quả raw thành đối tượng AdminFineTuningModel
      const items = rawItems.map(raw => {
        const model = new AdminFineTuningModel();
        model.id = raw.id;
        model.createdBy = raw.createdBy;
        model.updatedBy = raw.updatedBy;
        model.modelBaseId = raw.modelBaseId;
        model.isPrivate = raw.isPrivate;

        // Thêm thông tin từ các bảng liên kết
        Object.defineProperty(model, 'fineTuningModel', {
          enumerable: true,
          value: {
            name: raw.name,
            description: raw.description,
            token: raw.token,
            status: raw.status,
            createdAt: raw.createdAt,
            updatedAt: raw.updatedAt
          }
        });

        Object.defineProperty(model, 'baseModel', {
          enumerable: true,
          value: {
            name: raw.baseModelName
          }
        });

        Object.defineProperty(model, 'creator', {
          enumerable: true,
          value: {
            fullName: raw.creatorName,
            avatar: raw.creatorAvatar
          }
        });

        return model;
      });

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning admin: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`
      );
    }
  }

  /**
   * Tìm mô hình fine-tuning của admin theo ID
   * @param id ID của mô hình fine-tuning
   * @returns Thông tin mô hình fine-tuning hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<AdminFineTuningModel | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm mô hình fine-tuning admin với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Kiểm tra xem ID của fine-tuning model có tồn tại trong provider tương ứng không
   * @param id ID của fine-tuning model
   * @param baseModelId ID của base model
   * @param providerId ID của provider
   * @throws AppException nếu ID không tồn tại hoặc có lỗi xảy ra
   */
  async validateFineTuningModelId(id: string, baseModelId: string, providerId: string): Promise<void> {
    try {
      // Lấy thông tin về base model để biết provider type
      const baseModel = await this.dataSource
        .getRepository(BaseModel)
        .findOne({ where: { id: baseModelId } });

      if (!baseModel) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
          `Không tìm thấy base model với ID ${baseModelId}`
        );
      }

      // Admin có thể sử dụng base model ở trạng thái DRAFT hoặc APPROVED

      // Lấy API key từ admin_provider_models
      const provider = await this.dataSource
        .getRepository('admin_provider_models')
        .createQueryBuilder('provider')
        .select(['provider.type', 'provider.apiKey'])
        .where('provider.id = :id', { id: providerId })
        .andWhere('provider.deleted_at IS NULL')
        .getOne();

      if (!provider) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
          `Không tìm thấy provider với ID ${providerId}`
        );
      }

      const apiKey = provider.apiKey;
      if (!apiKey) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
          `Provider ${providerId} không có API key`
        );
      }

      // Sử dụng helper để kiểm tra ID trong provider tương ứng
      const providerType = TypeProviderEnum[baseModel.providerId as keyof typeof TypeProviderEnum];
      if (!providerType) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
          `Không hỗ trợ provider type: ${baseModel.providerId}`
        );
      }

      // Gọi validator - nó sẽ throw lỗi nếu ID không tồn tại
      try {
        await this.fineTuningModelIdValidator.validateFineTuningModelId(
          id,
          providerType,
          apiKey,
        );
      } catch (validationError) {
        // Xử lý lỗi API key hết hạn
        if (validationError.message && (
            validationError.message.includes('API key đã hết hạn') ||
            validationError.message.includes('unauthorized') ||
            validationError.message.includes('authentication') ||
            validationError.message.includes('invalid key') ||
            validationError.message.includes('expired'))) {
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.API_KEY_EXPIRED,
            `API key của provider ${providerId} đã hết hạn hoặc không hợp lệ`
          );
        }

        // Xử lý lỗi không tìm thấy model
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
          `ID fine-tuning model ${id} không tồn tại trong provider tương ứng với base model ${baseModelId}`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi kiểm tra ID fine-tuning model: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
        `ID fine-tuning model ${id} không tồn tại trong provider tương ứng với base model ${baseModelId}`
      );
    }
  }

  /**
   * Tạo mới mô hình fine-tuning của admin
   * @param dto Dữ liệu tạo mô hình
   * @param employeeId ID của nhân viên tạo mô hình
   * @returns Mô hình đã tạo hoặc null nếu tạo thất bại
   */
  async createAdminFineTuningModel(
    dto: CreateAdminFineTuningModelDto,
    employeeId: number,
  ): Promise<AdminFineTuningModel | null> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Tạo bản ghi trong bảng fine_tuning_models
      const fineTuningModel = new FineTuningModel();
      fineTuningModel.id = dto.id;
      fineTuningModel.name = dto.name || dto.id;
      fineTuningModel.description = dto.description || '';
      fineTuningModel.token = 0;
      fineTuningModel.status = 'completed';
      fineTuningModel.isManual = true;
      // Không đặt giá trị cho detailId để sử dụng giá trị mặc định null

      await queryRunner.manager.save(fineTuningModel);

      // Tạo bản ghi trong bảng admin_fine_tuning_models
      const adminFineTuningModel = new AdminFineTuningModel();
      adminFineTuningModel.id = dto.id;
      adminFineTuningModel.createdBy = employeeId;
      adminFineTuningModel.modelBaseId = dto.modelBaseId;
      adminFineTuningModel.isPrivate = dto.isPrivate !== undefined ? dto.isPrivate : true;

      await queryRunner.manager.save(adminFineTuningModel);

      await queryRunner.commitTransaction();
      return adminFineTuningModel;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi tạo mô hình fine-tuning admin: ${error.message}`, error.stack);

      // Phân loại lỗi để trả về thông báo cụ thể
      if (error.message.includes('invalid input syntax for type uuid')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
          'Lỗi định dạng UUID trong dữ liệu mô hình'
        );
      } else if (error.message.includes('duplicate key value violates unique constraint')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_ALREADY_EXISTS,
          `Mô hình với ID ${dto.id} đã tồn tại`
        );
      } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
          'Lỗi cấu hình cơ sở dữ liệu: Bảng không tồn tại'
        );
      } else if (error instanceof AppException) {
        throw error;
      } else {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED,
          `Tạo mô hình fine-tuning thất bại: ${error.message}`
        );
      }
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Xóa mô hình fine-tuning của admin
   * @param id ID của mô hình fine-tuning
   * @param employeeId ID của nhân viên xóa mô hình
   * @returns true nếu xóa thành công, false nếu xóa thất bại
   */
  async deleteAdminFineTuningModel(id: string, employeeId: number): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Cập nhật bản ghi trong bảng admin_fine_tuning_models
      await queryRunner.manager.update(
        AdminFineTuningModel,
        { id },
        { deletedBy: employeeId },
      );

      // Cập nhật bản ghi trong bảng fine_tuning_models
      const now = Date.now();
      await queryRunner.manager.update(
        FineTuningModel,
        { id },
        { deletedAt: now },
      );

      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi xóa mô hình fine-tuning admin: ${error.message}`, error.stack);
      return false;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các mô hình fine-tuning
   */
  async findAllPublicPaginated(query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      const page = query.page ?? 1;
      const limit = query.limit ?? 10;
      const skip = (page - 1) * limit;

      // Truy vấn mô hình của admin
      const adminQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          '"ftm"."id"',
          '"ftm"."name"',
          '"ftm"."description"',
          '"ftm"."token"',
          '"ftm"."status"',
          '"ftm"."created_at" AS "createdAt"',
          '"ftm"."updated_at" AS "updatedAt"',
          '"bm"."name" AS "baseModelName"',
          '"e"."full_name" AS "author"',
          '"e"."avatar" AS "avatar"',
          '"admin" AS "ownerType"',
        ])
        .from('fine_tuning_models', 'ftm')
        .innerJoin('admin_fine_tuning_models', 'afm', '"afm"."id" = "ftm"."id"')
        .leftJoin('base_models', 'bm', '"bm"."id" = "afm"."model_base_id"')
        .leftJoin('employees', 'e', '"e"."id" = "afm"."created_by"')
        .where('"ftm"."deleted_at" IS NULL')
        .andWhere('"afm"."is_private" = false');

      // Truy vấn mô hình của user
      const userQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          '"ftm"."id"',
          '"ftm"."name"',
          '"ftm"."description"',
          '"ftm"."token"',
          '"ftm"."status"',
          '"ftm"."created_at" AS "createdAt"',
          '"ftm"."updated_at" AS "updatedAt"',
          '"bm"."name" AS "baseModelName"',
          '"u"."full_name" AS "author"',
          '"u"."avatar" AS "avatar"',
          '"user" AS "ownerType"',
        ])
        .from('fine_tuning_models', 'ftm')
        .innerJoin('user_fine_tuning_models', 'ufm', '"ufm"."id" = "ftm"."id"')
        .leftJoin('base_models', 'bm', '"bm"."id" = "ufm"."model_base_id"')
        .leftJoin('users', 'u', '"u"."id" = "ufm"."user_id"')
        .where('"ftm"."deleted_at" IS NULL');

      // Kết hợp hai truy vấn
      const combinedQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select('*')
        .from(`(${adminQueryBuilder.getQuery()} UNION ALL ${userQueryBuilder.getQuery()})`, 'combined');

      // Áp dụng các điều kiện lọc
      if (query.search) {
        combinedQueryBuilder.andWhere(
          '(combined."name" ILIKE :search OR combined."description" ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      // Sắp xếp
      combinedQueryBuilder.orderBy(`combined."${query.sortBy || 'createdAt'}"`, query.sortDirection || 'DESC');

      // Đếm tổng số bản ghi
      const countQuery = `SELECT COUNT(*) FROM (${combinedQueryBuilder.getQuery()}) as count_query`;
      const parameters = combinedQueryBuilder.getParameters() as any[];
      const countResult = await this.dataSource.query(countQuery, parameters);
      const totalItems = parseInt(countResult[0].count, 10);

      // Phân trang
      combinedQueryBuilder.limit(limit).offset(skip);

      // Thực hiện truy vấn
      const queryParameters = combinedQueryBuilder.getParameters() as any[];
      const items = await this.dataSource.query(
        combinedQueryBuilder.getQuery(),
        queryParameters,
      );

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning của user với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các mô hình fine-tuning của user
   */
  async findAllUserModelsPaginated(query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      const page = query.page ?? 1;
      const limit = query.limit ?? 10;
      const skip = (page - 1) * limit;

      // Truy vấn mô hình của user
      const userQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          '"ftm"."id"',
          '"ftm"."name"',
          '"ftm"."description"',
          '"ftm"."token"',
          '"ftm"."status"',
          '"ftm"."created_at" AS "createdAt"',
          '"ftm"."updated_at" AS "updatedAt"',
          '"u"."full_name" AS "author"',
          '"u"."avatar" AS "avatar"',
        ])
        .from('fine_tuning_models', 'ftm')
        .innerJoin('user_fine_tuning_models', 'ufm', '"ufm"."id" = "ftm"."id"')
        .leftJoin('base_models', 'bm', '"bm"."id" = "ufm"."model_base_id"')
        .leftJoin('users', 'u', '"u"."id" = "ufm"."user_id"')
        .where('"ftm"."deleted_at" IS NULL');

      // Áp dụng các điều kiện lọc
      if (query.search) {
        userQueryBuilder.andWhere(
          '("ftm"."name" ILIKE :search OR "ftm"."description" ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      if (query.providerId) {
        userQueryBuilder.andWhere('"ufm"."provider_id" = :providerId', { providerId: query.providerId });
      }

      // Sắp xếp
      const sortFieldMap: Record<string, string> = {
        'createdAt': '"ftm"."created_at"',
        'updatedAt': '"ftm"."updated_at"',
        'name': '"ftm"."name"',
        'status': '"ftm"."status"',
        'token': '"ftm"."token"'
      };

      const sqlSortField = sortFieldMap[query.sortBy || 'createdAt'] || '"ftm"."created_at"';
      userQueryBuilder.orderBy(sqlSortField, query.sortDirection || 'DESC');

      // Đếm tổng số bản ghi
      const totalItems = await userQueryBuilder.getCount();

      // Phân trang
      userQueryBuilder.limit(limit).offset(skip);

      // Thực hiện truy vấn
      const items = await userQueryBuilder.getRawMany();

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning của user: ${error.message}`, error.stack);
      throw error;
    }
  }
}