import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DataFineTuningModel } from '../entities/data-fine-tuning-model.entity';

@Injectable()
export class DataFineTuningModelRepository extends Repository<DataFineTuningModel> {
  constructor(private dataSource: DataSource) {
    super(DataFineTuningModel, dataSource.createEntityManager());
  }

  /**
   * Cập nhật thông tin phương pháp fine-tuning
   */
  async updateMethod(id: string, method: any): Promise<void> {
    const query = `
      UPDATE data_fine_tuning_models
      SET method = $2
      WHERE id = $1
    `;
    
    await this.query(query, [id, JSON.stringify(method)]);
  }

  /**
   * Cập nhật thông tin metadata
   */
  async updateMetadata(id: string, metadata: any): Promise<void> {
    const query = `
      UPDATE data_fine_tuning_models
      SET metadata = $2
      WHERE id = $1
    `;
    
    await this.query(query, [id, JSON.stringify(metadata)]);
  }

  /**
   * Cập nhật thông tin file dữ liệu
   */
  async updateFiles(id: string, trainFile: string, validationFile: string): Promise<void> {
    const query = `
      UPDATE data_fine_tuning_models
      SET train_file = $2, validation_file = $3
      WHERE id = $1
    `;
    
    await this.query(query, [id, trainFile, validationFile]);
  }
}
