import { ApiProperty } from '@nestjs/swagger';
import { DataFineTuningModel } from '../entities/data-fine-tuning-model.entity';

export class DataFineTuningModelSchema {
  @ApiProperty({
    description: 'UUID định danh duy nhất cho dữ liệu chi tiết của mô hình fine-tuning',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Đường dẫn hoặc ID file dữ liệu huấn luyện',
    example: 'training_data_123.jsonl',
    nullable: true,
  })
  trainFile: string;

  @ApiProperty({
    description: 'Đường dẫn hoặc ID file dữ liệu kiểm thử',
    example: 'validation_data_123.jsonl',
    nullable: true,
  })
  validationFile: string;

  @ApiProperty({
    description: 'Phương pháp fine-tuning được sử dụng',
    example: {
      type: 'supervised',
      epochs: 3,
      batch_size: 4,
      learning_rate_multiplier: 0.1
    },
    nullable: true,
  })
  method: any;

  @ApiProperty({
    description: 'Thông tin bổ sung về quá trình fine-tuning',
    example: {
      training_loss: 0.05,
      validation_loss: 0.08,
      training_token_count: 50000,
      validation_token_count: 10000
    },
    nullable: true,
  })
  metadata: any;

  constructor(partial: Partial<DataFineTuningModel>) {
    Object.assign(this, partial);
  }
}

export class DataFineTuningModelListResponseSchema {
  @ApiProperty({
    description: 'Danh sách dữ liệu chi tiết của mô hình fine-tuning',
    type: [DataFineTuningModelSchema],
  })
  items: DataFineTuningModelSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số dữ liệu chi tiết',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số dữ liệu chi tiết trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số dữ liệu chi tiết trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
