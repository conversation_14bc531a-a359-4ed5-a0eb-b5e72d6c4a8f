import { FineTuningModel } from '@modules/model-training/entities';
import { BaseModel } from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';

@Injectable()
export class FineTuningModelValidationHelper {
  /**
   * Validates that a base model exists
   * @param baseModel The base model to validate
   * @throws AppException if the base model does not exist
   */
  validateBaseModelExists(baseModel: BaseModel | null): void {
    if (!baseModel) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
        'Base model not found',
      );
    }
  }

  /**
   * Validates that a fine-tuning model exists
   * @param fineTuningModel The fine-tuning model to validate
   * @throws AppException if the fine-tuning model does not exist
   */
  validateFineTuningModelExists(fineTuningModel: FineTuningModel | null): void {
    if (!fineTuningModel) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
        'Fine-tuning model not found',
      );
    }
  }
}