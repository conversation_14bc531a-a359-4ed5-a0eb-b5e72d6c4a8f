import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { FineTuningModelUserService } from '../services/fine-tuning-model-user.service';
import { CreateUserFineTuningModelDto } from '@/modules/model-training/dto/fine-tuning-model/create-user-fine-tuning-model.dto';
import { FineTuningModelQueryDto } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model-query.dto';
import { SWAGGER_API_TAGS } from '@/modules/model-training/constants/swagger-api-tags.constant';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { MODEL_TRAINING_ERROR_CODES } from '@/modules/model-training/exceptions/model-training.exception';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { UserFineTuningModelDetailRes, UserFineTuningModelPaginatedRes } from '@/modules/model-training/dto/fine-tuning-model/user-fine-tuning-model.dto';

/**
 * Controller xử lý các API liên quan đến quản lý mô hình fine-tuning cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_FINE_TUNING_MODEL)
@ApiExtraModels(
  ApiResponseDto,
  CreateUserFineTuningModelDto,
  UserFineTuningModelDetailRes,
  UserFineTuningModelPaginatedRes,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/fine-tuning-models')
export class FineTuningModelUserController {
  constructor(private readonly fineTuningModelUserService: FineTuningModelUserService) {}

  /**
   * Tạo mới mô hình fine-tuning
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới mô hình fine-tuning' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED)
  async create(
    @Body() createUserFineTuningModelDto: CreateUserFineTuningModelDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.fineTuningModelUserService.create(createUserFineTuningModelDto, userId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách mô hình fine-tuning của người dùng với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách mô hình fine-tuning của người dùng với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findAll(@Query() query: FineTuningModelQueryDto, @CurrentUser('id') userId: number) {
    const result = await this.fineTuningModelUserService.findAllByUserId(userId, query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết mô hình fine-tuning theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết mô hình fine-tuning theo ID' })
  @ApiParam({ name: 'id', description: 'ID của mô hình fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findById(@Param('id') id: string, @CurrentUser('id') userId: number) {
    const result = await this.fineTuningModelUserService.findById(id, userId);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mô hình fine-tuning
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa mô hình fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của mô hình fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async delete(@Param('id') id: string, @CurrentUser('id') userId: number) {
    await this.fineTuningModelUserService.delete(id, userId);
    return ApiResponseDto.success(true);
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang
   */
  @Get('public/all')
  @ApiOperation({ summary: 'Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tất cả mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findAllPublic(@Query() query: FineTuningModelQueryDto, @CurrentUser('id') userId: number) {
    const result = await this.fineTuningModelUserService.findAllPublic(userId, query);
    return ApiResponseDto.paginated(result);
  }
}
