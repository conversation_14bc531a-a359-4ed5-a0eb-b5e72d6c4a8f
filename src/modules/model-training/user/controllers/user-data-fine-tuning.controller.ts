import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { QueryDto } from '@/common/dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { CreateDataFineTuningReq } from '@modules/model-training/dto/data-fine-tuning/create-data-fine-tuning.dto';
import {
  DeleteManyDto,
  UpdateDataFineTuningDto
} from '@modules/model-training/dto/data-fine-tuning/data-fine-tuning.dto';
import { UserDataFineTuning } from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import { UserDataFineTuningResult, UserDataFineTuningDetailResult } from '@modules/model-training/repositories';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserDataFineTuningService } from '../services/user-data-fine-tuning.service';

/**
 * Controller xử lý các API liên quan đến liên kết dữ liệu fine-tuning với người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_FINETUNING_DATA)
@Controller('data-fine-tuning')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserDataFineTuningController {
  constructor(
    private readonly userDataFineTuningService: UserDataFineTuningService,
  ) { }

  /**
   * Tạo mới một bộ dữ liệu fine-tuning
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới một bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_FAILED)
  async createDataFineTuning(
    @Body() createDto: CreateDataFineTuningReq,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<string>> {
    const result = await this.userDataFineTuningService.createDataFineTuning(
      createDto,
      user.id,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật thông tin bộ dữ liệu fine-tuning
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin bộ dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.INVALID_STATUS)
  async updateDataFineTuning(
    @Param('id') id: string,
    @Body() updateDto: UpdateDataFineTuningDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.userDataFineTuningService.updateDataFineTuning(
      id,
      updateDto,
      user.id,
    );
    return ApiResponseDto.success(null);
  }

  /**
   * Xóa một bộ dữ liệu fine-tuning
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa một bộ dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async deleteDataFineTuning(
    @Param('id') id: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.userDataFineTuningService.deleteDataFineTuning(id, user.id);
    return ApiResponseDto.success(null);
  }

  /**
   * Xóa nhiều bộ dữ liệu fine-tuning
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async deleteMultipleDataFineTuning(
    @Body() deleteDto: DeleteManyDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.userDataFineTuningService.deleteMultipleDataFineTuning(deleteDto, user.id);
    return ApiResponseDto.success(null);
  }

  /**
   * Lấy danh sách liên kết dữ liệu fine-tuning của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách liên kết dữ liệu fine-tuning của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getUserDataFineTuningList(
    @Query() query: QueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserDataFineTuningResult>>> {
    const result = await this.userDataFineTuningService.getUserDataFineTuningList(
      user.id,
      query,
    );
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết liên kết dữ liệu fine-tuning
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết liên kết dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getUserDataFineTuningDetail(
    @Param('id') id: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserDataFineTuningDetailResult>> {
    const result = await this.userDataFineTuningService.getUserDataFineTuningDetail(
      id,
      user.id,
    );
    return ApiResponseDto.success(result);
  }
}
