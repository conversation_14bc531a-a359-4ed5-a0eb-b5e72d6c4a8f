import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';

/**
 * DTO cho việc tạo mới provider model của user
 */
export class CreateUserProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  @ApiProperty({
    description: 'Tên định danh cho provider model',
    example: 'My OpenAI Provider',
  })
  @IsNotEmpty({ message: 'Tên không được để trống' })
  @IsString({ message: 'Tên phải là chuỗi' })
  @MaxLength(255, { message: 'Tên không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Loại nhà cung cấp
   */
  @ApiProperty({
    description: '<PERSON>ại nhà cung cấp',
    enum: TypeProviderEnum,
    example: TypeProviderEnum.OPENAI,
  })
  @IsNotEmpty({ message: 'Loại nhà cung cấp không được để trống' })
  @IsEnum(TypeProviderEnum, { message: 'Loại nhà cung cấp không hợp lệ' })
  type: TypeProviderEnum;

  /**
   * API key của nhà cung cấp
   */
  @ApiProperty({
    description: 'API key của nhà cung cấp',
    example: 'sk-abcdefghijklmnopqrstuvwxyz123456',
  })
  @IsNotEmpty({ message: 'API key không được để trống' })
  @IsString({ message: 'API key phải là chuỗi' })
  apiKey: string;
}
