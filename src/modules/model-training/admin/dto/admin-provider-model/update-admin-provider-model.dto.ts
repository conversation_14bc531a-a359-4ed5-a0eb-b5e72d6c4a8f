import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật provider model của admin
 */
export class UpdateAdminProviderModelDto {
  /**
   * Tên nhà cung cấp
   */
  @ApiProperty({
    description: 'Tên nhà cung cấp',
    example: 'OpenAI Provider Updated',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên nhà cung cấp phải là chuỗi' })
  @MaxLength(255, { message: 'Tên nhà cung cấp không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * API key của nhà cung cấp
   */
  @ApiProperty({
    description: 'API key của nhà cung cấp',
    example: 'sk-abcdefghijklmnopqrstuvwxyz123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'API key phải là chuỗi' })
  apiKey?: string;
}
