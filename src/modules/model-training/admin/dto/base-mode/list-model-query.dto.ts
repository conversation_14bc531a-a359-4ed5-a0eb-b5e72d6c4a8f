import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNumber, IsOptional, IsString } from "class-validator";

export class ListModelQueryDto {
  @ApiProperty({
    description: 'ID của nhà cung cấp (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @IsString()
  providerId: string;

  @ApiProperty({
    description: 'Số lượng model muốn lấy',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;
}
