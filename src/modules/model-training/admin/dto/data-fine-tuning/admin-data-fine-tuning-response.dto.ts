import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { DataFineTunningStatusEnum } from '@modules/model-training/constants/data-fine-tunning.enum';
import { TrainingDataset } from '@modules/model-training/interfaces';

/**
 * DTO chứa thông tin nhân viên
 */
export class EmployeeInfoDto {
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên nhân viên',
    example: 'John <PERSON>'
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Avatar của nhân viên',
    example: 'https://example.com/avatar.jpg'
  })
  @Expose()
  avatar: string;
}

/**
 * DTO chứa thông tin chi tiết dữ liệu fine-tuning cho admin
 */
export class AdminDataFineTuningResponseDto {
  @ApiProperty({
    description: 'ID của bộ dữ liệu fine-tuning',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên của bộ dữ liệu fine-tuning',
    example: 'Training Dataset 1'
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về bộ dữ liệu fine-tuning',
    example: 'Dataset for training model X'
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Dữ liệu huấn luyện dạng JSONB',
    type: 'array'
  })
  @Expose()
  trainData: TrainingDataset;

  @ApiProperty({
    description: 'Dữ liệu kiểm thử dạng JSONB',
    type: 'array',
    nullable: true
  })
  @Expose()
  validationData: TrainingDataset | null;

  @ApiProperty({
    description: 'Cờ đánh dấu bộ dữ liệu có được rao bán hay không',
    example: false
  })
  @Expose()
  isForSale: boolean;

  @ApiProperty({
    description: 'Trạng thái của bộ dữ liệu fine-tuning',
    enum: DataFineTunningStatusEnum,
    example: DataFineTunningStatusEnum.DRAFT
  })
  @Expose()
  status: DataFineTunningStatusEnum;

  @ApiProperty({
    description: 'Thời điểm tạo bộ dữ liệu (timestamp millis)',
    example: 1672531200000
  })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.getTime() : value)
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis)',
    example: 1672531200000
  })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.getTime() : value)
  updatedAt: number;

  @ApiProperty({
    description: 'Thời điểm xóa bộ dữ liệu (timestamp millis)',
    example: 1672531200000,
    nullable: true
  })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.getTime() : value)
  deletedAt: number | null;

  @ApiProperty({
    description: 'Thông tin nhân viên tạo bộ dữ liệu',
    type: EmployeeInfoDto
  })
  @Expose()
  createdByEmployee: EmployeeInfoDto;

  @ApiProperty({
    description: 'Thông tin nhân viên cập nhật bộ dữ liệu',
    type: EmployeeInfoDto,
    nullable: true
  })
  @Expose()
  updatedByEmployee: EmployeeInfoDto | null;

  @ApiProperty({
    description: 'Thông tin nhân viên xóa bộ dữ liệu',
    type: EmployeeInfoDto,
    nullable: true
  })
  @Expose()
  deletedByEmployee: EmployeeInfoDto | null;
} 