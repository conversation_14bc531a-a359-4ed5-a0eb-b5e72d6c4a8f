import { QueryDto } from '@common/dto';
import { AppException } from '@common/exceptions';
import { generateS3Key } from '@common/helpers';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Transactional } from 'typeorm-transactional';
import { CategoryFolderEnum } from '../../constants/category-folder.enum';
import { BaseModelStatusEnum } from '../../constants/base-model-status.enum';
import { AdminProviderModel } from '../../entities/admin-provider-model.entity';
import { MODEL_TRAINING_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { BaseModelRepository } from '../../repositories';
import { AdminProviderModelRepository } from '../../repositories';
import { AdminProviderModelBaseResponseDto, AdminProviderModelDetailResponseDto, CreateAdminProviderModelDto, UpdateAdminProviderModelDto } from '../dto/admin-provider-model';
import { EmployeeInfoService } from '@/modules/employee/services';

/**
 * Service xử lý logic cho Admin Provider Model
 */
@Injectable()
export class AdminProviderModelService {
  private readonly logger = new Logger(AdminProviderModelService.name);

  constructor(
    private readonly adminProviderModelRepository: AdminProviderModelRepository,
    private readonly baseModelRepository: BaseModelRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
  ) {
  }

  /**
   * Tạo mới provider model
   * @param dto DTO tạo mới
   * @param employeeId ID của employee thực hiện
   * @returns Thông báo thành công
   */
  @Transactional()
  async create(dto: CreateAdminProviderModelDto, employeeId: number):
  Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra tên đã tồn tại chưa
      const isNameExists = await this.adminProviderModelRepository.isNameExists(dto.name);
      if (isNameExists) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NAME_EXISTS);
      }

      // Mã hóa API key
      let encryptedApiKey: string;
      try {
        encryptedApiKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(dto.apiKey);
      } catch (error) {
        this.logger.error(`Lỗi mã hóa API key: ${error.message}`);
        throw new AppException(MODEL_TRAINING_ERROR_CODES.ENCRYPTION_ERROR);
      }

      // Tạo provider model mới
      const providerModel = this.adminProviderModelRepository.create({
        name: dto.name,
        type: dto.type,
        apiKey: encryptedApiKey,
        createdBy: employeeId,
        updatedBy: employeeId,
      });

      await this.adminProviderModelRepository.save(providerModel);

      return ApiResponseDto.success({
        message: 'Tạo nhà cung cấp thành công'
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi tạo provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_CREATE_FAILED);
    }
  }

  /**
   * Lấy danh sách provider model có phân trang
   * @param queryDto Query parameters
   * @returns Danh sách provider model có phân trang
   */
  async findAll(queryDto: QueryDto): Promise<ApiResponseDto<PaginatedResult<AdminProviderModelBaseResponseDto>>> {
    try {
      const result = await this.adminProviderModelRepository.findPaginated(queryDto);

      const items = result.items.map(item => this.mapToBaseResponseDto(item));

      return ApiResponseDto.paginated({
        items,
        meta: result.meta
      });
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR);
    }
  }

  /**
   * Lấy chi tiết provider model
   * @param id ID của provider model (UUID)
   * @returns Chi tiết provider model
   */
  async findOne(id: string): Promise<ApiResponseDto<AdminProviderModelDetailResponseDto>> {
    try {
      const providerModel = await this.adminProviderModelRepository.findDetailById(id);
      if (!providerModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND);
      }

      const detailDto = await this.mapToDetailResponseDto(providerModel);

      return ApiResponseDto.success(detailDto);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi lấy chi tiết provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR);
    }
  }

  /**
   * Cập nhật provider model
   * @param id ID của provider model (UUID)
   * @param dto DTO cập nhật
   * @param employeeId ID của employee thực hiện
   * @returns Thông báo thành công
   */
  @Transactional()
  async update(id: string, dto: UpdateAdminProviderModelDto, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra provider model tồn tại
      const providerModel = await this.adminProviderModelRepository.findDetailById(id);
      if (!providerModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND);
      }

      // Kiểm tra tên đã tồn tại chưa (nếu có cập nhật tên)
      if (dto.name && dto.name !== providerModel.name) {
        const isNameExists = await this.adminProviderModelRepository.isNameExists(dto.name, id);
        if (isNameExists) {
          throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NAME_EXISTS);
        }
      }

      // Cập nhật thông tin
      const updateData: Partial<AdminProviderModel> = {
        updatedBy: employeeId,
      };

      if (dto.name) {
        updateData.name = dto.name;
      }

      if (dto.apiKey) {
        try {
          updateData.apiKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(dto.apiKey);
        } catch (error) {
          this.logger.error(`Lỗi mã hóa API key: ${error.message}`);
          throw new AppException(MODEL_TRAINING_ERROR_CODES.ENCRYPTION_ERROR);
        }
      }

      await this.adminProviderModelRepository.updateProvider(id, updateData);

      return ApiResponseDto.success({ message: 'Cập nhật nhà cung cấp thành công' });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi cập nhật provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_UPDATE_FAILED);
    }
  }

  /**
   * Xóa provider model
   * @param id ID của provider model (UUID)
   * @param employeeId ID của employee thực hiện
   * @returns Thông báo thành công
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
    try {
      // Kiểm tra provider model tồn tại
      const exists = await this.adminProviderModelRepository.isExists(id);
      if (!exists) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND);
      }

      // Xóa các base model liên quan
      await this.baseModelRepository.createQueryBuilder()
        .update()
        .set({
          updatedAt: () => '(EXTRACT(EPOCH FROM now()) * 1000)::bigint',
          updatedBy: employeeId,
          status: BaseModelStatusEnum.DELETED
        })
        .where('provider_id = :providerId', { providerId: id })
        .andWhere('status != :status', { status: BaseModelStatusEnum.DELETED })
        .execute();

      // Xóa provider model
      const deleted = await this.adminProviderModelRepository.softDeleteProvider(id, employeeId);
      if (!deleted) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_DELETE_FAILED);
      }

      return ApiResponseDto.success({ message: 'Xóa nhà cung cấp thành công' });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi xóa provider model: ${error.message}`);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.PROVIDER_DELETE_FAILED);
    }
  }

  /**
   * Map từ entity sang base response DTO
   * @param entity Entity
   * @returns Base response DTO
   */
  private mapToBaseResponseDto(entity: AdminProviderModel): AdminProviderModelBaseResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      type: entity.type,
      createdAt: entity.createdAt,
    };
  }

  /**
   * Map từ entity sang detail response DTO
   * @param entity Entity
   * @returns Detail response DTO
   */
  private async mapToDetailResponseDto(entity: AdminProviderModel): Promise<AdminProviderModelDetailResponseDto> {
    const baseDto = this.mapToBaseResponseDto(entity);

    // Tạo đối tượng mặc định cho createdBy
    const defaultCreatedBy = {
      id: entity.createdBy || 0,
      name: 'Unknown',
      avatar: null,
    };

    // Tạo đối tượng mặc định cho updatedBy
    const defaultUpdatedBy = entity.updatedBy ? {
      id: entity.updatedBy,
      name: 'Unknown',
      avatar: null,
    } : undefined;

    // Lấy thông tin người tạo và người cập nhật
    let createdBy = defaultCreatedBy;
    let updatedBy = defaultUpdatedBy;

    try {
      // Lấy thông tin người tạo nếu có
      if (entity.createdBy) {
        const creator = await this.employeeInfoService.getEmployeeInfo(entity.createdBy);
        createdBy = {
          id: creator.employeeId,
          name: creator.name,
          avatar: null,
        };
      }

      // Lấy thông tin người cập nhật nếu có
      if (entity.updatedBy) {
        const updater = await this.employeeInfoService.getEmployeeInfo(entity.updatedBy);
        if (updatedBy) {
          updatedBy = {
            id: updater.employeeId,
            name: updater.name,
            avatar: null,
          };
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi lấy thông tin người tạo/cập nhật: ${error.message}`);
    }

    return {
      ...baseDto,
      createdBy,
      updatedAt: entity.updatedAt,
      updatedBy,
    };
  }
}
