import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import {
  DeletedBaseModelRes,
} from '@/modules/model-training/dto/base-model/base-model.dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import {
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { BaseModelDeletedQueryDto } from '../../dto/base-model/base-model-deleted-query.dto';
import { BaseModelAdminService } from '../services/base-model-admin.service';

/**
 * Controller xử lý các API liên quan đến base model đã xóa
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BASE_MODEL_TRASH)
@ApiExtraModels(ApiResponseDto, PaginatedResult, DeletedBaseModelRes)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/base-models/trash')
export class BaseModelTrashAdminController {
  constructor(
    private readonly baseModelAdminService: BaseModelAdminService,
  ) { }

  /**
   * Lấy danh sách base model đã xóa
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách base model đã xóa',
    description: 'Lấy danh sách base model đã xóa với phân trang và tìm kiếm',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách base model đã xóa',
    schema: ApiResponseDto.getPaginatedSchema(DeletedBaseModelRes),
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getDeletedBaseModels(
    @Query() query: BaseModelDeletedQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<DeletedBaseModelRes>>> {
    const result = await this.baseModelAdminService.findAllDeleted(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Khôi phục base model đã xóa
   */
  @Post(':id/restore')
  @ApiOperation({
    summary: 'Khôi phục base model đã xóa',
    description: 'Khôi phục base model đã xóa về trạng thái DRAFT',
  })
  @ApiParam({ name: 'id', description: 'ID của base model cần khôi phục' })
  @ApiResponse({
    status: 200,
    description: 'Base model đã được khôi phục thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
    MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
  )
  async restoreBaseModel(
    @Param('id') id: string,
    @CurrentEmployee() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.baseModelAdminService.restoreBaseModel(id, user.id);
    return ApiResponseDto.success(null, 'Base model đã được khôi phục thành công');
  }
}
