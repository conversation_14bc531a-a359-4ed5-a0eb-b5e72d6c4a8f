import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { QueryDto } from '@/common/dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { AdminDataFineTuningResponseDto } from '@modules/model-training/admin/dto/data-fine-tuning/admin-data-fine-tuning-response.dto';
import { CreateDataFineTuningReq } from '@modules/model-training/dto/data-fine-tuning/create-data-fine-tuning.dto';
import {
    DeleteManyDto,
    UpdateDataFineTuningDto,
} from '@modules/model-training/dto/data-fine-tuning/data-fine-tuning.dto';
import { DataFineTuning } from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Query,
    UseGuards,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiOperation,
    ApiParam,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { AdminDataFineTuningService } from '../services/admin-data-fine-tuning.service';

/**
 * Controller xử lý các API liên quan đến quản lý dữ liệu fine-tuning cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_FINETUNING_DATA)
@Controller('admin/data-fine-tuning')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminDataFineTuningController {
  constructor(
    private readonly adminDataFineTuningService: AdminDataFineTuningService,
  ) {}

  /**
   * Tạo mới một bộ dữ liệu fine-tuning
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới một bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_FAILED)
  async createDataFineTuning(
    @Body() createDto: CreateDataFineTuningReq,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<string>> {
    const result = await this.adminDataFineTuningService.createDataFineTuning(
      createDto,
      employee.id,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật thông tin bộ dữ liệu fine-tuning
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin bộ dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.INVALID_STATUS)
  async updateDataFineTuning(
    @Param('id') id: string,
    @Body() updateDto: UpdateDataFineTuningDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.adminDataFineTuningService.updateDataFineTuning(
      id,
      updateDto,
      employee.id,
    );
    return ApiResponseDto.success(null);
  }

  /**
   * Xóa một bộ dữ liệu fine-tuning
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa một bộ dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async deleteDataFineTuning(
    @Param('id') id: string,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.adminDataFineTuningService.deleteDataFineTuning(id, employee.id);
    return ApiResponseDto.success(null);
  }

  /**
   * Xóa nhiều bộ dữ liệu fine-tuning
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều bộ dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async deleteMultipleDataFineTuning(
    @Body() deleteDto: DeleteManyDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.adminDataFineTuningService.deleteMultipleDataFineTuning(
      deleteDto,
      employee.id,
    );
    return ApiResponseDto.success(null);
  }

  /**
   * Lấy danh sách dữ liệu fine-tuning
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getDataFineTuningList(
    @Query() query: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<DataFineTuning>>> {
    const result =
      await this.adminDataFineTuningService.getDataFineTuningList(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết dữ liệu fine-tuning
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết dữ liệu fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của dữ liệu fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết thành công',
    schema: ApiResponseDto.getSchema(AdminDataFineTuningResponseDto),
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getDataFineTuningDetail(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<AdminDataFineTuningResponseDto>> {
    const result =
      await this.adminDataFineTuningService.getDataFineTuningDetail(id);
    return ApiResponseDto.success(result);
  }
}
