import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@/modules/auth/guards/jwt-employee.guard';
import { SWAGGER_API_TAGS } from '@/modules/model-training/constants/swagger-api-tags.constant';
import { CreateAdminFineTuningModelDto } from '@/modules/model-training/dto/fine-tuning-model/create-admin-fine-tuning-model.dto';
import { FineTuningModelQueryDto } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model-query.dto';
import { AdminFineTuningModelDetailRes, FineTuningModelPaginatedRes } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model.dto';
import { MODEL_TRAINING_ERROR_CODES } from '@/modules/model-training/exceptions/model-training.exception';
import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FineTuningModelAdminService } from '../services/fine-tuning-model-admin.service';

/**
 * Controller xử lý các API liên quan đến quản lý mô hình fine-tuning cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_FINE_TUNING_MODEL)
@ApiExtraModels(
  ApiResponseDto,
  CreateAdminFineTuningModelDto,
  AdminFineTuningModelDetailRes,
  FineTuningModelPaginatedRes,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/fine-tuning-models')
export class FineTuningModelAdminController {
  constructor(private readonly fineTuningModelAdminService: FineTuningModelAdminService) { }

  /**
   * Tạo mới mô hình fine-tuning
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới mô hình fine-tuning' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED)
  async create(
    @Body() createAdminFineTuningModelDto: CreateAdminFineTuningModelDto,
    @CurrentEmployee('id') employeeId: number,
  ) {
    const result = await this.fineTuningModelAdminService.create(createAdminFineTuningModelDto, employeeId);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách mô hình fine-tuning với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách mô hình fine-tuning với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findAll(@Query() query: FineTuningModelQueryDto) {
    const result = await this.fineTuningModelAdminService.findAll(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết mô hình fine-tuning theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết mô hình fine-tuning theo ID' })
  @ApiParam({ name: 'id', description: 'ID của mô hình fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findById(@Param('id') id: string) {
    const result = await this.fineTuningModelAdminService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mô hình fine-tuning
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa mô hình fine-tuning' })
  @ApiParam({ name: 'id', description: 'ID của mô hình fine-tuning' })
  @ApiResponse({
    status: 200,
    description: 'Xóa mô hình fine-tuning thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND)
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED)
  async delete(@Param('id') id: string, @CurrentEmployee('id') employeeId: number) {
    await this.fineTuningModelAdminService.delete(id, employeeId);
    return ApiResponseDto.success(true);
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning của user với phân trang
   */
  @Get('public/all')
  @ApiOperation({ summary: 'Lấy danh sách tất cả mô hình fine-tuning của user với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tất cả mô hình fine-tuning của user thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async findAllPublic(@Query() query: FineTuningModelQueryDto) {
    const result = await this.fineTuningModelAdminService.findAllUserModels(query);
    return ApiResponseDto.paginated(result);
  }
}