import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { QueryDto } from '@common/dto';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateAdminProviderModelDto,
  UpdateAdminProviderModelDto
} from '../dto/admin-provider-model';
import { AdminProviderModelService } from '../services/admin-provider-model.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller xử lý API cho Admin Provider Model
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_PROVIDER_MODEL)
@Controller('admin/provider-model')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminProviderModelController {
  constructor(private readonly adminProviderModelService: AdminProviderModelService) { }

  /**
   * Tạo mới provider model
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới provider model' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới provider model thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createAdminProviderModelDto: CreateAdminProviderModelDto,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminProviderModelService.create(createAdminProviderModelDto, employeeId);
  }

  /**
   * Lấy danh sách provider model có phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách provider model có phân trang và tìm kiếm',
    description: 'API này hỗ trợ tìm kiếm theo tên nhà cung cấp, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách provider model',
    type: ApiResponseDto
  })
  findAll(@Query() queryDto: QueryDto) {
    return this.adminProviderModelService.findAll(queryDto);
  }

  /**
   * Lấy chi tiết provider model
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết provider model' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết provider model',
    type: ApiResponseDto
  })
  findOne(@Param('id') id: string) {
    return this.adminProviderModelService.findOne(id);
  }

  /**
   * Cập nhật provider model
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật provider model' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật provider model thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id') id: string,
    @Body() updateAdminProviderModelDto: UpdateAdminProviderModelDto,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminProviderModelService.update(id, updateAdminProviderModelDto, employeeId);
  }

  /**
   * Xóa provider model
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa provider model' })
  @ApiResponse({
    status: 200,
    description: 'Xóa provider model thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminProviderModelService.remove(id, employeeId);
  }
}
