import { Column, Entity, PrimaryColumn, Unique } from 'typeorm';
import { BaseModelStatusEnum } from '@/modules/model-training/constants/base-model-status.enum';
import { ConfigModelBase } from '@modules/model-training/interfaces/config-model-base.interface';
import { Min } from 'class-validator';

/**
 * Entity đại diện cho bảng base_models trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các mô hình nền tảng được hỗ trợ trong hệ thống
 */
@Entity('base_models')
@Unique('base_models_pk_2', ['providerId', 'modelId'])
export class BaseModel {
  /**
   * ID định danh duy nhất cho mô hình, thường là mã của nhà cung cấp
   */
  @PrimaryColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên hiển thị của mô hình nền tảng
   */
  @Column({ name: 'model_id', type: 'varchar', length: 100 })
  modelId: string;

  /**
   * Mô tả chi tiết về mô hình và khả năng của nó
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * ID nhà cung cấp mô hình (UUID), tham chiếu đến bảng providers
   */
  @Column({ name: 'provider_id', nullable: true, type: 'uuid' })
  providerId: string;

  /**
   * Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản
   */
  @Column({ name: 'base_input_rate', default: 1 })
  @Min(1)
  baseInputRate: number;

  /**
   * Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản
   */
  @Column({ name: 'base_output_rate', default: 1 })
  @Min(1)
  baseOutputRate: number;

  /**
   * Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản
   */
  @Column({ name: 'base_train_rate', default: 1 })
  @Min(1)
  baseTrainRate: number;

  /**
   * Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning
   */
  @Column({ name: 'fine_tuning_input_rate', default: 1 })
  @Min(1)
  fineTuningInputRate: number;

  /**
   * Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning
   */
  @Column({ name: 'fine_tuning_output_rate', default: 1 })
  @Min(1)
  fineTuningOutputRate: number;

  /**
   * Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning
   */
  @Column({ name: 'fine_tuning_train_rate', default: 1 })
  @Min(1)
  fineTuningTrainRate: number;

  /**
   * Số token tối đa mô hình có thể xử lý trong một lần request
   */
  @Column({ name: 'token_count', default: 1 })
  @Min(1)
  tokenCount: number;

  /**
   * Cấu hình và khả năng của mô hình dạng JSONB
   */
  @Column({
    name: 'config',
    type: 'jsonb',
  })
  config: ConfigModelBase;

  /**
   * ID nhân viên tạo bản ghi mô hình
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID nhân viên cập nhật bản ghi mô hình gần nhất
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  /**
   * Thời điểm tạo bản ghi (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của base model: PENDING - đang chờ duyệt, APPROVED - đã được duyệt, REJECTED - bị từ chối, DISABLED - đã bị vô hiệu hóa
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: BaseModelStatusEnum,
    default: BaseModelStatusEnum.DRAFT,
  })
  status: BaseModelStatusEnum;

  /**
   * ID nhân viên xóa bản ghi mô hình
   */
  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy: number | null;

  /**
   * Thời điểm xóa bản ghi (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
