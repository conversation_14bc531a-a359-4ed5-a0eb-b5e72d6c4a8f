import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { DataFineTuning } from './data-fine-tuning.entity';

/**
 * Entity đại diện cho bảng user_data_fine_tuning trong cơ sở dữ liệu
 * Lưu thông tin về các mô hình fine-tune riêng của người dùng, liên kết với mô hình gốc và nhà cung cấp
 */
@Entity('user_data_fine_tuning')
export class UserDataFineTuning {
  /**
   * UUID định danh duy nhất, tham chiếu đến bảng data_fine_tuning
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID của người dùng sở hữu mô hình fine-tune (tham chiếu bảng users)
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của bộ dữ liệu gốc (nếu là bản sao)
   */
  @Column({ name: 'source_id', type: 'uuid', nullable: true })
  sourceId: string | null;

  /**
   * <PERSON><PERSON> hệ một-một với bảng data_fine_tuning
   */
  @OneToOne(() => DataFineTuning)
  @JoinColumn({ name: 'id' })
  dataFineTuning: DataFineTuning;
}
