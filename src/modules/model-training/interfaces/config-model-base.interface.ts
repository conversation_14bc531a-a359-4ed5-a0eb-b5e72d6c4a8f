
/**
 * Interface định nghĩa cấu hình và khả năng của base model
 */
export interface ConfigModelBase {
  /** Có hỗ trợ tham số top_p không */
  hasTopP: boolean;

  /** Có hỗ trợ tham số top_k không */
  hasTopK: boolean;

  /** Có hỗ trợ function calling không */
  hasFunction: boolean;

  /** Có hỗ trợ tham số temperature không */
  hasTemperature: boolean;

  /** Có hỗ trợ xử lý văn bản không */
  hasText: boolean;

  /** Có hỗ trợ xử lý hình ảnh không */
  hasImage: boolean;

  /** Có hỗ trợ xử lý âm thanh không */
  hasAudio: boolean;

  /** Có hỗ trợ xử lý video không */
  hasVideo: boolean;

  /** Có hỗ trợ parallel tool calling không */
  hasParallelToolCall: boolean;

  /** <PERSON><PERSON>c mức độ reasoning effort được hỗ trợ */
  hasReasoningEffort: string[];

  /** Có hỗ trợ tham số max_tokens không */
  hasMaxTokens: boolean;

  /** Các định dạng response được hỗ trợ */
  hasResponseFormat: string[];

  /** Có hỗ trợ file search không */
  hasFileSearch: boolean;

  /** Có hỗ trợ code interpreter không */
  hasCodeInterpreter: boolean;

  /** Có hỗ trợ streaming không */
  hasStreaming: boolean;

  /** Có hỗ trợ system message không */
  hasSystemMessage: boolean;

  /** Có hỗ trợ stop sequences không */
  hasStopSequences: boolean;

  /** Có hỗ trợ presence penalty không */
  hasPresencePenalty: boolean;

  /** Có hỗ trợ frequency penalty không */
  hasFrequencyPenalty: boolean;

  /** Có hỗ trợ logit bias không */
  hasLogitBias: boolean;

  /** Có hỗ trợ seed cho reproducible outputs không */
  hasSeed: boolean;
}