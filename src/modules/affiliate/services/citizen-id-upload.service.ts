import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';
import { MediaType } from '@utils/file';
@Injectable()
export class CitizenIdUploadService {
  private readonly logger = new Logger(CitizenIdUploadService.name);

  constructor(private readonly s3Service: S3Service) {}

  /**
   * Tạo URL tạm thời để upload ảnh mặt trước CCCD
   * @param userId ID của người dùng
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async createCitizenIdFrontUploadUrl(userId: number): Promise<{ uploadUrl: string; key: string }> {
    try {
      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/citizen-id/front-${Date.now()}.jpg`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        'image/jpeg' as MediaType,
        FileSizeEnum.FIVE_MB
      );

      // Trích xuất key từ URL
      const extractedKey = this.extractKeyFromUrl(presignedUrl);

      return {
        uploadUrl: presignedUrl,
        key: extractedKey,
      };
    } catch (error) {
      this.logger.error(`Error creating citizen ID front upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên ảnh mặt trước CCCD'
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload ảnh mặt sau CCCD
   * @param userId ID của người dùng
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async createCitizenIdBackUploadUrl(userId: number): Promise<{ uploadUrl: string; key: string }> {
    try {
      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/citizen-id/back-${Date.now()}.jpg`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        'image/jpeg' as MediaType,
        FileSizeEnum.FIVE_MB
      );

      // Trích xuất key từ URL
      const extractedKey = this.extractKeyFromUrl(presignedUrl);

      return {
        uploadUrl: presignedUrl,
        key: extractedKey,
      };
    } catch (error) {
      this.logger.error(`Error creating citizen ID back upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên ảnh mặt sau CCCD'
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload chữ ký
   * @param userId ID của người dùng
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async createSignatureUploadUrl(userId: number): Promise<{ uploadUrl: string; key: string }> {
    try {
      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/signature/${Date.now()}.png`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        'image/png' as MediaType,
        FileSizeEnum.ONE_MB
      );

      // Trích xuất key từ URL
      const extractedKey = this.extractKeyFromUrl(presignedUrl);

      return {
        uploadUrl: presignedUrl,
        key: extractedKey,
      };
    } catch (error) {
      this.logger.error(`Error creating signature upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên chữ ký'
      );
    }
  }

  /**
   * Lưu chữ ký dưới dạng base64
   * @param userId ID của người dùng
   * @param signatureBase64 Chữ ký dưới dạng base64
   * @returns Key của file đã lưu
   */
  async saveSignatureBase64(userId: number, signatureBase64: string): Promise<string> {
    try {
      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/signature/${Date.now()}.png`;

      // Xử lý chuỗi base64
      // Loại bỏ phần tiền tố data:image/png;base64, hoặc data:image/jpeg;base64,
      const base64Data = signatureBase64.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');

      // Chuyển đổi base64 thành buffer
      const buffer = Buffer.from(base64Data, 'base64');

      // Xác định content type từ chuỗi base64
      const contentType = signatureBase64.startsWith('data:image/png')
        ? 'image/png'
        : 'image/jpeg';

      // Upload trực tiếp lên S3/Cloudflare R2
      await this.s3Service.uploadBuffer(key, buffer, contentType as MediaType);

      this.logger.log(`Signature uploaded successfully for user ${userId}`);

      return key;
    } catch (error) {
      this.logger.error(`Error saving signature for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể lưu chữ ký'
      );
    }
  }

  /**
   * Trích xuất key từ URL
   * @param url URL đầy đủ
   * @returns Key của file
   */
  private extractKeyFromUrl(url: string): string {
    // Phân tích URL để lấy phần path
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;

      // Loại bỏ phần domain và lấy phần path
      // Ví dụ: từ "https://example.com/bucket/affiliate/123/citizen-id/front-1234567890.jpg"
      // Lấy "affiliate/123/citizen-id/front-1234567890.jpg"
      const parts = path.split('/');

      // Bỏ qua phần đầu tiên (rỗng) và phần thứ hai (tên bucket)
      const key = parts.slice(2).join('/');

      return key;
    } catch (error) {
      this.logger.error(`Error extracting key from URL: ${error.message}`, error.stack);
      // Trả về URL gốc nếu không thể phân tích
      return url;
    }
  }
}
