import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { AffiliateContractQueryDto } from '../admin/dto';
import { PaginatedResult } from '@/common/response';
import { ContractStatus, ContractType } from '../enums';

/**
 * Repository cho AffiliateContract
 * Extends Repository<AffiliateContract> theo Repository Standard #2
 */
@Injectable()
export class AffiliateContractRepository extends Repository<AffiliateContract> {
  constructor(dataSource: DataSource) {
    super(AffiliateContract, dataSource.createEntityManager());
  }

  /**
   * Tìm hợp đồng theo ID
   * @param id ID của hợp đồng
   * @returns Thông tin hợp đồng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliateContract | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Tìm hợp đồng theo ID người dùng
   * @param userId ID người dùng
   * @returns Danh sách hợp đồng
   */
  async findByUserId(userId: number): Promise<AffiliateContract[]> {
    return this.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm hợp đồng theo loại hợp đồng
   * @param contractType Loại hợp đồng
   * @returns Danh sách hợp đồng
   */
  async findByContractType(contractType: ContractType): Promise<AffiliateContract[]> {
    return this.find({ where: { contractType } });
  }

  /**
   * Tìm danh sách hợp đồng với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  async findWithPagination(
    queryDto: AffiliateContractQueryDto,
  ): Promise<PaginatedResult<AffiliateContract>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      affiliateAccountId,
      contractCode,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('contract');

    // Thêm điều kiện người dùng nếu có
    if (affiliateAccountId) {
      queryBuilder.andWhere('contract.userId = :userId', {
        userId: affiliateAccountId,
      });
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('contract.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('contract.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('contract.status = :status', { status });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search || contractCode) {
      const searchTerm = search || contractCode;
      queryBuilder.andWhere('contract.id = :searchTerm', {
        searchTerm,
      });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`contract.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật trạng thái hợp đồng
   * @param id ID của hợp đồng
   * @param status Trạng thái mới
   * @param note Ghi chú
   * @returns Kết quả cập nhật
   */
  async updateStatus(
    id: number,
    status: ContractStatus,
    note?: string,
  ): Promise<boolean> {
    const updateData: any = {
      status,
      updatedAt: Math.floor(Date.now() / 1000),
    };

    if (note) {
      updateData.rejectionReason = note;
    }

    const result = await this.update(id, updateData);

    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
