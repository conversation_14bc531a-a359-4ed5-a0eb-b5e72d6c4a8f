import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AffiliateRegistrationService } from '../affiliate-registration.service';
import { AffiliateAccount } from '../../entities/affiliate-account.entity';
import { AffiliateContract } from '../../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { ContractStatus, ContractType } from '../../enums';
import { AppException } from '@common/exceptions';

// Mock repositories
const mockAffiliateAccountRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockAffiliateContractRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockBusinessInfoRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockUserRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

describe('AffiliateRegistrationService', () => {
  let service: AffiliateRegistrationService;
  let affiliateAccountRepository: Repository<AffiliateAccount>;
  let affiliateContractRepository: Repository<AffiliateContract>;
  let businessInfoRepository: Repository<BusinessInfo>;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateRegistrationService,
        {
          provide: getRepositoryToken(AffiliateAccount),
          useFactory: mockAffiliateAccountRepository,
        },
        {
          provide: getRepositoryToken(AffiliateContract),
          useFactory: mockAffiliateContractRepository,
        },
        {
          provide: getRepositoryToken(BusinessInfo),
          useFactory: mockBusinessInfoRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<AffiliateRegistrationService>(AffiliateRegistrationService);
    affiliateAccountRepository = module.get<Repository<AffiliateAccount>>(
      getRepositoryToken(AffiliateAccount),
    );
    affiliateContractRepository = module.get<Repository<AffiliateContract>>(
      getRepositoryToken(AffiliateContract),
    );
    businessInfoRepository = module.get<Repository<BusinessInfo>>(
      getRepositoryToken(BusinessInfo),
    );
    userRepository = module.get<Repository<User>>(
      getRepositoryToken(User),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('restartAfterRejection', () => {
    it('should throw an exception if contract is not found', async () => {
      // Arrange
      const userId = 1;
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.restartAfterRejection(userId)).rejects.toThrow(AppException);
    });

    it('should throw an exception if contract is not in rejected status', async () => {
      // Arrange
      const userId = 1;
      const contract = {
        id: 1,
        userId,
        status: ContractStatus.DRAFT,
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(contract as AffiliateContract);

      // Act & Assert
      await expect(service.restartAfterRejection(userId)).rejects.toThrow(AppException);
    });

    it('should create a new contract and send restart event for personal account', async () => {
      // Arrange
      const userId = 1;
      const contract = {
        id: 1,
        userId,
        status: ContractStatus.REJECTED,
        contractType: ContractType.INDIVIDUAL,
      };
      
      const newContract = {
        id: 2,
        userId,
        status: ContractStatus.DRAFT,
        contractType: ContractType.INDIVIDUAL,
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(contract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'create').mockReturnValue(newContract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'save').mockResolvedValue(newContract as AffiliateContract);
      jest.spyOn(service, 'sendEvent').mockReturnValue(true);

      // Act
      const result = await service.restartAfterRejection(userId);

      // Assert
      expect(affiliateContractRepository.create).toHaveBeenCalled();
      expect(affiliateContractRepository.save).toHaveBeenCalled();
      expect(service.sendEvent).toHaveBeenCalledWith(userId, 'RESTART_AFTER_REJECTION', {
        contractId: newContract.id,
      });
      expect(result).toBe(true);
    });

    it('should create a new contract and send restart event for business account', async () => {
      // Arrange
      const userId = 1;
      const contract = {
        id: 1,
        userId,
        status: ContractStatus.REJECTED,
        contractType: ContractType.BUSINESS,
      };
      
      const newContract = {
        id: 2,
        userId,
        status: ContractStatus.DRAFT,
        contractType: ContractType.BUSINESS,
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(contract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'create').mockReturnValue(newContract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'save').mockResolvedValue(newContract as AffiliateContract);
      jest.spyOn(service, 'sendEvent').mockReturnValue(true);

      // Act
      const result = await service.restartAfterRejection(userId);

      // Assert
      expect(affiliateContractRepository.create).toHaveBeenCalled();
      expect(affiliateContractRepository.save).toHaveBeenCalled();
      expect(service.sendEvent).toHaveBeenCalledWith(userId, 'RESTART_AFTER_REJECTION', {
        contractId: newContract.id,
      });
      expect(result).toBe(true);
    });
  });

  describe('upgradeToBusinessAccount', () => {
    it('should throw an exception if affiliate account is not found', async () => {
      // Arrange
      const userId = 1;
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateAccountRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.upgradeToBusinessAccount(userId)).rejects.toThrow(AppException);
    });

    it('should throw an exception if account is already a business account', async () => {
      // Arrange
      const userId = 1;
      const account = {
        id: 1,
        userId,
        accountType: 'BUSINESS',
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateAccountRepository, 'findOne').mockResolvedValue(account as AffiliateAccount);

      // Act & Assert
      await expect(service.upgradeToBusinessAccount(userId)).rejects.toThrow(AppException);
    });

    it('should throw an exception if contract is not found', async () => {
      // Arrange
      const userId = 1;
      const account = {
        id: 1,
        userId,
        accountType: 'PERSONAL',
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateAccountRepository, 'findOne').mockResolvedValue(account as AffiliateAccount);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.upgradeToBusinessAccount(userId)).rejects.toThrow(AppException);
    });

    it('should throw an exception if contract is not in approved status', async () => {
      // Arrange
      const userId = 1;
      const account = {
        id: 1,
        userId,
        accountType: 'PERSONAL',
      };
      
      const contract = {
        id: 1,
        userId,
        status: ContractStatus.DRAFT,
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateAccountRepository, 'findOne').mockResolvedValue(account as AffiliateAccount);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(contract as AffiliateContract);

      // Act & Assert
      await expect(service.upgradeToBusinessAccount(userId)).rejects.toThrow(AppException);
    });

    it('should create a new business contract and send upgrade event', async () => {
      // Arrange
      const userId = 1;
      const account = {
        id: 1,
        userId,
        accountType: 'PERSONAL',
      };
      
      const contract = {
        id: 1,
        userId,
        status: ContractStatus.APPROVED,
        contractType: ContractType.INDIVIDUAL,
      };
      
      const newContract = {
        id: 2,
        userId,
        status: ContractStatus.DRAFT,
        contractType: ContractType.BUSINESS,
      };
      
      jest.spyOn(service, 'startRegistration').mockResolvedValue(null);
      jest.spyOn(affiliateAccountRepository, 'findOne').mockResolvedValue(account as AffiliateAccount);
      jest.spyOn(affiliateContractRepository, 'findOne').mockResolvedValue(contract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'create').mockReturnValue(newContract as AffiliateContract);
      jest.spyOn(affiliateContractRepository, 'save').mockResolvedValue(newContract as AffiliateContract);
      jest.spyOn(service, 'sendEvent').mockReturnValue(true);

      // Act
      const result = await service.upgradeToBusinessAccount(userId);

      // Assert
      expect(affiliateContractRepository.create).toHaveBeenCalled();
      expect(affiliateContractRepository.save).toHaveBeenCalled();
      expect(service.sendEvent).toHaveBeenCalledWith(userId, 'UPGRADE_TO_BUSINESS', {
        contractId: newContract.id,
      });
      expect(result).toBe(true);
    });
  });
});
