import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateRegistrationController } from '../affiliate-registration.controller';
import { AffiliateRegistrationService } from '../affiliate-registration.service';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';

// Mock service
const mockAffiliateRegistrationService = () => ({
  startRegistration: jest.fn(),
  getCurrentState: jest.fn(),
  sendEvent: jest.fn(),
  restartAfterRejection: jest.fn(),
  upgradeToBusinessAccount: jest.fn(),
});

describe('AffiliateRegistrationController', () => {
  let controller: AffiliateRegistrationController;
  let service: AffiliateRegistrationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateRegistrationController],
      providers: [
        {
          provide: AffiliateRegistrationService,
          useFactory: mockAffiliateRegistrationService,
        },
      ],
    }).compile();

    controller = module.get<AffiliateRegistrationController>(AffiliateRegistrationController);
    service = module.get<AffiliateRegistrationService>(AffiliateRegistrationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('restartAfterRejection', () => {
    it('should call service.restartAfterRejection and return current state', async () => {
      // Arrange
      const user: JwtPayload = { id: 1, email: '<EMAIL>', fullName: 'Test User' };
      const currentState = {
        value: 'citizenIdUpload',
        context: {
          userId: 1,
          accountType: 'PERSONAL',
        },
      };
      
      jest.spyOn(service, 'restartAfterRejection').mockResolvedValue(true);
      jest.spyOn(service, 'getCurrentState').mockReturnValue(currentState);

      // Act
      const result = await controller.restartAfterRejection(user);

      // Assert
      expect(service.restartAfterRejection).toHaveBeenCalledWith(user.id);
      expect(service.getCurrentState).toHaveBeenCalledWith(user.id);
      expect(result).toEqual(
        ApiResponseDto.success(
          {
            state: currentState.value,
            context: currentState.context,
          },
          'Đã bắt đầu lại quy trình ký hợp đồng',
        ),
      );
    });
  });

  describe('upgradeToBusinessAccount', () => {
    it('should call service.upgradeToBusinessAccount and return current state', async () => {
      // Arrange
      const user: JwtPayload = { id: 1, email: '<EMAIL>', fullName: 'Test User' };
      const currentState = {
        value: 'infoInput',
        context: {
          userId: 1,
          accountType: 'BUSINESS',
        },
      };
      
      jest.spyOn(service, 'upgradeToBusinessAccount').mockResolvedValue(true);
      jest.spyOn(service, 'getCurrentState').mockReturnValue(currentState);

      // Act
      const result = await controller.upgradeToBusinessAccount(user);

      // Assert
      expect(service.upgradeToBusinessAccount).toHaveBeenCalledWith(user.id);
      expect(service.getCurrentState).toHaveBeenCalledWith(user.id);
      expect(result).toEqual(
        ApiResponseDto.success(
          {
            state: currentState.value,
            context: currentState.context,
          },
          'Đã bắt đầu quy trình nâng cấp lên tài khoản doanh nghiệp',
        ),
      );
    });
  });
});
