import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { AffiliateRegistrationService } from './affiliate-registration.service';
import { AffiliateRegistrationController } from './affiliate-registration.controller';
import { AffiliateRegistrationAdminController } from './affiliate-registration-admin.controller';
import { AffiliateUploadService } from '../services/affiliate-upload.service';
import { CitizenIdUploadService } from '../services/citizen-id-upload.service';
import {
  AffiliateUploadController,
  AffiliateBusinessController,
  CitizenIdUploadController
} from '../user/controllers';
import { S3Service } from '@shared/services/s3.service';
import { UserRepository } from '@modules/user/repositories/user.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([AffiliateAccount, AffiliateContract, BusinessInfo, User]),
  ],
  controllers: [
    AffiliateRegistrationController,
    AffiliateRegistrationAdminController,
    AffiliateUploadController,
    AffiliateBusinessController,
    CitizenIdUploadController,
  ],
  providers: [
    AffiliateRegistrationService,
    AffiliateUploadService,
    CitizenIdUploadService,
    S3Service,
    UserRepository,
  ],
  exports: [
    AffiliateRegistrationService,
    AffiliateUploadService,
    CitizenIdUploadService,
    UserRepository,
  ],
})
export class AffiliateRegistrationModule {}
