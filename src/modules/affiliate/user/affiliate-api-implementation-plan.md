# Kế Hoạch Triển Khai API Affiliate

## Tổng Quan

Tài liệu này mô tả kế hoạch triển khai chi tiết cho các API Affiliate dành cho người dùng. Các API này sẽ cung cấp thông tin thống kê, thông tin tài kho<PERSON>n, danh sách đơn hàng, lịch sử rút tiền và danh sách khách hàng affiliate.

## Phân Tích Yêu Cầu

### Các API Cần Triển Khai

1. **API Thống Kê Tài Khoản Affiliate**
   - C<PERSON> cấp thông tin tổng quan về tài khoản affiliate
   - <PERSON><PERSON> g<PERSON><PERSON> số dư ví, số tiền đang xử lý, số lư<PERSON> click, kh<PERSON>ch hàng, đơn hàng, doanh thu, CVR
   - Hỗ trợ lọc theo khoảng thời gian

2. **API Thông Tin Tài Khoản Affiliate**
   - <PERSON><PERSON> cấp thông tin chi tiết về tài <PERSON> affiliate
   - <PERSON><PERSON> g<PERSON><PERSON> thông tin cá nhân, lo<PERSON><PERSON>à<PERSON>, tr<PERSON>ng thái, rank và mức hoa hồng

3. **API Danh Sách Đơn Hàng Affiliate**
   - Cung cấp danh sách đơn hàng được tạo thông qua liên kết affiliate
   - Bao gồm thông tin về mã đơn hàng, khách hàng, ngày đặt hàng, giá trị đơn hàng
   - Hỗ trợ phân trang và lọc theo khoảng thời gian

4. **API Danh Sách Lịch Sử Rút Tiền**
   - Cung cấp danh sách các yêu cầu rút tiền
   - Hỗ trợ phân trang và lọc theo khoảng thời gian

5. **API Danh Sách Khách Hàng Affiliate**
   - Cung cấp danh sách khách hàng được giới thiệu thông qua affiliate
   - Hỗ trợ phân trang và lọc theo khoảng thời gian

### Các Entity Liên Quan

1. **AffiliateAccount**
   - Chứa thông tin tài khoản affiliate
   - Bao gồm trạng thái, số dư, tổng thu nhập

2. **AffiliateClick**
   - Lưu thông tin các lượt click từ affiliate links
   - Bao gồm thông tin IP, user agent, thời gian click

3. **AffiliateCustomerOrder**
   - Lưu thông tin đơn hàng được tạo thông qua affiliate
   - Liên kết với PointPurchaseTransaction để lấy thông tin đơn hàng

4. **AffiliateWithdrawHistory**
   - Lưu thông tin các yêu cầu rút tiền
   - Bao gồm số tiền, trạng thái, thông tin ngân hàng

5. **AffiliateRank**
   - Lưu thông tin về các rank affiliate
   - Bao gồm tên rank, icon, mức hoa hồng

6. **User**
   - Lưu thông tin người dùng
   - Cần xác định người dùng nào được giới thiệu bởi affiliate

## Kế Hoạch Triển Khai

### Giai Đoạn 1: Thiết Lập Cơ Sở Hạ Tầng (2 ngày)

1. **Tạo các DTO cần thiết**
   - Tạo các DTO cho request và response của các API
   - Đảm bảo tuân thủ chuẩn API của hệ thống

2. **Thiết lập cấu trúc thư mục**
   - Tạo các thư mục controller, service, dto
   - Đảm bảo tuân thủ cấu trúc dự án

3. **Thiết lập các guard và decorator**
   - Tạo JwtUserGuard để bảo vệ các API
   - Tạo CurrentUser decorator để truy cập thông tin người dùng đã xác thực

### Giai Đoạn 2: Phát Triển Service Layer (5 ngày)

1. **Phát triển AffiliateStatisticsService**
   - Triển khai logic tính toán thống kê
   - Xử lý lọc theo khoảng thời gian

2. **Phát triển AffiliateAccountService**
   - Triển khai logic lấy thông tin tài khoản
   - Xử lý lấy thông tin rank và mức hoa hồng

3. **Phát triển AffiliateOrderService**
   - Triển khai logic lấy danh sách đơn hàng
   - Xử lý phân trang và lọc theo khoảng thời gian

4. **Phát triển AffiliateWithdrawalService**
   - Triển khai logic lấy danh sách yêu cầu rút tiền
   - Xử lý phân trang và lọc theo khoảng thời gian

5. **Phát triển AffiliateCustomerService**
   - Triển khai logic lấy danh sách khách hàng
   - Xử lý phân trang và lọc theo khoảng thời gian

### Giai Đoạn 3: Phát Triển Controller Layer (3 ngày)

1. **Phát triển AffiliateController**
   - Triển khai các endpoint cho các API
   - Tích hợp với các service đã phát triển

2. **Tài liệu hóa API với Swagger**
   - Thêm các decorator Swagger cho các endpoint
   - Mô tả chi tiết các tham số, phản hồi và mã lỗi

### Giai Đoạn 4: Kiểm Thử và Tối Ưu Hóa (3 ngày)

1. **Viết unit test**
   - Viết test cho các service
   - Đảm bảo độ phủ test tối thiểu 80%

2. **Viết integration test**
   - Viết test cho các controller
   - Kiểm tra luồng hoạt động end-to-end

3. **Tối ưu hóa hiệu suất**
   - Tối ưu các truy vấn database
   - Thêm cache cho các dữ liệu thống kê

### Giai Đoạn 5: Triển Khai và Giám Sát (2 ngày)

1. **Triển khai lên môi trường staging**
   - Kiểm tra hoạt động trên môi trường staging
   - Xử lý các vấn đề phát sinh

2. **Triển khai lên môi trường production**
   - Triển khai từng API một
   - Giám sát hoạt động sau khi triển khai

## Chi Tiết Triển Khai

### 1. API Thống Kê Tài Khoản Affiliate

#### Controller
```typescript
@Controller('user/affiliate')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateStatisticsService: AffiliateStatisticsService) {}

  @Get('statistics')
  @ApiOperation({ summary: 'Lấy thông tin thống kê tài khoản affiliate' })
  async getStatistics(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AffiliateStatisticsQueryDto
  ): Promise<ApiResponseDto<AffiliateStatisticsDto>> {
    const statistics = await this.affiliateStatisticsService.getStatistics(user.id, queryDto);
    return wrapResponse(statistics, 'Lấy thông tin thống kê thành công');
  }
}
```

#### Service
```typescript
@Injectable()
export class AffiliateStatisticsService {
  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateClickRepository: AffiliateClickRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly userRepository: UserRepository
  ) {}

  async getStatistics(userId: number, queryDto: AffiliateStatisticsQueryDto): Promise<AffiliateStatisticsDto> {
    // Lấy tài khoản affiliate của người dùng
    const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
    
    if (!affiliateAccount) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate');
    }

    // Tính toán các thống kê
    const walletBalance = affiliateAccount.availableBalance;
    const pendingAmount = await this.calculatePendingAmount(affiliateAccount.id, queryDto);
    const clickCount = await this.countClicks(affiliateAccount.id, queryDto);
    const customerCount = await this.countCustomers(affiliateAccount.id, queryDto);
    const orderCount = await this.countOrders(affiliateAccount.id, queryDto);
    const revenue = await this.calculateRevenue(affiliateAccount.id, queryDto);
    const conversionRate = clickCount > 0 ? (orderCount / clickCount) * 100 : 0;

    return {
      walletBalance,
      pendingAmount,
      clickCount,
      customerCount,
      orderCount,
      revenue,
      conversionRate,
      period: {
        begin: queryDto.begin,
        end: queryDto.end
      }
    };
  }

  // Các phương thức hỗ trợ tính toán
  private async calculatePendingAmount(affiliateAccountId: number, queryDto: AffiliateStatisticsQueryDto): Promise<number> {
    // Logic tính toán số tiền đang xử lý
  }

  private async countClicks(affiliateAccountId: number, queryDto: AffiliateStatisticsQueryDto): Promise<number> {
    // Logic đếm số lượt click
  }

  private async countCustomers(affiliateAccountId: number, queryDto: AffiliateStatisticsQueryDto): Promise<number> {
    // Logic đếm số khách hàng
  }

  private async countOrders(affiliateAccountId: number, queryDto: AffiliateStatisticsQueryDto): Promise<number> {
    // Logic đếm số đơn hàng
  }

  private async calculateRevenue(affiliateAccountId: number, queryDto: AffiliateStatisticsQueryDto): Promise<number> {
    // Logic tính toán doanh thu
  }
}
```

### 2. API Thông Tin Tài Khoản Affiliate

#### Controller
```typescript
@Controller('user/affiliate')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateAccountService: AffiliateAccountService) {}

  @Get('account')
  @ApiOperation({ summary: 'Lấy thông tin tài khoản affiliate' })
  async getAccount(
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<AffiliateAccountDto>> {
    const account = await this.affiliateAccountService.getAccount(user.id);
    return wrapResponse(account, 'Lấy thông tin tài khoản affiliate thành công');
  }
}
```

#### Service
```typescript
@Injectable()
export class AffiliateAccountService {
  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly userRepository: UserRepository
  ) {}

  async getAccount(userId: number): Promise<AffiliateAccountDto> {
    // Lấy tài khoản affiliate của người dùng
    const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
    
    if (!affiliateAccount) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate');
    }

    // Lấy thông tin người dùng
    const user = await this.userRepository.findById(userId);

    // Lấy thông tin rank
    const rank = await this.affiliateRankRepository.findById(affiliateAccount.rankId);

    // Tạo referral code và link
    const referralCode = this.generateReferralCode(user.fullName, affiliateAccount.id);
    const referralLink = `https://app.redai.vn?ref=${referralCode}`;

    return {
      accountInfo: {
        id: affiliateAccount.id,
        partnerName: user.fullName,
        accountType: UserTypeEnum, // Giả định, cần điều chỉnh theo thực tế
        status: affiliateAccount.status,
        createdAt: affiliateAccount.createdAt
      },
      rankInfo: rank ? {
        id: rank.id,
        rankName: rank.rankName,
        rankBadge: rank.rankBadge,
        commission: rank.commission,
        minCondition: rank.minCondition,
        maxCondition: rank.maxCondition
      } : null,
      referralCode,
      referralLink
    };
  }

  private generateReferralCode(fullName: string, accountId: number): string {
    // Logic tạo referral code
  }
}
```

### 3. API Danh Sách Đơn Hàng Affiliate

#### Controller
```typescript
@Controller('user/affiliate')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateOrderService: AffiliateOrderService) {}

  @Get('orders')
  @ApiOperation({ summary: 'Lấy danh sách đơn hàng affiliate' })
  async getOrders(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AffiliateOrderQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateOrderDto>>> {
    const orders = await this.affiliateOrderService.getOrders(user.id, queryDto);
    return wrapResponse(orders, 'Lấy danh sách đơn hàng affiliate thành công');
  }
}
```

#### Service
```typescript
@Injectable()
export class AffiliateOrderService {
  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly pointPurchaseTransactionRepository: PointPurchaseTransactionRepository,
    private readonly userRepository: UserRepository
  ) {}

  async getOrders(userId: number, queryDto: AffiliateOrderQueryDto): Promise<PaginatedResult<AffiliateOrderDto>> {
    // Lấy tài khoản affiliate của người dùng
    const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
    
    if (!affiliateAccount) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate');
    }

    // Lấy danh sách đơn hàng với phân trang
    const { items, meta } = await this.affiliateCustomerOrderRepository.findWithPagination(
      affiliateAccount.id,
      queryDto
    );

    // Xử lý dữ liệu trả về
    const orderDtos = await Promise.all(
      items.map(async (order) => {
        // Lấy thông tin giao dịch
        const transaction = await this.pointPurchaseTransactionRepository.findById(order.orderId);
        
        // Lấy thông tin khách hàng
        const customer = await this.userRepository.findById(transaction.userId);

        return {
          orderId: order.orderId.toString(),
          customer: {
            id: customer.id,
            fullName: customer.fullName,
            email: customer.email,
            phoneNumber: customer.phoneNumber
          },
          orderDate: transaction.createdAt,
          amount: transaction.amount,
          commission: order.commission * transaction.amount / 100, // Tính hoa hồng
          status: transaction.status
        };
      })
    );

    return {
      items: orderDtos,
      meta
    };
  }
}
```

### 4. API Danh Sách Lịch Sử Rút Tiền

#### Controller
```typescript
@Controller('user/affiliate')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateWithdrawalService: AffiliateWithdrawalService) {}

  @Get('withdrawals')
  @ApiOperation({ summary: 'Lấy danh sách lịch sử rút tiền' })
  async getWithdrawals(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AffiliateWithdrawalQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateWithdrawalDto>>> {
    const withdrawals = await this.affiliateWithdrawalService.getWithdrawals(user.id, queryDto);
    return wrapResponse(withdrawals, 'Lấy danh sách lịch sử rút tiền thành công');
  }
}
```

#### Service
```typescript
@Injectable()
export class AffiliateWithdrawalService {
  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateWithdrawHistoryRepository: AffiliateWithdrawHistoryRepository
  ) {}

  async getWithdrawals(userId: number, queryDto: AffiliateWithdrawalQueryDto): Promise<PaginatedResult<AffiliateWithdrawalDto>> {
    // Lấy tài khoản affiliate của người dùng
    const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
    
    if (!affiliateAccount) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate');
    }

    // Lấy danh sách yêu cầu rút tiền với phân trang
    const { items, meta } = await this.affiliateWithdrawHistoryRepository.findWithPagination(
      affiliateAccount.id,
      queryDto
    );

    // Xử lý dữ liệu trả về
    const withdrawalDtos = items.map((withdrawal) => ({
      id: withdrawal.id,
      amount: withdrawal.amount,
      vatAmount: withdrawal.vatAmount,
      netPayment: withdrawal.netPayment,
      bankInfo: {
        bankCode: withdrawal.bankCode,
        accountNumber: withdrawal.accountNumber,
        accountName: withdrawal.accountName
      },
      status: withdrawal.status,
      createdAt: withdrawal.createdAt,
      finishAt: withdrawal.finishAt,
      rejectReason: withdrawal.rejectReason,
      purchaseInvoice: withdrawal.purchaseInvoice
    }));

    return {
      items: withdrawalDtos,
      meta
    };
  }
}
```

### 5. API Danh Sách Khách Hàng Affiliate

#### Controller
```typescript
@Controller('user/affiliate')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateCustomerService: AffiliateCustomerService) {}

  @Get('customers')
  @ApiOperation({ summary: 'Lấy danh sách khách hàng affiliate' })
  async getCustomers(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AffiliateCustomerQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateCustomerDto>>> {
    const customers = await this.affiliateCustomerService.getCustomers(user.id, queryDto);
    return wrapResponse(customers, 'Lấy danh sách khách hàng affiliate thành công');
  }
}
```

#### Service
```typescript
@Injectable()
export class AffiliateCustomerService {
  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly userRepository: UserRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly pointPurchaseTransactionRepository: PointPurchaseTransactionRepository
  ) {}

  async getCustomers(userId: number, queryDto: AffiliateCustomerQueryDto): Promise<PaginatedResult<AffiliateCustomerDto>> {
    // Lấy tài khoản affiliate của người dùng
    const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
    
    if (!affiliateAccount) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate');
    }

    // Lấy danh sách khách hàng với phân trang
    const { items, meta } = await this.userRepository.findAffiliateCustomersWithPagination(
      affiliateAccount.id,
      queryDto
    );

    // Xử lý dữ liệu trả về
    const customerDtos = await Promise.all(
      items.map(async (customer) => {
        // Lấy số đơn hàng và tổng chi tiêu
        const { orderCount, totalSpent } = await this.calculateCustomerStats(
          affiliateAccount.id,
          customer.id,
          queryDto
        );

        return {
          id: customer.id,
          fullName: customer.fullName,
          email: customer.email,
          phoneNumber: customer.phoneNumber,
          createdAt: customer.createdAt,
          orderCount,
          totalSpent
        };
      })
    );

    return {
      items: customerDtos,
      meta
    };
  }

  private async calculateCustomerStats(
    affiliateAccountId: number,
    customerId: number,
    queryDto: AffiliateCustomerQueryDto
  ): Promise<{ orderCount: number; totalSpent: number }> {
    // Logic tính toán số đơn hàng và tổng chi tiêu
  }
}
```

## Lịch Trình Triển Khai

| Giai Đoạn | Công Việc | Thời Gian | Người Phụ Trách |
|-----------|-----------|-----------|-----------------|
| 1 | Thiết lập cơ sở hạ tầng | 2 ngày | Developer 1 |
| 2 | Phát triển AffiliateStatisticsService | 1 ngày | Developer 1 |
| 2 | Phát triển AffiliateAccountService | 1 ngày | Developer 2 |
| 2 | Phát triển AffiliateOrderService | 1 ngày | Developer 1 |
| 2 | Phát triển AffiliateWithdrawalService | 1 ngày | Developer 2 |
| 2 | Phát triển AffiliateCustomerService | 1 ngày | Developer 1 |
| 3 | Phát triển Controller Layer | 3 ngày | Developer 1 & 2 |
| 4 | Kiểm thử và tối ưu hóa | 3 ngày | Developer 1 & 2 |
| 5 | Triển khai và giám sát | 2 ngày | Developer 1 & 2 |

## Rủi Ro và Giải Pháp

| Rủi Ro | Mức Độ | Giải Pháp |
|--------|--------|-----------|
| Hiệu suất truy vấn kém khi dữ liệu lớn | Cao | Tối ưu hóa truy vấn, thêm index, sử dụng cache |
| Lỗi tính toán thống kê | Trung bình | Viết unit test kỹ lưỡng, kiểm tra chéo kết quả |
| Vấn đề bảo mật thông tin | Cao | Đảm bảo xác thực và phân quyền chặt chẽ |
| Thời gian phản hồi API chậm | Trung bình | Tối ưu hóa code, sử dụng cache, phân trang hợp lý |

## Kết Luận

Kế hoạch triển khai API Affiliate đã được xây dựng chi tiết với các giai đoạn rõ ràng. Việc tuân thủ kế hoạch này sẽ giúp đảm bảo các API được phát triển đúng tiến độ, đáp ứng yêu cầu về chức năng và hiệu suất. Các rủi ro đã được xác định và có giải pháp phòng ngừa, giúp giảm thiểu tác động đến dự án.
