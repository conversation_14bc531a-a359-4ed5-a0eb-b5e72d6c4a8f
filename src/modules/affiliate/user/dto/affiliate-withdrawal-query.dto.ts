import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho trạng thái yêu cầu rút tiền
 */
export enum WithdrawalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

/**
 * DTO cho tham số truy vấn danh sách yêu cầu rút tiền
 */
export class AffiliateWithdrawalQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái yêu cầu',
    enum: WithdrawalStatus,
    example: WithdrawalStatus.PENDING
  })
  @IsOptional()
  @IsEnum(WithdrawalStatus)
  status?: WithdrawalStatus;
}
