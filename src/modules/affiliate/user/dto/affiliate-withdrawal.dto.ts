import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin ngân hàng trong yêu cầu rút tiền
 */
export class AffiliateBankInfoDto {
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'TCB'
  })
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '***********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên tài khoản ngân hàng',
    example: 'NGUYEN VAN A'
  })
  accountName: string;
}

/**
 * DTO cho thông tin yêu cầu rút tiền
 */
export class AffiliateWithdrawalDto {
  @ApiProperty({
    description: 'ID của yêu cầu rút tiền',
    example: 789
  })
  id: number;

  @ApiProperty({
    description: 'Số tiền rút',
    example: 1000000
  })
  amount: number;

  @ApiProperty({
    description: 'Tiền thuế VAT',
    example: 100000
  })
  vatAmount: number;

  @ApiProperty({
    description: 'Tiền thực nhận',
    example: 900000
  })
  netPayment: number;

  @ApiProperty({
    description: 'Thông tin ngân hàng',
    type: AffiliateBankInfoDto
  })
  bankInfo: AffiliateBankInfoDto;

  @ApiProperty({
    description: 'Trạng thái yêu cầu',
    example: 'APPROVED'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo yêu cầu (Unix timestamp)',
    example: **********
  })
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành yêu cầu (Unix timestamp)',
    example: **********
  })
  finishAt?: number;

  @ApiPropertyOptional({
    description: 'Lý do từ chối',
    example: 'Thông tin tài khoản không chính xác'
  })
  rejectReason?: string;

  @ApiPropertyOptional({
    description: 'Đường dẫn hóa đơn đầu vào',
    example: 'invoice_789.pdf'
  })
  purchaseInvoice?: string;
}
