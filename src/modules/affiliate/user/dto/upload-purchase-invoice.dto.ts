import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * D<PERSON> cho yêu cầu upload hóa đơn đầu vào
 */
export class UploadPurchaseInvoiceDto {
  @ApiProperty({
    description: 'URL hóa đơn đầu vào',
    example: 'affiliate/123/invoice/invoice-1234567890.pdf',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  purchaseInvoiceUrl: string;
}
