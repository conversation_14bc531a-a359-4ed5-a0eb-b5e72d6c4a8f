import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, Min } from 'class-validator';

/**
 * DTO cho việc tạo yêu cầu rút tiền affiliate
 */
export class CreateWithdrawRequestDto {
  @ApiProperty({
    description: 'Số tiền rút (tối thiểu 2,000,000 VND)',
    example: 2000000,
    minimum: 2000000,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(2000000, { message: 'Số tiền rút phải lớn hơn hoặc bằng 2,000,000 VND' })
  amount: number;
}
