import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tham số truy vấn thống kê affiliate
 */
export class AffiliateStatisticsQueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu thống kê (Unix timestamp)',
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc thống kê (Unix timestamp)',
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;
}
