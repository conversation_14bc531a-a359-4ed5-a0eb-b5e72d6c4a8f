import { ApiProperty } from '@nestjs/swagger';
import { PointConversionStatus } from '@modules/affiliate/enums';

/**
 * DTO cho kết quả chuyển đổi tiền hoa hồng sang điểm
 */
export class ConvertToPointsResponseDto {
  /**
   * ID của bản ghi chuyển đổi
   */
  @ApiProperty({
    description: 'ID của bản ghi chuyển đổi',
    example: 1,
  })
  id: number;

  /**
   * Số tiền đã chuyển đổi
   */
  @ApiProperty({
    description: 'Số tiền đã chuyển đổi',
    example: 10000,
  })
  amount: number;

  /**
   * Số point đã chuyển đổi
   */
  @ApiProperty({
    description: 'Số point đã chuyển đổi',
    example: 10000,
  })
  pointsConverted: number;

  /**
   * Tỷ lệ chuyển đổi
   */
  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi',
    example: 1,
  })
  conversionRate: number;

  /**
   * Số dư ví hiện tại
   */
  @ApiProperty({
    description: 'Số dư ví hiện tại',
    example: 90000,
  })
  currentWalletBalance: number;

  /**
   * Số dư point hiện tại
   */
  @ApiProperty({
    description: 'Số dư point hiện tại',
    example: 20000,
  })
  currentPointsBalance: number;

  /**
   * Trạng thái chuyển đổi
   */
  @ApiProperty({
    description: 'Trạng thái chuyển đổi',
    enum: PointConversionStatus,
    example: PointConversionStatus.SUCCESS,
  })
  status: PointConversionStatus;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600,
  })
  createdAt: number;
}
