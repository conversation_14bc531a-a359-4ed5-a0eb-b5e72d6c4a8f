import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateController } from '../controllers/affiliate.controller';
import { 
  AffiliateStatisticsService,
  AffiliateAccountService,
  AffiliateOrderService,
  AffiliateWithdrawalService,
  AffiliateCustomerService
} from '../services';
import { 
  AffiliateStatisticsQueryDto,
  AffiliateOrderQueryDto,
  AffiliateWithdrawalQueryDto,
  AffiliateCustomerQueryDto
} from '../dto';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { PaginatedResult } from '@/common/response';

describe('AffiliateController', () => {
  let controller: AffiliateController;
  let statisticsService: AffiliateStatisticsService;
  let accountService: AffiliateAccountService;
  let orderService: AffiliateOrderService;
  let withdrawalService: AffiliateWithdrawalService;
  let customerService: AffiliateCustomerService;

  // Mock user
  const mockUser: JWTPayload = {
    id: 1,
    email: '<EMAIL>',
    role: 'user'
  };

  // Mock services
  const mockStatisticsService = {
    getStatistics: jest.fn()
  };

  const mockAccountService = {
    getAccount: jest.fn()
  };

  const mockOrderService = {
    getOrders: jest.fn()
  };

  const mockWithdrawalService = {
    getWithdrawals: jest.fn()
  };

  const mockCustomerService = {
    getCustomers: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateController],
      providers: [
        { provide: AffiliateStatisticsService, useValue: mockStatisticsService },
        { provide: AffiliateAccountService, useValue: mockAccountService },
        { provide: AffiliateOrderService, useValue: mockOrderService },
        { provide: AffiliateWithdrawalService, useValue: mockWithdrawalService },
        { provide: AffiliateCustomerService, useValue: mockCustomerService }
      ]
    }).compile();

    controller = module.get<AffiliateController>(AffiliateController);
    statisticsService = module.get<AffiliateStatisticsService>(AffiliateStatisticsService);
    accountService = module.get<AffiliateAccountService>(AffiliateAccountService);
    orderService = module.get<AffiliateOrderService>(AffiliateOrderService);
    withdrawalService = module.get<AffiliateWithdrawalService>(AffiliateWithdrawalService);
    customerService = module.get<AffiliateCustomerService>(AffiliateCustomerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getStatistics', () => {
    it('should return statistics', async () => {
      // Arrange
      const queryDto = new AffiliateStatisticsQueryDto();
      const mockStatistics = {
        walletBalance: 1500000,
        pendingAmount: 500000,
        clickCount: 1250,
        customerCount: 45,
        orderCount: 78,
        revenue: 7800000,
        conversionRate: 6.24,
        period: {
          begin: **********,
          end: **********
        }
      };
      mockStatisticsService.getStatistics.mockResolvedValue(mockStatistics);

      // Act
      const result = await controller.getStatistics(mockUser, queryDto);

      // Assert
      expect(statisticsService.getStatistics).toHaveBeenCalledWith(mockUser.id, queryDto);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockStatistics);
      expect(result.message).toBe('Lấy thông tin thống kê thành công');
    });
  });

  describe('getAccount', () => {
    it('should return account information', async () => {
      // Arrange
      const mockAccount = {
        accountInfo: {
          id: 123,
          partnerName: 'Nguyễn Văn A',
          accountType: 'PERSONAL',
          status: 'ACTIVE',
          createdAt: **********
        },
        rankInfo: {
          id: 2,
          rankName: 'Silver',
          rankBadge: 'silver_badge.png',
          commission: 5.5,
          minCondition: 10,
          maxCondition: 49
        },
        referralCode: 'NGUYENA123',
        referralLink: 'https://redai.vn/ref/NGUYENA123'
      };
      mockAccountService.getAccount.mockResolvedValue(mockAccount);

      // Act
      const result = await controller.getAccount(mockUser);

      // Assert
      expect(accountService.getAccount).toHaveBeenCalledWith(mockUser.id);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAccount);
      expect(result.message).toBe('Lấy thông tin tài khoản affiliate thành công');
    });
  });

  describe('getOrders', () => {
    it('should return paginated orders', async () => {
      // Arrange
      const queryDto = new AffiliateOrderQueryDto();
      const mockOrders: PaginatedResult<any> = {
        items: [
          {
            orderId: 'ORD123456',
            customer: {
              id: 456,
              fullName: 'Trần Thị B',
              email: '<EMAIL>',
              phoneNumber: '**********'
            },
            orderDate: **********,
            amount: 1500000,
            commission: 75000,
            status: 'COMPLETED'
          }
        ],
        meta: {
          totalItems: 78,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 8,
          currentPage: 1
        }
      };
      mockOrderService.getOrders.mockResolvedValue(mockOrders);

      // Act
      const result = await controller.getOrders(mockUser, queryDto);

      // Assert
      expect(orderService.getOrders).toHaveBeenCalledWith(mockUser.id, queryDto);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockOrders);
      expect(result.message).toBe('Lấy danh sách đơn hàng affiliate thành công');
    });
  });

  describe('getWithdrawals', () => {
    it('should return paginated withdrawals', async () => {
      // Arrange
      const queryDto = new AffiliateWithdrawalQueryDto();
      const mockWithdrawals: PaginatedResult<any> = {
        items: [
          {
            id: 789,
            amount: 1000000,
            vatAmount: 100000,
            netPayment: 900000,
            bankInfo: {
              bankCode: 'TCB',
              accountNumber: '***********',
              accountName: 'NGUYEN VAN A'
            },
            status: 'APPROVED',
            createdAt: **********,
            finishAt: **********
          }
        ],
        meta: {
          totalItems: 15,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 2,
          currentPage: 1
        }
      };
      mockWithdrawalService.getWithdrawals.mockResolvedValue(mockWithdrawals);

      // Act
      const result = await controller.getWithdrawals(mockUser, queryDto);

      // Assert
      expect(withdrawalService.getWithdrawals).toHaveBeenCalledWith(mockUser.id, queryDto);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockWithdrawals);
      expect(result.message).toBe('Lấy danh sách lịch sử rút tiền thành công');
    });
  });

  describe('getCustomers', () => {
    it('should return paginated customers', async () => {
      // Arrange
      const queryDto = new AffiliateCustomerQueryDto();
      const mockCustomers: PaginatedResult<any> = {
        items: [
          {
            id: 456,
            fullName: 'Trần Thị B',
            email: '<EMAIL>',
            phoneNumber: '**********',
            createdAt: **********,
            orderCount: 3,
            totalSpent: 4500000
          }
        ],
        meta: {
          totalItems: 45,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 5,
          currentPage: 1
        }
      };
      mockCustomerService.getCustomers.mockResolvedValue(mockCustomers);

      // Act
      const result = await controller.getCustomers(mockUser, queryDto);

      // Assert
      expect(customerService.getCustomers).toHaveBeenCalledWith(mockUser.id, queryDto);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockCustomers);
      expect(result.message).toBe('Lấy danh sách khách hàng affiliate thành công');
    });
  });
});
