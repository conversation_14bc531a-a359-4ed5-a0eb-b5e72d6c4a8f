import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  AffiliateAccount,
  AffiliateClick,
  AffiliateCustomerOrder,
  AffiliateWithdrawHistory,
  AffiliateRank,
  AffiliatePointConversionHistory
} from '@modules/affiliate/entities';
import { User } from '@modules/user/entities';
import { PointPurchaseTransaction } from '@modules/r-point/entities';

// Repositories
import {
  AffiliateAccountRepository,
  AffiliateClickRepository,
  AffiliateCustomerOrderRepository,
  AffiliateWithdrawHistoryRepository,
  AffiliateRankRepository,
  AffiliateCustomerRepository,
  AffiliatePointConversionHistoryRepository
} from '@modules/affiliate/repositories';
import { UserRepository } from '@modules/user/repositories';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';

// Controllers
import {
  AffiliateStatisticsController,
  AffiliateAccountController,
  AffiliateOrderController,
  AffiliateWithdrawalController,
  AffiliateCustomerController,
  AffiliatePointConversionController,
  AffiliateReferralLinkController
} from './controllers';

// Services
import {
  AffiliateStatisticsService,
  AffiliateAccountService,
  AffiliateOrderService,
  AffiliateWithdrawalService,
  AffiliateCustomerService,
  AffiliatePointConversionService,
  AffiliateReferralLinkService
} from './services';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AffiliateAccount,
      AffiliateClick,
      AffiliateCustomerOrder,
      AffiliateWithdrawHistory,
      AffiliateRank,
      AffiliatePointConversionHistory,
      User,
      PointPurchaseTransaction
    ])
  ],
  controllers: [
    AffiliateStatisticsController,
    AffiliateAccountController,
    AffiliateOrderController,
    AffiliateWithdrawalController,
    AffiliateCustomerController,
    AffiliatePointConversionController,
    AffiliateReferralLinkController
  ],
  providers: [
    // Repositories
    AffiliateAccountRepository,
    AffiliateClickRepository,
    AffiliateCustomerOrderRepository,
    AffiliateWithdrawHistoryRepository,
    AffiliateRankRepository,
    AffiliateCustomerRepository,
    AffiliatePointConversionHistoryRepository,
    UserRepository,
    PointPurchaseTransactionRepository,

    // Services
    AffiliateStatisticsService,
    AffiliateAccountService,
    AffiliateOrderService,
    AffiliateWithdrawalService,
    AffiliateCustomerService,
    AffiliatePointConversionService,
    AffiliateReferralLinkService
  ],
  exports: [
    AffiliateStatisticsService,
    AffiliateAccountService,
    AffiliateOrderService,
    AffiliateWithdrawalService,
    AffiliateCustomerService,
    AffiliatePointConversionService,
    AffiliateReferralLinkService
  ]
})
export class AffiliateUserModule {}
