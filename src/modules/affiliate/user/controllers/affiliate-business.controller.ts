import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response';
import { AffiliateRegistrationService } from '../../state-machine/affiliate-registration.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';
import { SWAGGER_API_TAGS } from '@/common/swagger';

class BusinessInfoDto {
  @ApiProperty({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  businessName: string;

  @ApiProperty({
    description: 'Mã số thuế',
    example: '0123456789',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  taxCode: string;

  @ApiProperty({
    description: 'Địa chỉ doanh nghiệp',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    description: 'Người đại diện pháp luật',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  legalRepresentative: string;

  @ApiProperty({
    description: 'Chức vụ người đại diện',
    example: 'Giám đốc',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  position: string;

  @ApiProperty({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Số điện thoại doanh nghiệp',
    example: '0901234567',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;
}

@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_BUSINESS)
@Controller('affiliate/business')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateBusinessController {
  constructor(
    private readonly affiliateRegistrationService: AffiliateRegistrationService,
    @InjectRepository(BusinessInfo)
    private readonly businessInfoRepository: Repository<BusinessInfo>,
  ) {}

  @Post('info')
  @ApiOperation({ summary: 'Cập nhật thông tin doanh nghiệp' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin doanh nghiệp thành công',
    type: ApiResponseDto,
  })
  async updateBusinessInfo(
    @CurrentUser() user: JWTPayload,
    @Body() dto: BusinessInfoDto,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    // Kiểm tra xem đã có thông tin doanh nghiệp chưa
    let businessInfo = await this.businessInfoRepository.findOne({
      where: { userId: user.id },
    });

    if (!businessInfo) {
      // Tạo mới nếu chưa có
      businessInfo = new BusinessInfo();
      businessInfo.userId = user.id;
      businessInfo.createdAt = Math.floor(Date.now() / 1000);
    }

    // Cập nhật thông tin
    businessInfo.businessName = dto.businessName;
    businessInfo.taxCode = dto.taxCode;
    businessInfo.businessAddress = dto.address;
    businessInfo.representativeName = dto.legalRepresentative;
    businessInfo.representativePosition = dto.position;
    businessInfo.businessEmail = dto.email;
    businessInfo.businessPhone = dto.phoneNumber;
    businessInfo.status = 'PENDING';
    businessInfo.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu vào database
    await this.businessInfoRepository.save(businessInfo);

    // Gửi sự kiện để cập nhật trạng thái
    const success = this.affiliateRegistrationService.sendEvent(
      user.id,
      'SUBMIT_BUSINESS_INFO',
      {
        businessInfo: {
          businessName: dto.businessName,
          taxCode: dto.taxCode,
          address: dto.address,
          legalRepresentative: dto.legalRepresentative,
          position: dto.position,
          email: dto.email,
          phoneNumber: dto.phoneNumber,
        },
      },
    );

    return ApiResponseDto.success({ success }, 'Cập nhật thông tin doanh nghiệp thành công');
  }
}
