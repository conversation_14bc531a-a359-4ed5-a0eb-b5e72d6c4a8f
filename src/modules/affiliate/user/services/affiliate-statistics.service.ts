import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateClickRepository } from '../../repositories/affiliate-click.repository';
import { AffiliateCustomerOrderRepository } from '../../repositories/affiliate-customer-order.repository';
import { UserRepository } from '@/modules/user/repositories/user.repository';
import { AffiliateStatisticsQueryDto, AffiliateStatisticsDto, AffiliateUserOverviewDto } from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { AffiliateRankRepository } from '@modules/affiliate/repositories/affiliate-rank.repository';
import { AffiliatePointConversionHistoryRepository } from '@modules/affiliate/repositories/affiliate-point-conversion-history.repository';

@Injectable()
export class AffiliateStatisticsService {
  private readonly logger = new Logger(AffiliateStatisticsService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateClickRepository: AffiliateClickRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly userRepository: UserRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly affiliatePointConversionHistoryRepository: AffiliatePointConversionHistoryRepository,
  ) {}

  /**
   * Lấy thông tin thống kê tài khoản affiliate
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Thông tin thống kê
   */
  @Transactional()
  async getStatistics(
    userId: number,
    queryDto: AffiliateStatisticsQueryDto,
  ): Promise<AffiliateStatisticsDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Xác định khoảng thời gian thống kê
      const begin =
        queryDto.begin ||
        Math.floor((Date.now() - 30 * 24 * 60 * 60 * 1000) / 1000); // Mặc định 30 ngày trước
      const end = queryDto.end || Math.floor(Date.now() / 1000); // Mặc định hiện tại

      // Tính toán các thống kê
      // Sử dụng availableBalance thay vì walletBalance vì cột wallet_balance không tồn tại
      const walletBalance = affiliateAccount.availableBalance;
      const pendingAmount = await this.calculatePendingAmount(
        affiliateAccount.id,
        begin,
        end,
      );
      const clickCount = await this.countClicks(
        affiliateAccount.id,
        begin,
        end,
      );
      const customerCount = await this.countCustomers(
        affiliateAccount.id,
        begin,
        end,
      );
      const orderCount = await this.countOrders(
        affiliateAccount.id,
        begin,
        end,
      );
      const revenue = await this.calculateRevenue(
        affiliateAccount.id,
        begin,
        end,
      );
      const conversionRate =
        clickCount > 0 ? (orderCount / clickCount) * 100 : 0;

      return {
        walletBalance,
        pendingAmount,
        clickCount,
        customerCount,
        orderCount,
        revenue,
        conversionRate,
        period: {
          begin,
          end,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate statistics: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin thống kê affiliate',
      );
    }
  }

  /**
   * Tính toán số tiền đang xử lý
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số tiền đang xử lý
   */
  private async calculatePendingAmount(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    try {
      // Logic tính toán số tiền đang xử lý
      // Đây là phần giả định, cần thay thế bằng logic thực tế
      return 0;
    } catch (error) {
      this.logger.error(
        `Error calculating pending amount: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Đếm số lượt click
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số lượt click
   */
  private async countClicks(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    try {
      // Logic đếm số lượt click
      // Đây là phần giả định, cần thay thế bằng logic thực tế
      return await this.affiliateClickRepository.countByAffiliateAccountIdAndTimeRange(
        affiliateAccountId,
        begin,
        end,
      );
    } catch (error) {
      this.logger.error(`Error counting clicks: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Đếm số khách hàng
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số khách hàng
   */
  private async countCustomers(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    try {
      // Logic đếm số khách hàng
      // Đây là phần giả định, cần thay thế bằng logic thực tế
      return await this.userRepository.countByAffiliateAccountIdAndTimeRange(
        affiliateAccountId,
        begin,
        end,
      );
    } catch (error) {
      this.logger.error(
        `Error counting customers: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Đếm số đơn hàng
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số đơn hàng
   */
  private async countOrders(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    try {
      // Logic đếm số đơn hàng
      // Đây là phần giả định, cần thay thế bằng logic thực tế
      return await this.affiliateCustomerOrderRepository.countByAffiliateAccountIdAndTimeRange(
        affiliateAccountId,
        begin,
        end,
      );
    } catch (error) {
      this.logger.error(`Error counting orders: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Tính toán doanh thu
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Doanh thu
   */
  private async calculateRevenue(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    try {
      // Logic tính toán doanh thu
      // Đây là phần giả định, cần thay thế bằng logic thực tế
      return await this.affiliateCustomerOrderRepository.calculateRevenueByAffiliateAccountIdAndTimeRange(
        affiliateAccountId,
        begin,
        end,
      );
    } catch (error) {
      this.logger.error(
        `Error calculating revenue: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Lấy thông tin tổng quan về affiliate
   * @returns Thông tin tổng quan
   */
  @Transactional()
  async getOverview(): Promise<AffiliateUserOverviewDto> {
    try {
      // Tính tổng số tài khoản publisher (affiliate accounts)
      const totalPublishers = await this.affiliateAccountRepository.countTotal();

      // Tính tổng số cấp bậc affiliate
      const totalRanks = await this.affiliateRankRepository.countTotal();

      // Tính tổng số đơn hàng affiliate
      const totalOrders = await this.affiliateCustomerOrderRepository.countTotal();

      // Tính tổng số lần chuyển đổi điểm
      const totalPointConversions = await this.affiliatePointConversionHistoryRepository.countTotal();

      return {
        totalPublishers,
        totalRanks,
        totalOrders,
        totalPointConversions,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate overview: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin tổng quan về affiliate',
      );
    }
  }
}
