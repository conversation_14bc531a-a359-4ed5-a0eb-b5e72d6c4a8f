import { PdfEditService } from '../pdf/pdf-edit.service';
import { PdfPosition } from '@shared/interface/pdf-edit.interface';
import * as fs from 'fs';
import * as path from 'path';

/**
 * <PERSON><PERSON> dụ sử dụng PdfEditService
 * Đ<PERSON>y là một ví dụ đơn giản để minh họa cách sử dụng PdfEditService
 * Không phải là một unit test thực sự
 */
async function exampleUsage() {
  // Khởi tạo service
  const pdfEditService = new PdfEditService();

  try {
    // Đọc file PDF mẫu
    const pdfPath = path.join(__dirname, '../../../test-files/sample.pdf');
    const pdfBytes = fs.readFileSync(pdfPath);

    // Tạo danh sách các vị trí cần chỉnh sửa
    const positions: PdfPosition[] = [
      {
        pageIndex: 0,
        text: '<PERSON><PERSON><PERSON> bản thêm vào trang 1',
        xMm: 50,
        yMm: 50,
        size: 12,
      },
      {
        pageIndex: 0,
        text: 'Văn bản căn giữa',
        xMm: 100,
        yMm: 100,
        size: 16,
        isCenter: true,
      },
      {
        pageIndex: 1,
        text: 'Văn bản thêm vào trang 2',
        xMm: 50,
        yMm: 50,
        size: 14,
      },
    ];

    // Chỉnh sửa PDF
    const result = await pdfEditService.editPdf(pdfBytes, positions);

    // Lưu file PDF đã chỉnh sửa
    const outputPath = path.join(__dirname, '../../../test-files/output.pdf');
    fs.writeFileSync(outputPath, result.pdfBuffer);

    console.log('Đã lưu file PDF đã chỉnh sửa tại:', outputPath);
    console.log('Base64 của file PDF:', result.pdfBase64?.substring(0, 100) + '...');

    // Ví dụ thêm chữ ký
    // Đọc file chữ ký (giả sử là file PNG)
    const signaturePath = path.join(__dirname, '../../../test-files/signature.png');
    const signatureBytes = fs.readFileSync(signaturePath);
    const signatureBase64 = signatureBytes.toString('base64');

    // Tạo danh sách vị trí với chữ ký
    const positionsWithSignature: PdfPosition[] = [
      {
        pageIndex: 0,
        text: 'Chữ ký của tôi:',
        xMm: 50,
        yMm: 150,
        size: 12,
      },
      {
        pageIndex: 0,
        signatureBase64: signatureBase64,
        xMm: 50,
        yMm: 170,
        signatureWidthMm: 50,
        signatureHeightMm: 20,
      },
    ];

    // Chỉnh sửa PDF với chữ ký
    const resultWithSignature = await pdfEditService.editPdf(pdfBytes, positionsWithSignature);

    // Lưu file PDF đã chỉnh sửa với chữ ký
    const outputSignaturePath = path.join(__dirname, '../../../test-files/output-with-signature.pdf');
    fs.writeFileSync(outputSignaturePath, resultWithSignature.pdfBuffer);

    console.log('Đã lưu file PDF với chữ ký tại:', outputSignaturePath);

  } catch (error) {
    console.error('Lỗi khi chạy ví dụ:', error);
  }
}

// Chạy ví dụ
// exampleUsage().catch(console.error);

/**
 * Hướng dẫn sử dụng PdfEditService trong controller
 * 
 * ```typescript
 * @Controller('pdf')
 * export class PdfController {
 *   constructor(private readonly pdfEditService: PdfEditService) {}
 * 
 *   @Post('edit')
 *   async editPdf(@Body() body: { pdfBase64: string, positions: PdfPosition[] }) {
 *     try {
 *       const result = await this.pdfEditService.editPdf(
 *         body.pdfBase64,
 *         body.positions
 *       );
 *       
 *       return {
 *         success: true,
 *         pdfBase64: result.pdfBase64
 *       };
 *     } catch (error) {
 *       throw new AppException(ErrorCode.PDF_PROCESSING_ERROR, error.message);
 *     }
 *   }
 * }
 * ```
 */
