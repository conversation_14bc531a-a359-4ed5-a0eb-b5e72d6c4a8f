import { MarketingAiOptions, MarketingAiResponse, BaseMarketingAiService } from './base.interface';

/**
 * Image size options
 */
export enum ImageSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  CUSTOM = 'custom',
}

/**
 * Image format options
 */
export enum ImageFormat {
  PNG = 'png',
  JPEG = 'jpeg',
  WEBP = 'webp',
}

/**
 * Image style options
 */
export enum ImageStyle {
  REALISTIC = 'realistic',
  ARTISTIC = 'artistic',
  CARTOON = 'cartoon',
  SKETCH = 'sketch',
  ABSTRACT = 'abstract',
  PHOTOGRAPHIC = 'photographic',
}

/**
 * Image generation options
 */
export interface ImageGenerationOptions extends MarketingAiOptions {
  /**
   * Size of the generated image
   * @default ImageSize.MEDIUM
   */
  size?: ImageSize;

  /**
   * Width of the generated image (for custom size)
   */
  width?: number;

  /**
   * Height of the generated image (for custom size)
   */
  height?: number;

  /**
   * Format of the generated image
   * @default ImageFormat.PNG
   */
  format?: ImageFormat;

  /**
   * Style of the generated image
   * @default ImageStyle.REALISTIC
   */
  style?: ImageStyle;

  /**
   * Number of images to generate
   * @default 1
   */
  count?: number;

  /**
   * Seed for reproducible results
   */
  seed?: number;

  /**
   * Negative prompt to guide what should not be in the image
   */
  negativePrompt?: string;
}

/**
 * Image generation result
 */
export interface ImageGenerationResult {
  /**
   * URLs of the generated images
   */
  imageUrls: string[];

  /**
   * Seeds used for the generated images
   */
  seeds?: number[];

  /**
   * Prompt used for the generation
   */
  prompt: string;
}

/**
 * Interface for image generation services
 */
export interface ImageGenerationService extends BaseMarketingAiService {
  /**
   * Generate images from a text prompt
   * @param prompt Text prompt to generate images from
   * @param options Options for image generation
   * @returns A promise that resolves to a response containing the generated images
   */
  generateImage(
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>>;

  /**
   * Edit an existing image using a text prompt
   * @param imageUrl URL of the image to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for image editing
   * @returns A promise that resolves to a response containing the edited image
   */
  editImage?(
    imageUrl: string,
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>>;

  /**
   * Generate image variations from an existing image
   * @param imageUrl URL of the image to create variations from
   * @param options Options for image variation generation
   * @returns A promise that resolves to a response containing the image variations
   */
  generateImageVariations?(
    imageUrl: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>>;
}
