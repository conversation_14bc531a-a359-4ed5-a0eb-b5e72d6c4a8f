import { Injectable, Logger } from '@nestjs/common';
import { GoogleAdsService } from './google-ads.service';
import { PerformanceReport } from './interfaces/google-ads.interface';

@Injectable()
export class GoogleAdsReportService {
  private readonly logger = new Logger(GoogleAdsReportService.name);

  constructor(private readonly googleAdsService: GoogleAdsService) {}

  /**
   * Lấy báo cáo hiệu suất của chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param dateRange Khoảng thời gian
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Báo cáo hiệu suất
   */
  async getCampaignPerformance(
    customerId: string,
    campaignId: string,
    dateRange: { startDate: string; endDate: string },
    refreshToken?: string,
  ): Promise<PerformanceReport[]> {
    try {
      return await this.googleAdsService.getCampaignPerformance(customerId, campaignId, dateRange, refreshToken);
    } catch (error) {
      this.logger.error(`Failed to get campaign performance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tính toán tổng số liệu từ báo cáo hiệu suất
   * @param reports Danh sách báo cáo hiệu suất
   * @returns Tổng số liệu
   */
  calculateTotalMetrics(reports: PerformanceReport[]): {
    impressions: number;
    clicks: number;
    cost: number;
    ctr: number;
    averageCpc: number;
    conversions: number;
    conversionValue: number;
    roas: number;
  } {
    const totalImpressions = reports.reduce((sum, report) => sum + report.impressions, 0);
    const totalClicks = reports.reduce((sum, report) => sum + report.clicks, 0);
    const totalCost = reports.reduce((sum, report) => sum + report.cost, 0);
    const totalConversions = reports.reduce((sum, report) => sum + report.conversions, 0);
    const totalConversionValue = reports.reduce((sum, report) => sum + report.conversionValue, 0);
    
    // Tính toán các chỉ số trung bình
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
    const averageCpc = totalClicks > 0 ? totalCost / totalClicks : 0;
    const roas = totalCost > 0 ? totalConversionValue / totalCost : 0;
    
    return {
      impressions: totalImpressions,
      clicks: totalClicks,
      cost: totalCost,
      ctr,
      averageCpc,
      conversions: totalConversions,
      conversionValue: totalConversionValue,
      roas,
    };
  }

  /**
   * Tính toán số liệu theo ngày
   * @param reports Danh sách báo cáo hiệu suất
   * @returns Số liệu theo ngày
   */
  calculateDailyMetrics(reports: PerformanceReport[]): {
    date: string;
    impressions: number;
    clicks: number;
    cost: number;
    ctr: number;
    averageCpc: number;
    conversions: number;
  }[] {
    // Sắp xếp báo cáo theo ngày
    const sortedReports = [...reports].sort((a, b) => a.date.localeCompare(b.date));
    
    return sortedReports.map((report) => ({
      date: report.date,
      impressions: report.impressions,
      clicks: report.clicks,
      cost: report.cost,
      ctr: report.ctr,
      averageCpc: report.averageCpc,
      conversions: report.conversions,
    }));
  }

  /**
   * Tính toán số liệu theo tuần
   * @param reports Danh sách báo cáo hiệu suất
   * @returns Số liệu theo tuần
   */
  calculateWeeklyMetrics(reports: PerformanceReport[]): {
    weekStart: string;
    weekEnd: string;
    impressions: number;
    clicks: number;
    cost: number;
    ctr: number;
    averageCpc: number;
    conversions: number;
  }[] {
    // Nhóm báo cáo theo tuần
    const weeklyData: { [key: string]: PerformanceReport[] } = {};
    
    reports.forEach((report) => {
      const date = new Date(report.date.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
      const dayOfWeek = date.getDay(); // 0 = Chủ Nhật, 1 = Thứ Hai, ..., 6 = Thứ Bảy
      
      // Tính ngày đầu tuần (Thứ Hai)
      const startDate = new Date(date);
      startDate.setDate(date.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
      
      // Tính ngày cuối tuần (Chủ Nhật)
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);
      
      // Định dạng ngày
      const weekStart = this.formatDate(startDate);
      const weekEnd = this.formatDate(endDate);
      const weekKey = `${weekStart}_${weekEnd}`;
      
      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = [];
      }
      
      weeklyData[weekKey].push(report);
    });
    
    // Tính toán số liệu cho mỗi tuần
    return Object.entries(weeklyData).map(([weekKey, weekReports]) => {
      const [weekStart, weekEnd] = weekKey.split('_');
      const totalMetrics = this.calculateTotalMetrics(weekReports);
      
      return {
        weekStart,
        weekEnd,
        impressions: totalMetrics.impressions,
        clicks: totalMetrics.clicks,
        cost: totalMetrics.cost,
        ctr: totalMetrics.ctr,
        averageCpc: totalMetrics.averageCpc,
        conversions: totalMetrics.conversions,
      };
    });
  }

  /**
   * Tính toán số liệu theo tháng
   * @param reports Danh sách báo cáo hiệu suất
   * @returns Số liệu theo tháng
   */
  calculateMonthlyMetrics(reports: PerformanceReport[]): {
    month: string;
    impressions: number;
    clicks: number;
    cost: number;
    ctr: number;
    averageCpc: number;
    conversions: number;
  }[] {
    // Nhóm báo cáo theo tháng
    const monthlyData: { [key: string]: PerformanceReport[] } = {};
    
    reports.forEach((report) => {
      const month = report.date.substring(0, 6); // YYYYMM
      
      if (!monthlyData[month]) {
        monthlyData[month] = [];
      }
      
      monthlyData[month].push(report);
    });
    
    // Tính toán số liệu cho mỗi tháng
    return Object.entries(monthlyData).map(([month, monthReports]) => {
      const totalMetrics = this.calculateTotalMetrics(monthReports);
      
      // Định dạng tháng (YYYY-MM)
      const formattedMonth = `${month.substring(0, 4)}-${month.substring(4, 6)}`;
      
      return {
        month: formattedMonth,
        impressions: totalMetrics.impressions,
        clicks: totalMetrics.clicks,
        cost: totalMetrics.cost,
        ctr: totalMetrics.ctr,
        averageCpc: totalMetrics.averageCpc,
        conversions: totalMetrics.conversions,
      };
    });
  }

  /**
   * Định dạng ngày (YYYY-MM-DD)
   * @param date Đối tượng Date
   * @returns Chuỗi ngày định dạng YYYY-MM-DD
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
