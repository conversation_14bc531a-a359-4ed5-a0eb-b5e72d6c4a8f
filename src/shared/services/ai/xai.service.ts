import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { firstValueFrom } from 'rxjs';
import { XAIModel, XAIModelsResponse } from './interfaces/xai.interface';

/**
 * Service tương tác với X.AI API
 */
@Injectable()
export class XAIService {
  private readonly logger = new Logger(XAIService.name);
  private readonly apiBaseUrl = 'https://api.x.ai/v1';

  constructor(private readonly httpService: HttpService) {}

  /**
   * Lấy danh sách model từ X.AI API
   * @param apiKey API key của X.AI
   * @returns Danh sách model từ X.AI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(apiKey: string): Promise<XAIModel[]> {
    try {
      if (!apiKey) {
        throw new Error('API key is required to list models');
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<XAIModelsResponse>(
          `${this.apiBaseUrl}/models`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization':`Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model từ response
      const models = response.data.data || [];

      this.logger.log(`Retrieved ${models.length} models from X.AI`);
      return models;
    } catch (error: any) {
      this.handleApiError(error, 'lấy danh sách model');
    }
  }

  /**
   * Lấy thông tin chi tiết của một model theo ID
   * @param modelId ID của model cần lấy thông tin (ví dụ: grok-1)
   * @param apiKey API key của X.AI
   * @returns Thông tin chi tiết của model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string, apiKey: string): Promise<XAIModel> {
    try {
      // Kiểm tra modelId
      if (!modelId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Model ID không được để trống',
        );
      }

      // Kiểm tra apiKey
      if (!apiKey) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'API key không được để trống',
        );
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy thông tin chi tiết của model
      const response = await firstValueFrom(
        this.httpService.get<XAIModel>(
          `${this.apiBaseUrl}/models/${encodeURIComponent(modelId)}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy thông tin model từ response
      const model = response.data;

      if (!model || !model.id) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.logger.log(`Retrieved details for model: ${model.id}`);
      return model;
    } catch (error: any) {
      // Xử lý trường hợp không tìm thấy model (404)
      if (error.response?.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.handleApiError(error, `lấy thông tin model ${modelId}`);
    }
  }

  /**
   * Xử lý lỗi từ API
   * @param error Lỗi từ API
   * @param action Hành động đang thực hiện
   * @throws AppException với mã lỗi và thông báo phù hợp
   */
  private handleApiError(error: any, action: string): never {
    this.logger.error(
      `Error ${action} from X.AI: ${error.message}`,
      error.stack,
    );

    // Xử lý các lỗi khi kết nối X.AI API
    if (error.response?.status === 401) {
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'API key không hợp lệ hoặc đã hết hạn',
      );
    }

    if (error.response?.status === 403) {
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Không có quyền truy cập vào tài nguyên này',
      );
    }

    if (error.response?.status === 429) {
      throw new AppException(
        ErrorCode.OPENAI_QUOTA_EXCEEDED,
        'Đã vượt quá giới hạn sử dụng X.AI API',
      );
    }

    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Kết nối đến X.AI API bị gián đoạn hoặc quá thời gian chờ',
      );
    }

    if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Lỗi kết nối đến X.AI API',
      );
    }

    // Các lỗi khác
    throw new AppException(
      ErrorCode.OPENAI_API_ERROR,
      `Lỗi khi ${action}: ${error.message}`,
    );
  }
}