import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerCustomOptions } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from './swagger.tags';

// Hàm tạo cấu hình <PERSON>wagger với thông tin từ biến môi trường
export const createSwaggerConfig = (configService: ConfigService) => {
  // Lấy môi trường hiện tại (có thể sử dụng cho logic phức tạp hơn trong tương lai)
  // const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // Lấy URL server từ biến môi trường dựa trên môi trường hiện tại
  const localUrl = configService.get<string>(
    'SWAGGER_LOCAL_URL',
    `http://localhost:${configService.get('PORT', 3000)}`,
  );
  const devUrl = configService.get<string>(
    'SWAGGER_DEV_URL',
    'http://localhost:3000',
  );
  const testUrl = configService.get<string>(
    'SWAGGER_TEST_URL',
    'http://*************:3000',
  );
  const stagingUrl = configService.get<string>(
    'SWAGGER_STAGING_URL',
    'https://api-staging.redai.vn',
  );
  const prodUrl = configService.get<string>(
    'SWAGGER_PROD_URL',
    'https://api.redai.vn',
  );

  return (
    new DocumentBuilder()
      .setTitle('RedAI API')
      .setDescription(
        `
# RedAI API Documentation v1.0

Welcome to the RedAI API documentation. This API provides endpoints for managing tests, users, and blogs.

## API Version
Current version: v1

## Base URL
All API endpoints are prefixed with: \`/api/v1\`

## Authentication
Most endpoints require authentication. To authenticate, you need to:
1. Obtain a JWT token by logging in
2. Click the "Authorize" button at the top of this page
3. Enter your token in the format: \`Bearer your_token_here\`
4. Click "Authorize" and close the dialog

### Authentication for Protected Endpoints
Many endpoints require authentication. For these endpoints, you must provide a valid JWT token.

Public endpoints (like login, register, and those with 'public' in the path) do not require authentication.

## Rate Limiting
API calls are subject to rate limiting. Please refer to the response headers for rate limit information.

## Error Handling
The API uses standard HTTP status codes to indicate the success or failure of requests.
Common error codes:
- 400: Bad Request - The request was malformed or contains invalid parameters
- 401: Unauthorized - Authentication is required or has failed
- 403: Forbidden - The authenticated user does not have permission to access the requested resource
- 404: Not Found - The requested resource does not exist
- 429: Too Many Requests - Rate limit exceeded
- 500: Internal Server Error - An unexpected error occurred on the server
  `,
      )
      .setVersion('1.0')
      .setContact(
        'RedAI Support',
        'https://redai.com/support',
        '<EMAIL>',
      )
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .setExternalDoc('Additional Documentation', 'https://redai.com/docs')
      .addServer(localUrl, 'Local Server')
      .addServer(devUrl, 'Development Server')
      .addServer(testUrl, 'Test Server')
      .addServer(stagingUrl, 'Staging Server')
      .addServer(prodUrl, 'Production Server')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'x-api-key', // tên header chứa API Key
          in: 'header', // hoặc 'query' nếu bạn muốn nhận qua query param
        },
        'api-key', // tên này PHẢI khớp với @ApiSecurity('api-key')
      )
      .addTag(SWAGGER_API_TAGS.USERS, 'User endpoints')
      .addTag(SWAGGER_API_TAGS.COMMON, 'Common endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_USERS, 'Admin user management endpoints')
      .addTag(SWAGGER_API_TAGS.EMPLOYEES, 'Employee management endpoints')
      .addTag(SWAGGER_API_TAGS.TESTS, 'Test endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_TESTS, 'Admin test management endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_FILES, 'Admin file management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SETTINGS,
        'Admin settings management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.RESOURCES, 'Resource management endpoints')
      .addTag(SWAGGER_API_TAGS.BANKS, 'Bank management endpoints')
      .addTag(SWAGGER_API_TAGS.USER_BANKS, 'User bank management endpoints')
      // Add the blog-related tags here
      .addTag(SWAGGER_API_TAGS.BLOGS, 'Blog endpoints')
      .addTag(SWAGGER_API_TAGS.BLOG_COMMENTS, 'Blog comment endpoints')
      .addTag(SWAGGER_API_TAGS.BLOG_PURCHASES, 'Blog purchase endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_BLOGS, 'Admin blog management endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_SUBSCRIPTIONS,
        'User subscription endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN,
        'Admin subscription plan management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN_PRICING,
        'Admin subscription plan pricing management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION,
        'Admin subscription management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_ORDER,
        'Admin subscription order management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL,
        'User template email management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_STRATEGY, 'User strategy endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_STRATEGY,
        'Admin strategy management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.INTEGRATION, 'Integration endpoints')
      .addTag(
        SWAGGER_API_TAGS.INTEGRATION_ADMIN,
        'Admin integration management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES,
        'User knowledge files endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_KNOWLEDGE_FILES,
        'Admin knowledge files management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_AUDIENCE, 'User audience endpoints')
      .addTag(SWAGGER_API_TAGS.USER_CAMPAIGN, 'User campaign endpoints')
      .addTag(
        SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL,
        'User template email endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_SEGMENT, 'User segment endpoints')
      .addTag(SWAGGER_API_TAGS.USER_TAG, 'User tag endpoints')
      .addTag(SWAGGER_API_TAGS.USER_TEMPLATE_SMS, 'User template SMS endpoints')
      .addTag(SWAGGER_API_TAGS.USER_ACCOUNT, 'User account endpoints')
      .addTag(SWAGGER_API_TAGS.USER_AFFILIATE, 'User affiliate endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_ACCOUNT,
        'Admin affiliate account management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_ORDER,
        'Admin affiliate order management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_CUSTOMER,
        'Admin affiliate customer management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_OVERVIEW,
        'Admin affiliate overview management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AFFILIATE_RANK,
        'Admin affiliate rank management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.USER_MARKETING_STATISTICS,
        'User marketing statistics endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ADMIN_USER, 'Admin user endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_SEGMENT,
        'Admin segment management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.ADMIN_TAG, 'Admin tag management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_AUDIENCE,
        'Admin audience management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_TEMPLATE_EMAIL,
        'Admin template email management endpoints',
      )
      .addTag(
        SWAGGER_API_TAGS.ADMIN_CAMPAIGN,
        'Admin campaign management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.USER_TYPE_AGENT, 'User type agent endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_TYPE_AGENT,
        'Admin type agent management endpoints',
      )
      .addTag(SWAGGER_API_TAGS.MODEL_TRAINING, 'Model training endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_MEDIA, 'Admin media management endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_URL, 'Admin URL management endpoints')
      .addTag(
        SWAGGER_API_TAGS.ADMIN_INTEGRATION_SMS_TEST,
        'Admin integration SMS test endpoints',
      )
      .addTag(SWAGGER_API_TAGS.R_POINT_ADMIN_COUPONS, 'R-Point - Admin Coupons')
      .addTag(SWAGGER_API_TAGS.R_POINT_ADMIN_POINTS, 'R-Point - Admin Points')
      .addTag(SWAGGER_API_TAGS.PAYMENT_R_POINT_USER, 'Payment - R-Point - User')
      .addTag(SWAGGER_API_TAGS.USER_RULE_CONTRACT, 'User rule contract endpoints')
      .addTag(SWAGGER_API_TAGS.ADMIN_RULE_CONTRACT, 'Admin rule contract endpoints')
      .addTag(SWAGGER_API_TAGS.USER_MEDIA, 'User media endpoints')
      .build()
  );
};

export const swaggerCustomOptions: SwaggerCustomOptions = {
  swaggerOptions: {
    persistAuthorization: true,
    docExpansion: 'none',
    filter: true,
    tagsSorter: 'alpha',
    operationsSorter: 'alpha',
    defaultModelsExpandDepth: 1,
    defaultModelExpandDepth: 1,
    tryItOutEnabled: true, // Enable Try it out by default
    displayRequestDuration: true, // Show request duration
    showExtensions: true,
    showCommonExtensions: true,
  },
  customCss: `
  /* Thay nền topbar thành trắng, chữ và icon đỏ */
  .swagger-ui .topbar {
    background-color: #e53e3e;
    color: #ffffff;
  }
  .swagger-ui .topbar .download-url-wrapper .select-label select {
    border-color: #e53e3e;
    color: #e53e3e;
  }

  /* Tiêu đề API màu đỏ */
  .swagger-ui .info .title {
    color: #e53e3e;
  }

  /* Các opblock (GET, POST, PUT, DELETE, PATCH) chung nền trắng, viền đỏ nhạt */
  .swagger-ui .opblock {
    background: rgba(229, 62, 62, 0.1);
    border-color: #e53e3e;
  }
  .swagger-ui .opblock .opblock-summary-method {
    color: #ffffff;
  }

  /* Nút Execute màu đỏ tươi trên nền trắng */
  .swagger-ui .btn.execute {
    background-color: #e53e3e;
    color: #ffffff;
    border-color: #c53030;
  }
  .swagger-ui .btn.execute:hover {
    background-color: #c53030;
  }

  /* Các đường kẻ, nút mở rộng cũng về tông xám nhạt cho hài hòa */
  .swagger-ui .opblock .opblock-summary {
    border-bottom-color: #fde8e8;
  }
  .swagger-ui .parameter__name, .swagger-ui .response-col_status {
    color: #c53030;
  }
    `,
  customSiteTitle: 'RedAI API Documentation',
  customfavIcon: 'https://redai.com/favicon.ico',
};
