package com.redon_agency.chatbot.user.payment_config.service;


import com.redon_agency.chatbot.common.repository.CustomerWebRepository;
import com.redon_agency.chatbot.dto.client.chatbot.*;
import com.redon_agency.chatbot.dto.client.sepay.PaymentSlipResponse;
import com.redon_agency.chatbot.dto.client.sepay.WebhooksResponse;
import com.redon_agency.chatbot.dto.client.sepay.hub.*;
import com.redon_agency.chatbot.dto.response.ApiResponse;
import com.redon_agency.chatbot.dto.response.PageResponse;
import com.redon_agency.chatbot.dto.response.sepay.BankResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CreateBankResponse;
import com.redon_agency.chatbot.entity.*;
import com.redon_agency.chatbot.entity.email.TemplateAuto;
import com.redon_agency.chatbot.event.SepayHubEvent;
import com.redon_agency.chatbot.exception.AppException;
import com.redon_agency.chatbot.exception.ErrorCode;
import com.redon_agency.chatbot.mapper.BankInSepayMapper;
import com.redon_agency.chatbot.repository.*;
import com.redon_agency.chatbot.repository.client.chatbot.ChatBotFacebookClient;
import com.redon_agency.chatbot.repository.client.chatbot.ChatBotWebClient;
import com.redon_agency.chatbot.repository.client.chatbot.MediaChannelEnum;
import com.redon_agency.chatbot.repository.client.chatbot.PaymentWebWebhook;
import com.redon_agency.chatbot.repository.client.sepay.SePayBankHubClient;
import com.redon_agency.chatbot.repository.email.TemplateAutoRepository;
import com.redon_agency.chatbot.service.other.RedisService;
import com.redon_agency.chatbot.user.payment_config.dto.response.CAccountRes;
import com.redon_agency.chatbot.user.payment_config.dto.request.CCreateBankAccountReq;
import com.redon_agency.chatbot.user.payment_config.event.CSepayHubEvent;
import com.redon_agency.chatbot.user.payment_config.mapper.ElectronicPaymentMapper;
import com.redon_agency.chatbot.utils.BankCodeEnum;
import com.redon_agency.chatbot.utils.SepayHubUtils;
import com.redon_agency.chatbot.utils.SepayUtils;
import com.redon_agency.chatbot.utils.email.CategoryTemplateAutoEnum;
import com.redon_agency.chatbot.utils.email.TemplateAutoUtils;
import com.redon_agency.chatbot.utils.email.TemplateEntity;
import com.redon_agency.chatbot.utils.enum_utils.ConvertHistoryEnum;
import com.redon_agency.chatbot.utils.enum_utils.ElectronicPaymentGatewayStatusEnum;
import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;
import com.redon_agency.chatbot.utils.enum_utils.PaymentStatusEnum;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CSepayHubServiceImpl implements CSepayHubService {
    final TemplateAutoRepository templateAutoRepository;
    final BankInSepayRepository bankInSepayRepository;
    final BankInSepayMapper bankInSepayMapper;
    final SepayHubUtils sepayHubUtils;
    final ConvertHistoryRepository convertHistoryRepository;
    final SepayUtils sepayUtils;
    final SePayBankHubClient sePayBankHubClient;
    final UserRepository userRepository;
    final ElectronicPaymentGatewayRepository electronicPaymentGatewayRepository;
    final RedisService redisService;
    final CompanySepayRepository companySepayRepository;
    final ChatBotPaymentGatewaysRepository chatBotPaymentGatewaysRepository;
    final TemplateAutoUtils templateAutoUtils;
    final PaymentDetailRepository paymentDetailRepository;
    final ChatBotFacebookClient chatBotFacebookClient;
    final ConvertCustomerRepository convertCustomerRepository;
    final ElectronicPaymentMapper electronicPaymentMapper;
    final ChatBotWebClient chatBotWebClient;
    final CustomerWebRepository customerWebRepository;
    final CSepayHubEvent cSepayHubEvent;
    private final ChatBotRepository chatBotRepository;

    @Value("${chatbot.api.key}")
    String apiKey;

    @Override
    public List<BankResponse> getListAvailableBanks() {
        List<BankInSepay> list = bankInSepayRepository.findAll();
        return bankInSepayMapper.toBankResponseList(list);
    }

    @Transactional
    @Override
    public CreateBankResponse getCreateBank(CCreateBankAccountReq request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        int userId = Integer.parseInt(authentication.getName());

//        ElectronicPaymentGateway electronicPaymentGatewayExist =
//                electronicPaymentGatewayRepository.findByAccountNumberAndBankCode(request.getAccountNumber(), request.getBankCode().name())
//                        .orElse(null);
//
//
//        if (electronicPaymentGatewayExist != null) {
//            Integer userIdOfPayment = electronicPaymentGatewayExist.getUserId();
//            if (electronicPaymentGatewayExist.getStatus().equals(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC)) {
//                throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS_AND_VERIFIED);
//            } else if (Objects.equals(userIdOfPayment, userId) && electronicPaymentGatewayExist.getStatus().equals(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)) {
//                throw new AppException(ErrorCode.REQUEST_BANK_LINK_AND_VERIFY);
//            } else if (!Objects.equals(userIdOfPayment, userId) && electronicPaymentGatewayExist.getStatus().equals(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)) {
//                throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
//            } else {
//                throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
//            }
//        }

        User user = userRepository.findById(userId).orElseThrow(
                () -> new AppException(ErrorCode.UNAUTHORIZED)
        );

        String bearerToken = sepayHubUtils.getToken();

        CompanySepay companySepay = companySepayRepository.findByUserId(userId).orElseGet(
                () -> {
                    UUID uuid2 = UUID.randomUUID();
                    CompanyCreateResponse companyCreateResponse = sePayBankHubClient.createCompany(bearerToken, uuid2, CompanyCreateRequest.builder()
                            .fullName(user.getName())
                            .shortName(user.getUserId().toString())
                            .build());
                    if (companyCreateResponse == null || companyCreateResponse.getCode() == 400 || companyCreateResponse.getCode() == 503) {
                        throw new AppException(ErrorCode.ERROR_SYSTEM);
                    }

                    return companySepayRepository.save(CompanySepay.builder()
                            .userId(userId)
                            .companyId(companyCreateResponse.getCompanyId())
                            .ipnActive(false)
                            .build());
                }
        );

        // Cập nhật công ty tổ chức
        if (!companySepay.isIpnActive()) {
            sepayHubUtils.updateCompany(companySepay.getCompanyId(), user);

            // Cấu hình công ty tổ chức
            sepayHubUtils.updateCompanyConfiguration(companySepay.getCompanyId());

            // cập nhật ipn active
            companySepay.setIpnActive(true);
            companySepayRepository.save(companySepay);
        }

        // Kiểm tra bank_code
        switch (request.getBankCode()) {
            case ACB -> {
                BankAccountCreateRequest bankAccountCreateRequest = BankAccountCreateRequest.builder()
                        .company_id(companySepay.getCompanyId())
                        .account_holder_name(request.getAccountHolderName())
                        .account_number(request.getAccountNumber())
                        .phone_number(request.getPhoneNumber())
                        .build();

                BankAccountCreateResponse bankAccountCreateResponse = sepayHubUtils.createBankAccountACB(bankAccountCreateRequest);

                if (bankAccountCreateResponse != null) {
                    switch (bankAccountCreateResponse.getCode()) {
                        case 2011 -> {
                            String requestId = bankAccountCreateResponse.getData().getRequest_id();
                            String accountId = bankAccountCreateResponse.getId();
                            ElectronicPaymentGateway electronicPaymentGateway = ElectronicPaymentGateway.builder()
                                    .accountId(accountId)
                                    .userId(userId)
                                    .bankCode(BankCodeEnum.ACB.name())
                                    .accountNumber(request.getAccountNumber())
                                    .accountHolderName(request.getAccountHolderName())
                                    .identificationNumber(request.getIdentificationNumber())
                                    .merchantName(request.getMerchantName())
                                    .merchantAddress(request.getMerchantAddress())
                                    .phoneNumber(request.getPhoneNumber())
                                    .companyId(companySepay.getCompanyId())
                                    .status(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)
                                    .requestId(requestId)
                                    .isVa(false)
                                    .mainId(null)
                                    .build();

                            ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.save(electronicPaymentGateway);

                            // Tạo thời gian hết hạn OTP (5 phút sau)
                            LocalDateTime otpExpired = LocalDateTime.now().plusMinutes(3);

                            // Chuyển sang milliseconds (epoch milli)
                            long otpExpiredMillis = otpExpired.toInstant(ZoneOffset.UTC).toEpochMilli();

                            return CreateBankResponse.builder()
                                    .accountId(accountId)
                                    .id(gateway.getId())
                                    .status(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC.name())
                                    .otpExpiryTime(otpExpiredMillis)
                                    .build();
                        }
                        case 2012 -> {
                            String accountId = bankAccountCreateResponse.getId();
                            ElectronicPaymentGateway electronicPaymentGateway = ElectronicPaymentGateway.builder()
                                    .accountId(accountId)
                                    .userId(userId)
                                    .bankCode(BankCodeEnum.ACB.name())
                                    .accountNumber(request.getAccountNumber())
                                    .accountHolderName(request.getAccountHolderName())
                                    .identificationNumber(request.getIdentificationNumber())
                                    .phoneNumber(request.getPhoneNumber())
                                    .companyId(companySepay.getCompanyId())
                                    .merchantName(request.getMerchantName())
                                    .merchantAddress(request.getMerchantAddress())
                                    .status(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC)
                                    .isVa(false)
                                    .mainId(null)
                                    .build();

                            ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                            return CreateBankResponse.builder()
                                    .accountId(accountId)
                                    .id(gateway.getId())
                                    .status(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC.name())
                                    .build();
                        }
                        case 400 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        }
                        case 4001 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
                        }
                        case 4002 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.UNREGISTERED_ID_PHONE);
                        }
                        case 4003 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.UNREGISTERED_ID);
                        }
                        case 4004 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_NOT_FOUND);
                        }
                        case 4005 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_NAME_MISMATCH);
                        }
                        case 4006 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.PHONE_NOT_REGISTERED_WITH_ACCOUNT);
                        }
                        case 504 -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACB_SYSTEM_BUSY);
                        }
                        default -> {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION);
                        }
                    }
                }
            }

            case MB -> {
                BankAccountCreateRequest bankAccountCreateRequest = BankAccountCreateRequest.builder()
                        .company_id(companySepay.getCompanyId())
                        .account_holder_name(request.getAccountHolderName())
                        .account_number(request.getAccountNumber())
                        .identification_number(request.getIdentificationNumber())
                        .phone_number(request.getPhoneNumber())
                        .build();

                BankAccountCreateResponse bankAccountCreateResponse = sepayHubUtils.createBankAccountMB(bankAccountCreateRequest);

                switch (bankAccountCreateResponse.getCode()) {
                    case 2011 -> {
                        String requestId = bankAccountCreateResponse.getData().getRequest_id();
                        String accountId = bankAccountCreateResponse.getId();
                        ElectronicPaymentGateway electronicPaymentGateway = ElectronicPaymentGateway.builder()
                                .accountId(accountId)
                                .userId(userId)
                                .bankCode(BankCodeEnum.MB.name())
                                .accountNumber(request.getAccountNumber())
                                .accountHolderName(request.getAccountHolderName())
                                .identificationNumber(request.getIdentificationNumber())
                                .merchantName(request.getMerchantName())
                                .merchantAddress(request.getMerchantAddress())
                                .phoneNumber(request.getPhoneNumber())
                                .companyId(companySepay.getCompanyId())
                                .status(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)
                                .requestId(requestId)
                                .isVa(false)
                                .mainId(null)
                                .build();

                        ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        return CreateBankResponse.builder()
                                .accountId(accountId)
                                .id(gateway.getId())
                                .status(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC.name())
                                .build();
                    }
                    case 2012 -> {
                        String accountId = bankAccountCreateResponse.getId();
                        ElectronicPaymentGateway electronicPaymentGateway = ElectronicPaymentGateway.builder()
                                .accountId(accountId)
                                .userId(userId)
                                .bankCode(BankCodeEnum.MB.name())
                                .accountNumber(request.getAccountNumber())
                                .accountHolderName(request.getAccountHolderName())
                                .identificationNumber(request.getIdentificationNumber())
                                .phoneNumber(request.getPhoneNumber())
                                .companyId(companySepay.getCompanyId())
                                .merchantName(request.getMerchantName())
                                .merchantAddress(request.getMerchantAddress())
                                .status(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC)
                                .build();

                        ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.save(electronicPaymentGateway);

                        // Tạo thời gian hết hạn OTP (5 phút sau)
                        LocalDateTime otpExpired = LocalDateTime.now().plusMinutes(3);

                        // Chuyển sang milliseconds (epoch milli)
                        long otpExpiredMillis = otpExpired.toInstant(ZoneOffset.UTC).toEpochMilli();

                        return CreateBankResponse.builder()
                                .accountId(accountId)
                                .id(gateway.getId())
                                .status(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC.name())
                                .otpExpiryTime(otpExpiredMillis)
                                .build();
                    }
                    case 400 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                    }
                    case 4001 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
                    }
                    case 4002 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.UNREGISTERED_ID_PHONE);
                    }
                    case 4003 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.UNREGISTERED_ID);
                    }
                    case 4004 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.ACCOUNT_NOT_FOUND);
                    }
                    case 4005 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.ACCOUNT_NAME_MISMATCH);
                    }
                    case 4006 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.PHONE_NOT_REGISTERED_WITH_ACCOUNT);
                    }
                    case 504 -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                    }
                    default -> {
                        log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                        throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION);
                    }
                }
            }
            case OCB -> {
                BankAccountCreateRequest bankAccountCreateRequest = BankAccountCreateRequest.builder()
                        .company_id(companySepay.getCompanyId())
                        .phone_number(request.getPhoneNumber())
                        .account_holder_name(request.getAccountHolderName())
                        .account_number(request.getAccountNumber())
                        .identification_number(request.getIdentificationNumber())
                        .build();
                BankAccountCreateResponse bankAccountCreateResponse = sepayHubUtils.createBankAccountOCB(bankAccountCreateRequest);

                if (bankAccountCreateResponse != null) {
                    switch (bankAccountCreateResponse.getCode()) {
                        case 200: {
                            String accountId = bankAccountCreateResponse.getId();
                            ElectronicPaymentGateway electronicPaymentGateway = ElectronicPaymentGateway.builder()
                                    .accountId(accountId)
                                    .userId(userId)
                                    .bankCode(BankCodeEnum.OCB.name())
                                    .accountNumber(request.getAccountNumber())
                                    .accountHolderName(request.getAccountHolderName())
                                    .identificationNumber(request.getIdentificationNumber())
                                    .phoneNumber(request.getPhoneNumber())
                                    .companyId(companySepay.getCompanyId())
                                    .merchantName(request.getMerchantName())
                                    .merchantAddress(request.getMerchantAddress())
                                    .status(ElectronicPaymentGatewayStatusEnum.CHUA_LIEN_KET_API)
                                    .isVa(false)
                                    .mainId(null)
                                    .build();

                            electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                            return CreateBankResponse.builder()
                                    .accountId(accountId)
                                    .status(ElectronicPaymentGatewayStatusEnum.CHUA_LIEN_KET_API.name())
                                    .success(true)
                                    .build();
                        }
                        case 400: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        }
                        case 4001: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
                        }
                        case 4002: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.UNREGISTERED_ID_PHONE);
                        }
                        case 4003: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.UNREGISTERED_ID);
                        }
                        case 4004: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_NOT_FOUND);
                        }
                        case 504: {
                            log.error("Sepay Hub Error : {}", bankAccountCreateResponse.getMessage());
                            throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                        }
                        default: {
                            throw new AppException(ErrorCode.LINK_FAILED);
                        }
                    }
                }
            }
            default -> throw new AppException(ErrorCode.BANK_CODE_INVALID);
        }
        return null;
    }

    @Override
    public Object confirmApiConnection(String otp, Integer id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        if (!Objects.equals(electronicPaymentGateway.getUserId(), userId)) {
            throw new AppException(ErrorCode.UNAUTHORIZED);
        }

        if (electronicPaymentGateway.getRequestId() == null || electronicPaymentGateway.getRequestId().isEmpty()) {
            throw new AppException(ErrorCode.NOT_SENT_LINK_REQUEST_YET);
        }

        switch (electronicPaymentGateway.getBankCode()) {
            case "ACB": {
                BankAccountConfirmRequest confirmRequest = BankAccountConfirmRequest.builder()
                        .otp(otp)
                        .build();
                BankAccountConfirmResponse bankAccountConfirmResponse = sepayHubUtils.confirmApiConnectionACB(electronicPaymentGateway.getRequestId(), confirmRequest);

                Integer codeStatus = bankAccountConfirmResponse.getCode();
                if (codeStatus != null) {
                    if (codeStatus == 200) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGateway.setStatus(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);

                        // BEGIN EVENT
                        cSepayHubEvent.eventAfterConfirmApiConnection(userId, electronicPaymentGateway);
                        // END EVENT
                        return "Xác thực thành công";
                    } else if (codeStatus == 400) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                    } else if (codeStatus == 4001) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                    } else if (codeStatus == 504) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                    }
                }
            }
            case "MB": {
                BankAccountConfirmRequest confirmRequest = BankAccountConfirmRequest.builder()
                        .otp(otp)
                        .build();

                BankAccountConfirmResponse bankAccountConfirmResponse = sepayHubUtils.confirmBankAccountConnectionMB(electronicPaymentGateway.getRequestId(), confirmRequest);

                Integer codeStatus = bankAccountConfirmResponse.getCode();
                if (codeStatus != null) {
                    if (codeStatus == 200) {
                        electronicPaymentGateway.setStatus(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        return "Xác thực thành công";
                    } else if (codeStatus == 400) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                    } else if (codeStatus == 4001) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                    } else if (codeStatus == 504) {
                        electronicPaymentGateway.setRequestId(null);
                        electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                        log.error("Sepay Hub Error : {}", bankAccountConfirmResponse.getMessage());
                        throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                    }
                }
            }
            case "OCB": {
                return "Đã xác thực hãy tạo tài khoản VA";
            }
            default: {
                throw new AppException(ErrorCode.BANK_CODE_INVALID);
            }
        }
    }

    @Override
    public List<CAccountRes> listOfLinkedAccounts() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        List<ElectronicPaymentGateway> list = electronicPaymentGatewayRepository.findByUserId(userId);
        List<BankInSepay> banks = bankInSepayRepository.findAll();

        return electronicPaymentMapper.toListCAccountRes(list, banks);
    }

    @Transactional
    @Override
    public Object createVaAccount(Integer id, String email, String VA) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        if (!Objects.equals(electronicPaymentGateway.getUserId(), userId)) {
            throw new AppException(ErrorCode.UNAUTHORIZED);
        }

        boolean isExistVAAccount = electronicPaymentGatewayRepository.existVAAccount(VA, electronicPaymentGateway.getId());
        if (isExistVAAccount) {
            throw new AppException(ErrorCode.VA_ACCOUNT_EXITS);
        }

        CompanySepay companySepay = companySepayRepository.findByUserId(userId).orElseThrow(
                () -> new AppException(ErrorCode.LET_CREATE_ACCOUNT)
        );

        switch (electronicPaymentGateway.getBankCode()) {
            case "MB": {
                throw new AppException(ErrorCode.UNSUPPORTED_VA_BANK.formatMessage("MB"));
            }
            case "ACB": {
                throw new AppException(ErrorCode.UNSUPPORTED_VA_BANK.formatMessage("ACB"));
            }
            case "OCB": {
                // Tạo VA
                CreateVARequest createVARequest = CreateVARequest.builder()
                        .bank_account_id(electronicPaymentGateway.getAccountId())
                        .email(email)
                        .merchant_address(electronicPaymentGateway.getMerchantAddress())
                        .merchant_name(electronicPaymentGateway.getMerchantName())
                        .company_id(companySepay.getCompanyId())
                        .va(VA)
                        .build();
                CreateVAResponse createVAResponse = sepayHubUtils.requestCreateVAOCB(createVARequest);

                if (createVAResponse != null) {
                    return switch (createVAResponse.getCode()) {
                        case 200 -> {
                            String requestId = createVAResponse.getData().getRequest_id();
                            yield electronicPaymentGatewayRepository.save(ElectronicPaymentGateway.builder()
                                    .accountNumber(VA)
                                    .accountHolderName(electronicPaymentGateway.getAccountHolderName())
                                    .requestId(requestId)
                                    .companyId(companySepay.getCompanyId())
                                    .identificationNumber(electronicPaymentGateway.getIdentificationNumber())
                                    .label(electronicPaymentGateway.getLabel())
                                    .phoneNumber(electronicPaymentGateway.getPhoneNumber())
                                    .bankCode(electronicPaymentGateway.getBankCode())
                                    .mainId(id)
                                    .isVa(true)
                                    .status(ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)
                                    .build());
                        }
                        case 400 -> {
                            log.error("Sepay Hub Error : {}", createVAResponse.getMessage());
                            throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        }
                        case 4001 -> {
                            log.error("Sepay Hub Error : {}", createVAResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
                        }
                        default -> throw new AppException(ErrorCode.CREATE_VA_FAILT);
                    };
                }
            }
            default: {
                throw new AppException(ErrorCode.CREATE_VA_FAILT);
            }
        }
    }

    @Override
    public Object confirmVAAccount(String otp, Integer id) {

        ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        OtpRequest otpRequest = OtpRequest.builder()
                .otp(otp)
                .build();
        ConfirmCreateVAResponse confirmCreateVAResponse = sepayHubUtils.confirmCreateVAOCB(gateway.getRequestId(), otpRequest);

        if (confirmCreateVAResponse != null) {
            switch (confirmCreateVAResponse.getCode()) {
                case 201: {
                    String vaCreatedId = confirmCreateVAResponse.getId();
                    gateway.setRequestId(null);
                    gateway.setStatus(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC);
                    gateway.setVaId(vaCreatedId);
                    return electronicPaymentGatewayRepository.save(gateway);
                }
                case 400: {
                    log.error("Sepay Hub Error : {}", confirmCreateVAResponse.getMessage());
                    throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                }
                case 4001: {
                    log.error("Sepay Hub Error : {}", confirmCreateVAResponse.getMessage());
                    throw new AppException(ErrorCode.OTP_INVALID);
                }
                case 4002: {
                    log.error("Sepay Hub Error : {}", confirmCreateVAResponse.getMessage());
                    throw new AppException(ErrorCode.OTP_EXPIRED);
                }
                case 504: {
                    log.error("Sepay Hub Error : {}", confirmCreateVAResponse.getMessage());
                    throw new AppException(ErrorCode.OCB_SYSTEM_BUSY);
                }
                default: {
                    throw new AppException(ErrorCode.CREATE_VA_FAILT);
                }
            }
        }
        throw new AppException(ErrorCode.CREATE_VA_FAILT);
    }

    @Override
    public ResponseEntity<?> ipn(
            WebhooksSepayHubRequest webhooksRequest,
            HttpServletRequest httpServletRequest
    ) {
        log.info("1");
        boolean isApiKey = sepayHubUtils.validateApiKey(httpServletRequest);

        log.info("2");
        if (!isApiKey) {
            log.info("3");
            throw new AppException(ErrorCode.ACCESS_DENIED);
        }

        log.info("4");
        Pattern pattern = Pattern.compile("RED(.*?)SE");

        log.info("5");
        Matcher matcher = pattern.matcher(webhooksRequest.getContent());

        log.info("6");
        Integer orderId = null; // Biến để lưu kết quả

        log.info("7");
        if (matcher.find()) {
            log.info("8");
            // Trích xuất ID
            orderId = Integer.valueOf(matcher.group(1));
        }

        log.info("9");
        // Kiểm tra và in kết quả
        if (orderId == null) {
            log.info("10");
            return ResponseEntity.badRequest().body(WebhooksResponse.builder()
                    .success(false)
                    .build());
        }

        log.info("11");
        ConvertHistory convertHistory = convertHistoryRepository.findById(orderId).orElse(null);
        log.info("12");
        if (convertHistory == null || !isOrderUnpaid(convertHistory)) {
            log.info("13");
            return ResponseEntity.badRequest().body(WebhooksResponse.builder()
                    .success(false)
                    .build());
        }
        log.info("14");
        if (
                "credit".equals(webhooksRequest.getTransferType())
                        &&
                        isValidAmount(webhooksRequest.getAmount(), convertHistory.getTotalPrice().doubleValue())
        ) {
            log.info("15");
            convertHistory.setPaymentStatus(PaymentStatusEnum.SUCCESS);

            log.info("16");
            convertHistoryRepository.save(convertHistory);

            log.info("17");
            this.handleSepayEvent(new SepayHubEvent(this, webhooksRequest, convertHistory));

            log.info("18");
            return ResponseEntity.ok().body(WebhooksResponse.builder()
                    .success(true)
                    .build());
        }

        log.info("19");
        return ResponseEntity.badRequest().body(WebhooksResponse.builder()
                .success(false)
                .build());
    }


    @Async
    @EventListener
    public void handleSepayEvent(SepayHubEvent sepayHubEvent) {
        log.info("async 1");
        WebhooksSepayHubRequest webhooksRequest = sepayHubEvent.getRequest();

        log.info("async 2");
        ConvertHistory convertHistory = sepayHubEvent.getConvertHistory();

        log.info("async 3");
        // Câp nhật thông tin payment detail
        PaymentDetail paymentDetail = convertHistory.getPaymentDetail();

        log.info("async 4");
        updatePaymentDetail(paymentDetail, webhooksRequest);

        log.info("async 5");
        // Gọi api chatbot client
        sendStatusToChatBotClient(convertHistory, webhooksRequest);

        log.info("async 6");

        // Gửi email
        User user = convertHistory.getChatBot().getUser();

        log.info("async 7");
        sendEmail(user.getEmail(), TemplateEntity.builder()
                .user(user)
                .build(), CategoryTemplateAutoEnum.PAYMENT_SUCCESSFUL);

    }

    private void sendStatusToChatBotClient(ConvertHistory convertHistory, WebhooksSepayHubRequest webhooksRequest) {
        log.info("async-async 1");
        // Phân loại media channel
        String mediaChannel = convertHistory.getMediaChannel();

        log.info("async-async 2");
        String[] option = mediaChannel.split(" - ");

        log.info("async-async 3");
        if (option[0].equalsIgnoreCase(MediaChannelEnum.website.name())) {

            log.info("async-async 5");
            String webId = convertHistory.getMetadata().getWebsite().getWebId();

            log.info("async-async 6");
            PaymentWebWebhook request = PaymentWebWebhook.builder()
                    .orderId(convertHistory.getConversationId())
                    .status(true)
                    .webId(webId)
                    .build();

            int retryCount = 0;

            log.info("async-async 7");
            boolean isSuccess = false;

            log.info("async-async 8");
            while (retryCount < 5) {
                log.info("async-async 9");
                StatusResponse statusResponse = chatBotWebClient.paymentWebWebhook(request);

                log.info("async-async 10");
                isSuccess = statusResponse.isSuccess();
                if (isSuccess) {
                    log.info("async-async 11");
                    break;
                }
                log.info("async-async 12");
                retryCount++;
            }

            log.info("async-async 13");
            if (!isSuccess) {
                // Xử lý nếu sau 5 lần vẫn không thành công
                log.error("sendStatusToChatBotClient: {}", webhooksRequest);
            }
        } else if (MediaChannelEnum.facebook.name().equalsIgnoreCase(option[0])) {
            log.info("async-async 14");
            PaymentFacebookWebhook paymentFacebookWebhook = getPaymentFacebookWebhook(webhooksRequest, convertHistory, true);

            log.info("async-async 15");
            int retryCount = 0;

            log.info("async-async 16");
            boolean isSuccess = false;

            log.info("async-async 17");
            while (retryCount < 5) {

                log.info("async-async 18");
                StatusResponse statusResponse = chatBotFacebookClient.paymentPaceBookWebhook(paymentFacebookWebhook);

                log.info("async-async 19");
                isSuccess = statusResponse.isSuccess();

                log.info("async-async 20");
                if (isSuccess) {
                    log.info("async-async 21");
                    break;
                }
                log.info("async-async 22");
                retryCount++;
            }

            if (!isSuccess) {
                // Xử lý nếu sau 5 lần vẫn không thành công
                log.error("sendStatusToChatBotClient: {}", webhooksRequest);
            }
        }
    }

    private PaymentFacebookWebhook getPaymentFacebookWebhook(WebhooksSepayHubRequest webhooksRequest, ConvertHistory convertHistory, boolean status) {
        String senderId = convertHistory.getMetadata().getFacebook().getSenderId();
        Sender sender = Sender.builder()
                .id(senderId)
                .build();

        String recipientId = convertHistory.getMetadata().getFacebook().getRecipientId();
        Recipient recipient = Recipient.builder()
                .id(recipientId)
                .build();

        Message message = Message.builder()
                .mid("19283")
                .text("SEPAY_WEBHOOK")
                .build();

        Payment payment = Payment.builder()
                .orderId(convertHistory.getConversationId())
                .status(status)
                .build();

        Messaging messaging = Messaging.builder()
                .sender(sender)
                .recipient(recipient)
                .timestamp(2374)
                .message(message)
                .payment(payment)
                .build();
        List<Messaging> messagings = new ArrayList<>();
        messagings.add(messaging);

        Entry entry = Entry.builder()
                .id("12345")
                .messaging(messagings)
                .time(1)
                .build();

        List<Entry> entrys = new ArrayList<>();
        entrys.add(entry);

        return PaymentFacebookWebhook.builder()
                .entry(entrys)
                .object("page")
                .build();
    }

    private void updatePaymentDetail(PaymentDetail paymentDetail, WebhooksSepayHubRequest webhooksRequest) {
        try {
            Map<String, Object> detail = new HashMap<>();

            // Lưu các thông tin từ WebhooksRequest vào Map
            detail.put("gateway", webhooksRequest.getGateway());
            detail.put("transactionDate", webhooksRequest.getTransactionDate());
            detail.put("accountNumber", webhooksRequest.getAccountNumber());
            detail.put("bankAccountId", webhooksRequest.getBankAccountId());
            detail.put("va", webhooksRequest.getVa());
            detail.put("paymentCode", webhooksRequest.getPaymentCode());
            detail.put("content", webhooksRequest.getContent());
            detail.put("transferType", webhooksRequest.getTransferType());
            detail.put("amount", webhooksRequest.getAmount());
            detail.put("referenceCode", webhooksRequest.getReferenceCode());
            detail.put("accumulated", webhooksRequest.getAccumulated());
            detail.put("transactionId", webhooksRequest.getTransactionId());

            // Gán Map vào trường detail của PaymentDetail
            paymentDetail.setDetail(detail); // Bạn có thể lưu dưới dạng JSON hoặc dạng chuỗi bất kỳ tùy theo yêu cầu của hệ thống
            paymentDetailRepository.save(paymentDetail);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    @Override
    public PaymentSlipResponse getQRUrl(HttpServletRequest request, Integer orderId) {
        log.info("1");
        // BEGIN SECURITY
        String header = request.getHeader("Authorization");

        log.info("2");
        if (header == null || !header.startsWith("Apikey ")) {
            throw new AppException(ErrorCode.ACCESS_DENIED);
        }
        log.info("3");

        String apiKeyRequest = header.substring("Apikey ".length());

        log.info("4");
        if (apiKeyRequest.isEmpty() || !apiKeyRequest.equals(apiKey)) {
            throw new AppException(ErrorCode.ACCESS_DENIED);
        }
        // END SECURITY

        log.info("5");
        ConvertHistory order = convertHistoryRepository.findById(orderId).orElseThrow(
                () -> new AppException(ErrorCode.ORDER_NOT_FOUND)
        );

        log.info("5");
        PaymentDetail paymentDetail = order.getPaymentDetail();

        log.info("6");
        if (!paymentDetail.getPaymentMethod().name().equals(PaymentChatbotEnum.ONLINE_BANKING.name())) {
            throw new AppException(ErrorCode.ORDER_NOT_ONLINE_BANKING);
        }

        log.info("7");
        if (order.getTotalPrice() == null) {
            throw new AppException(ErrorCode.ORDER_MISSING_PRICE);
        }

//        log.info("5");
//        if (!isOrderUnpaid(order)) {
//            throw new AppResponse(AppCode.PAYMENT_SUCCESSED);
//        }

        log.info("8");
        ChatBotPaymentGateways gateways = chatBotPaymentGatewaysRepository.findByChatBotId(order.getChatBot().getChatBotId())
                .orElseThrow(
                        () -> new AppException(ErrorCode.CHAT_BOT_PAYMENT_NOT_FOUND)
                );

        log.info("9");
        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(gateways.getElectronicPaymentGetwayId()).orElseThrow(
                () -> new AppException(ErrorCode.ELECTRONIC_PAYMENT_NOT_FOUND)
        );

        log.info("10");
        String qrUrl = sepayUtils.generateQRPaymentSepayHub(
                electronicPaymentGateway.getBankCode(),
                electronicPaymentGateway.getAccountNumber(),
                order.getTotalPrice().doubleValue(),
                order.getConversationId().toString()
        );

        log.info("11");
        BankInSepay bankInSepay = bankInSepayRepository.findById(electronicPaymentGateway.getBankCode()).orElseThrow(
                () -> new AppException(ErrorCode.BANK_CODE_INVALID)
        );

        return PaymentSlipResponse.builder()
                .qrUrl(qrUrl)
                .accountName(electronicPaymentGateway.getAccountHolderName())
                .bankName(bankInSepay.getFullName())
                .bankAccount(electronicPaymentGateway.getAccountNumber())
                .des("RED" + order.getConversationId() + "SE")
                .total(order.getTotalPrice().doubleValue())
                .build();
    }

    @Transactional
    @Override
    public ApiResponse<?> deleteBankAccount(Integer id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        if (!Objects.equals(electronicPaymentGateway.getUserId(), userId)) {
            throw new AppException(ErrorCode.UNAUTHORIZED);
        }

        BankCodeEnum bankCodeEnum = BankCodeEnum.valueOf(electronicPaymentGateway.getBankCode());
        switch (bankCodeEnum) {
            case ACB -> {
                BankAccountDeleteRequestResponse bankAccountDeleteRequestResponse = sepayHubUtils.requestDeleteApiConnection(BankCodeEnum.ACB, electronicPaymentGateway.getAccountId());
                if (bankAccountDeleteRequestResponse != null) {
                    Integer code = bankAccountDeleteRequestResponse.getCode();
                    switch (code) {
                        case 200: {
                            String requestId = bankAccountDeleteRequestResponse.getData().getRequest_id();
                            redisService.saveDataWithTTL("DELETE_REQUEST_ID_" + electronicPaymentGateway.getAccountId(), requestId, 15, TimeUnit.MINUTES);
                            return ApiResponse.builder()
                                    .code(200)
                                    .result("Đã gửi yêu cầu xóa thành công hãy xác nhận otp")
                                    .build();
                        }
                        case 409: {
                            throw new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET);
                        }
                        case 504: {
                            throw new AppException(ErrorCode.OCB_SYSTEM_BUSY);
                        }
                        default: {
                            throw new AppException(ErrorCode.ERROR_SYSTEM);
                        }
                    }
                }
            }
            case MB -> {
                if (Objects.equals(electronicPaymentGateway.getStatus(), ElectronicPaymentGatewayStatusEnum.CHUA_LIEN_KET_API) || Objects.equals(electronicPaymentGateway.getStatus(), ElectronicPaymentGatewayStatusEnum.CHUA_XAC_THUC)) {
                    BankAccountDeleteConfirmResponse response = sepayHubUtils.forceDeleteMB(electronicPaymentGateway.getAccountId());
                    if (response.getCode() == 200) {
                        chatBotPaymentGatewaysRepository.deleteByAccountNumberBankCode(electronicPaymentGateway.getId());
                        electronicPaymentGatewayRepository.deleteById(electronicPaymentGateway.getId());
                        return ApiResponse.builder()
                                .code(201)
                                .result("Xoá tài khoản thành công")
                                .build();
                    }
                }

                BankAccountDeleteRequestResponse bankAccountDeleteRequestResponse = sepayHubUtils.requestDeleteApiConnection(BankCodeEnum.MB, electronicPaymentGateway.getAccountId());

                if (bankAccountDeleteRequestResponse != null) {
                    Integer code = bankAccountDeleteRequestResponse.getCode();
                    switch (code) {
                        case 200: {
                            String requestId = bankAccountDeleteRequestResponse.getData().getRequest_id();
                            redisService.saveDataWithTTL("DELETE_REQUEST_ID_" + id, requestId, 15, TimeUnit.MINUTES);
                            return ApiResponse.builder()
                                    .code(200)
                                    .result("Đã gửi yêu cầu xóa thành công hãy xác nhận otp")
                                    .build();
                        }
                        case 409: {
                            throw new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET);
                        }
                        case 504: {
                            throw new AppException(ErrorCode.OCB_SYSTEM_BUSY);
                        }
                        default: {
                            throw new AppException(ErrorCode.ERROR_SYSTEM);
                        }
                    }
                }
            }
            case OCB -> throw new AppException(ErrorCode.BANK_OCB_NO_DELETE_FEATURE);
            default -> throw new AppException(ErrorCode.INVALID_INPUT_DATA);
        }
        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
    }

    @Transactional
    @Override
    public String confirmDeleteBankAccount(Integer id, String otp) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        if (!Objects.equals(electronicPaymentGateway.getUserId(), userId)) {
            throw new AppException(ErrorCode.UNAUTHORIZED);
        }

        if (electronicPaymentGateway.getStatus() == null || !electronicPaymentGateway.getStatus().equals(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC)) {
            throw new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET);
        }

        String requestId = (String) redisService.getData("DELETE_REQUEST_ID_" + id);

        if (requestId == null || requestId.isEmpty()) {
            throw new AppException(ErrorCode.OTP_EXPIRED);
        }


        BankCodeEnum bankCodeEnum = BankCodeEnum.valueOf(electronicPaymentGateway.getBankCode());

        // Đặt lại chatbot payment method is NONE
        chatBotRepository.updateChatBotPaymentMethod(electronicPaymentGateway.getId(), PaymentChatbotEnum.NONE.name());
        chatBotPaymentGatewaysRepository.deleteByAccountNumberBankCode(electronicPaymentGateway.getId());
        electronicPaymentGatewayRepository.deleteById(electronicPaymentGateway.getId());

        switch (bankCodeEnum) {
            case ACB -> {
                OtpRequest otpRequest = OtpRequest.builder()
                        .otp(otp)
                        .build();
                BankAccountDeleteConfirmResponse bankAccountDeleteConfirmResponse = sepayHubUtils.confirmDeleteApiConnection(BankCodeEnum.ACB, requestId, otpRequest);
                if (bankAccountDeleteConfirmResponse != null) {
                    Integer code = bankAccountDeleteConfirmResponse.getCode();
                    return switch (code) {
                        case 200 -> "Đã hủy liên kết API thành công";
                        case 400 -> throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        case 4001 -> throw new AppException(ErrorCode.OTP_EXPIRED);
                        case 504 -> throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                        default -> throw new AppException(ErrorCode.ERROR_SYSTEM);
                    };
                }
            }
            case MB -> {
                OtpRequest otpRequest = OtpRequest.builder()
                        .otp(otp)
                        .build();
                BankAccountDeleteConfirmResponse bankAccountDeleteConfirmResponse = sepayHubUtils.confirmDeleteApiConnection(BankCodeEnum.MB, requestId, otpRequest);
                if (bankAccountDeleteConfirmResponse != null) {
                    Integer code = bankAccountDeleteConfirmResponse.getCode();
                    return switch (code) {
                        case 200 -> "Đã hủy liên kết API thành công";
                        case 400 -> throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        case 4001 -> throw new AppException(ErrorCode.OTP_INVALID_OR_EXPIRED);
                        case 504 -> throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                        default -> throw new AppException(ErrorCode.ERROR_SYSTEM);
                    };
                }
            }
            case OCB -> throw new AppException(ErrorCode.BANK_OCB_NO_DELETE_FEATURE);
            default -> throw new AppException(ErrorCode.INVALID_INPUT_DATA);
        }
        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
    }

    @Override
    public CreateBankResponse requestApiConnection(Integer id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        if (!Objects.equals(electronicPaymentGateway.getUserId(), userId)) {
            throw new AppException(ErrorCode.UNAUTHORIZED);
        }

        if (electronicPaymentGateway.getStatus() == null) {
            throw new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET);
        }

        if (electronicPaymentGateway.getStatus().equals(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC)) {
            throw new AppException(ErrorCode.ACCOUNT_ALREADY_VERIFIED);
        }

        BankCodeEnum bankCodeEnum = BankCodeEnum.valueOf(electronicPaymentGateway.getBankCode());
        switch (bankCodeEnum) {
            case MB -> {
                BankAccountRequestResponse bankAccountRequestResponse = sepayHubUtils.requestApiConnection(BankCodeEnum.MB, electronicPaymentGateway.getAccountId());
                if (bankAccountRequestResponse != null) {
                    Integer code = bankAccountRequestResponse.getCode();
                    switch (code) {
                        case 200: {
                            String requestId = bankAccountRequestResponse.getData().getRequest_id();
                            electronicPaymentGateway.setRequestId(requestId);
                            electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                            return null;
                        }
                        case 409: {
                            throw new AppException(ErrorCode.BANK_ACCOUNT_ALREADY_LINKED);
                        }
                        case 504: {
                            throw new AppException(ErrorCode.MB_SYSTEM_BUSY);
                        }
                        default: {
                            throw new AppException(ErrorCode.ERROR_SYSTEM);
                        }
                    }
                }
            }
            case OCB -> throw new AppException(ErrorCode.BANK_ACCOUNT_ALREADY_LINKED);
            case ACB -> {
                BankAccountRequestResponse bankAccountRequestResponse = sepayHubUtils.requestApiConnection(BankCodeEnum.ACB, electronicPaymentGateway.getAccountId());

                if (bankAccountRequestResponse != null) {
                    Integer code = bankAccountRequestResponse.getCode();
                    switch (code) {
                        case 200: {
                            String requestId = bankAccountRequestResponse.getData().getRequest_id();
                            electronicPaymentGateway.setRequestId(requestId);
                            electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                            return null;
                        }
                        case 409: {
                            throw new AppException(ErrorCode.BANK_ACCOUNT_ALREADY_LINKED);
                        }
                        case 504: {
                            throw new AppException(ErrorCode.ACB_SYSTEM_BUSY);
                        }
                        default: {
                            throw new AppException(ErrorCode.ERROR_SYSTEM);
                        }
                    }
                }
            }
            default -> throw new AppException(ErrorCode.INVALID_INPUT_DATA);
        }
        throw new AppException(ErrorCode.INVALID_INPUT_DATA);
    }

    @Override
    public Object requestApiConnectionVA(Integer id, String email) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        CompanySepay companySepay = companySepayRepository.findByUserId(userId).orElseThrow(
                () -> new AppException(ErrorCode.LET_CREATE_ACCOUNT)
        );

        switch (electronicPaymentGateway.getBankCode()) {
            case "MB": {
                throw new AppException(ErrorCode.UNSUPPORTED_VA_BANK.formatMessage("MB"));
            }
            case "ACB": {
                throw new AppException(ErrorCode.UNSUPPORTED_VA_BANK.formatMessage("ACB"));
            }
            case "OCB": {
                String VA = electronicPaymentGateway.getAccountNumber();

                if (VA != null && VA.startsWith("SEP")) {
                    VA = VA.substring(3);
                }

                CreateVARequest createVARequest = CreateVARequest.builder()
                        .bank_account_id(electronicPaymentGateway.getAccountId())
                        .email(email)
                        .merchant_address(electronicPaymentGateway.getMerchantAddress())
                        .merchant_name(electronicPaymentGateway.getMerchantName())
                        .company_id(companySepay.getCompanyId())
                        .va(VA)
                        .build();
                CreateVAResponse createVAResponse = sepayHubUtils.requestCreateVA(BankCodeEnum.OCB, createVARequest);
                if (createVAResponse != null) {
                    switch (createVAResponse.getCode()) {
                        case 200: {
                            String requestId = createVAResponse.getData().getRequest_id();
                            electronicPaymentGateway.setRequestId(requestId);
                            electronicPaymentGatewayRepository.save(electronicPaymentGateway);
                            return null;
                        }
                        case 400: {
                            log.error("Sepay Hub Error : {}", createVAResponse.getMessage());
                            throw new AppException(ErrorCode.INVALID_INPUT_DATA);
                        }
                        case 4001: {
                            log.error("Sepay Hub Error : {}", createVAResponse.getMessage());
                            throw new AppException(ErrorCode.ACCOUNT_ALREADY_EXISTS);
                        }
                        default: {
                            throw new AppException(ErrorCode.CREATE_VA_FAILT);
                        }
                    }
                }
            }
            default: {
                throw new AppException(ErrorCode.CREATE_VA_FAILT);
            }
        }
    }

    @Override
    public PageResponse<BankAccountDTO> getPageBankAccounts(
            Integer pageNo,
            Integer pageSize,
            String searchKey
    ) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        String bearerToken = sepayHubUtils.getToken();

        UUID uuid = UUID.randomUUID();

        CompanySepay companySepay = companySepayRepository.findByUserId(userId).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        String companyId = companySepay.getCompanyId();

        BankAccountResponse response = sePayBankHubClient.getBankAccounts(bearerToken, uuid, pageSize.toString(), String.valueOf((pageNo - 1)), searchKey, companyId, null);
        BankAccountDTO[] content = response.getData();

        BankAccountResponse.Meta meta = response.getMeta();

        return PageResponse.<BankAccountDTO>builder()
                .pageNo(pageNo)
                .pageSize(meta.getPer_page())
                .totalPages(meta.getPage_count())
                .totalElements(meta.getTotal())
                .last(meta.getHas_more())
                .content(List.of(content))
                .build();
    }

    @Override
    public com.redon_agency.chatbot.dto.response.sepay_hub.BankAccountDetailResponse getBankAccountDetails(Integer id) {
        ElectronicPaymentGateway electronicPaymentGateway = electronicPaymentGatewayRepository.findById(id).orElseThrow(
                () -> new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET)
        );

        BankInSepay bankInSepay = bankInSepayRepository.findById(electronicPaymentGateway.getBankCode()).orElseThrow(
                () -> new AppException(ErrorCode.UNSUPPORTED_VA_BANK)
        );

        try {
            return sepayHubUtils.getBankAccountDetails(electronicPaymentGateway, bankInSepay);
        } catch (Exception e) {
            throw new AppException(ErrorCode.NO_ACCOUNTS_LINKED_YET);
        }
    }

    @Override
    public List<CAccountRes> getEligibleVAAccounts() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        List<ElectronicPaymentGateway> list = electronicPaymentGatewayRepository.getEligibleVAAccounts(userId);
        List<BankInSepay> banks = bankInSepayRepository.findAll();

        return electronicPaymentMapper.toListCAccountRes(list, banks);
    }

    @Override
    public List<CAccountRes> listEligiblePaymentAccountsForChatBot() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        List<ElectronicPaymentGateway> list =
                electronicPaymentGatewayRepository.findActiveByUserId(ElectronicPaymentGatewayStatusEnum.DA_XAC_THUC, userId);

        List<BankInSepay> banks = bankInSepayRepository.findAll();

        return electronicPaymentMapper.toListCAccountRes(list, banks);
    }

    private boolean isValidAmount(Double transferAmount, Double total) {
        return transferAmount != null && transferAmount > 0 && Math.abs(transferAmount - total) <= 500;
    }

    private boolean isOrderUnpaid(ConvertHistory order) {
        return order.getStatus() == null || order.getStatus().isEmpty() || Objects.equals(PaymentStatusEnum.PENDING.name(), order.getPaymentStatus().name());
    }

    private void sendEmail(String to, TemplateEntity entity, CategoryTemplateAutoEnum category) {
        try {
            TemplateAuto template = templateAutoRepository.findTemplateAutoByCategory(category);

            templateAutoUtils.sendEmail(to, template, entity);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }
}
