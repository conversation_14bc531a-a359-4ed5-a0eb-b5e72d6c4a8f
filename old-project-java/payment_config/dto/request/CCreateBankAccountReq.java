package com.redon_agency.chatbot.user.payment_config.dto.request;

import com.redon_agency.chatbot.utils.BankCodeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CCreateBankAccountReq {

    // merchant_name yêu cầu không rỗng, có độ dài từ 1 đến 500 và chỉ chứa chữ, số, dấu cách và dấu gạch ngang
    @NotEmpty(message = "Tên điểm bán là bắt buộc")
    @Size(min = 1, max = 500, message = "Tên điểm bán phải có độ dài từ 1 đến 500 ký tự")
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-]+$", message = "Tên điểm bán chỉ có thể chứa chữ cái, số, dấu cách và dấu gạch ngang")
    private String merchantName;

    // merchant_address yêu cầu không rỗng, có độ dài từ 1 đến 1000 và chỉ chứa chữ, số, dấu cách và dấu gạch ngang
    @NotEmpty(message = "Địa chỉ điểm bán là bắt buộc")
    @Size(min = 1, max = 1000, message = "Địa chỉ điểm bán phải có độ dài từ 1 đến 1000 ký tự")
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-]+$", message = "Địa chỉ điểm bán chỉ có thể chứa chữ cái, số, dấu cách và dấu gạch ngang")
    private String merchantAddress;

    // Các trường còn lại kiểm tra theo kiểu đã có trong lớp của bạn
    private BankCodeEnum bankCode;

    @NotEmpty(message = "Số tài khoản ngân hàng là bắt buộc")
    @Size(max = 20, message = "Số tài khoản ngân hàng phải có độ dài tối đa 20 ký tự")
    private String accountNumber;

    @NotEmpty(message = "Tên chủ tài khoản ngân hàng là bắt buộc")
    private String accountHolderName;

    @NotEmpty(message = "Số CMND/CCCD là bắt buộc")
    @Size(max = 100, message = "Số CMND/CCCD không được vượt quá 100 ký tự")
    private String identificationNumber;

    @NotEmpty(message = "Số điện thoại là bắt buộc")
    private String phoneNumber;
}