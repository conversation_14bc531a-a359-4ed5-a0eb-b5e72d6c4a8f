package com.redon_agency.chatbot.user.payment_config.controller;

import com.redon_agency.chatbot.dto.client.sepay.hub.BankAccountDTO;
import com.redon_agency.chatbot.dto.client.sepay.hub.WebhooksSepayHubRequest;
import com.redon_agency.chatbot.dto.response.ApiResponse;
import com.redon_agency.chatbot.dto.response.PageResponse;
import com.redon_agency.chatbot.dto.response.sepay.BankResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CreateBankResponse;
import com.redon_agency.chatbot.dto.response.sepay_hub.BankAccountDetailResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CAccountRes;
import com.redon_agency.chatbot.user.payment_config.service.CSepayHubService;
import com.redon_agency.chatbot.user.payment_config.dto.request.CCreateBankAccountReq;
import com.redon_agency.chatbot.validator.ValidEmail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/c_payment-gateway")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CSepayHubController {
    CSepayHubService cSepayHubService;

    @Operation(summary = "Lấy danh sách ngân hàng có hỗ trợ trong hệ thống", description = "Lấy danh sách ngân hàng có hỗ trợ trong hệ thống")
    @GetMapping("/list-bank-available")
    public ApiResponse<List<BankResponse>> listBankAvailable() {

        return ApiResponse.<List<BankResponse>>builder()
                .code(200)
                .result(cSepayHubService.getListAvailableBanks())
                .build();
    }

    @Operation(summary = "Tạo yêu cầu thêm tài khoản ngân hàng", description = "Yêu cầu thêm tài khoản ngân hàng")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Success"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "3002", description = "Hãy gửi yêu cầu liên kết và xác thực tài khoản ngân hàng", content = @Content),
    })
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/create-bank")
    public ApiResponse<?> createBank(
            @Valid @RequestBody CCreateBankAccountReq request
    ) {

        return ApiResponse.builder()
                .code(200)
                .result(cSepayHubService.getCreateBank(request))
                .build();
    }

    @Operation(summary = "Nhập OTP xác nhận liên kết tài khoản", description = "Nhập OTP xác nhận liên kết tài khoản")
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/confirm-api-connection")
    public ApiResponse<?> confirmApiConnection(
            @NotNull(message = "Trường bắt buộc") @RequestParam String otp,
            @RequestParam Integer id
    ) {

        return ApiResponse.builder()
                .code(200)
                .result(cSepayHubService.confirmApiConnection(otp, id))
                .build();
    }

    @Operation(summary = "Lấy danh sách tài khoản đã liên kết", description = "Lấy danh sách tài khoản đã liên kết")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/list-of-linked-accounts")
    public ApiResponse<List<CAccountRes>> listOfLinkedAccounts() {

        return ApiResponse.<List<CAccountRes>>builder()
                .code(200)
                .result(cSepayHubService.listOfLinkedAccounts())
                .build();
    }

    @Operation(summary = "Danh sách tài khoản đã liên kết có phân trang", description = "Lấy danh sách tài khoản đã liên kết có phân trang")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("page/bank-accounts")
    public ApiResponse<PageResponse<BankAccountDTO>> getPageBankAccounts(
            @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "page_size", required = false, defaultValue = "8") Integer pageSize,
            @RequestParam(value = "search_key", required = false, defaultValue = "") String searchKey
    ) {
        return ApiResponse.<PageResponse<BankAccountDTO>>builder()
                .code(200)
                .result(cSepayHubService.getPageBankAccounts(
                                pageNo,
                                pageSize,
                                searchKey
                        )
                )
                .build();
    }

    @Operation(summary = "Tạo yêu cầu liên kết tài khoản VA", description = "Tạo yêu cầu liên kết tài khoản VA")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Success"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "1135", description = "Số tài khoản đã tồn tại", content = @Content)
    })
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/create-va-account")
    public ApiResponse<?> createVaAccount(
            @NotNull(message = "Trường bắt buộc") @RequestParam Integer id,
            @ValidEmail(message = "Email không đúng định dạng") @RequestParam String email,
            @Size(min = 1, max = 15, message = "Số VA tối đa 15 ký tự và tối thiểu 1 ký tự") @RequestParam(value = "vaAccount") String VA
    ) {

        return ApiResponse.builder()
                .code(200)
                .result(cSepayHubService.createVaAccount(id, email, VA))
                .build();
    }

    @Operation(summary = "Nhập mã xác thực tài khoản VA", description = "Nhập mã xác thực tài khoản VA")
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/confirm-va-account")
    public ApiResponse<?> confirmVaAccount(
            @NotNull(message = "Trường bắt buộc otp") @Pattern(regexp = "^[0-9]{8}$", message = "Trường phải có chính xác 8 chữ số") @RequestParam String otp,
            @NotNull(message = "Trường bắt buộc id") @RequestParam Integer id
    ) {
        return ApiResponse.builder()
                .code(200)
                .result(cSepayHubService.confirmVAAccount(otp, id))
                .build();
    }

    @Operation(summary = "API IPN Frontend không cần làm", description = "API IPN Frontend không cần làm")
    @PermitAll
    @PostMapping("/ipn")
    public ResponseEntity<?> sepayHubIpn(
            @RequestBody WebhooksSepayHubRequest request,
            HttpServletRequest httpServletRequest
    ) {
        log.info("info: {}", request);
        log.info("content: {}", request.getContent());
        log.info("transactionDate: {}", request.getTransactionDate());
        log.info("transactionId: {}", request.getTransactionId());
        log.info("amount: {}", request.getAmount());
        log.info("va: {}", request.getVa());
        log.info("gateway: {}", request.getGateway());
        log.info("accumulated: {}", request.getAccumulated());
        log.info("transferType: {}", request.getTransferType());
        log.info("bankAccountId: {}", request.getBankAccountId());
        log.info("paymentCode: {}", request.getPaymentCode());
        return cSepayHubService.ipn(request, httpServletRequest);
    }

    @Operation(summary = "Gửi yêu cầu xóa tài khoản", description = "Gửi yêu cầu xóa tài khoản")
    @DeleteMapping("/bank-account/request-delete")
    public ApiResponse<?> deleteBankAccount(
            @RequestParam Integer id
    ) {
        return cSepayHubService.deleteBankAccount(id);
    }

    @Operation(summary = "Nhập OTP xác nhận xóa tài khoản", description = "Nhập OTP xác nhận xóa tài khoản")
    @DeleteMapping("/bank-account/confirm-delete")
    public ApiResponse<String> confirmDeleteBankAccount(
            @RequestParam Integer id,
            @RequestParam String otp
    ) {
        return ApiResponse.<String>builder()
                .code(200)
                .result(cSepayHubService.confirmDeleteBankAccount(id, otp))
                .build();
    }

    @Operation(summary = "Gửi lại yêu cầu liên kết tài khoản ngân hàng lại", description = "Gửi lại yêu cầu liên kết tài khoản ngân hàng lại")
    @PostMapping("/bank-account/request-api-connection")
    public ApiResponse<CreateBankResponse> requestApiConnection(
            @RequestParam Integer id
    ) {
        return ApiResponse.<CreateBankResponse>builder()
                .code(200)
                .result(cSepayHubService.requestApiConnection(id))
                .build();
    }

    @Operation(summary = "Gửi lại yêu cầu liên kết tài khoản VA", description = "Gửi lại yêu cầu liên kết tài khoản VA")
    @PostMapping("/bank-account/request-api-connection-va")
    public ApiResponse<?> requestApiConnectionVA(
            @RequestParam Integer id,
            @RequestParam String email
    ) {
        return ApiResponse.builder()
                .code(200)
                .result(cSepayHubService.requestApiConnectionVA(id, email))
                .build();
    }

    @Operation(summary = "Thông tin chi tiết của tài khoản ngân hàng", description = "Thông tin chi tiết của tài khoản ngân hàng")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/bank-account/{id}")
    public ApiResponse<BankAccountDetailResponse> getDetailsBankAccount(
            @PathVariable(value = "id") Integer id
    ) {

        return ApiResponse.<BankAccountDetailResponse>builder()
                .code(200)
                .result(cSepayHubService.getBankAccountDetails(id))
                .build();
    }

    @Operation(summary = "Danh sách tài khoản đủ điều kiện tạo tài khoản VA",
            description = "Danh sách tài khoản có thể tạo được tài khoản VA")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/va-account/eligible")
    public ApiResponse<List<CAccountRes>> getEligibleVAAccounts() {

        return ApiResponse.<List<CAccountRes>>builder()
                .code(200)
                .result(cSepayHubService.getEligibleVAAccounts())
                .build();
    }

    @Operation(summary = "Danh sách tài khoản payment đủ điều kiện để liên kết với chatbot",
            description = "Liệt kê danh sách các tài khoản payment đủ điều kiện để liên kết với chatbot")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/chatbots/eligible-payments")
    public ApiResponse<List<CAccountRes>> listEligiblePaymentAccountsForChatBot(
    ) {
        List<CAccountRes> eligibleAccounts = cSepayHubService.listEligiblePaymentAccountsForChatBot();
        return ApiResponse.<List<CAccountRes>>builder()
                .code(200)
                .message("Danh sách tài khoản đủ điều kiện!")
                .result(eligibleAccounts)
                .build();
    }

}
