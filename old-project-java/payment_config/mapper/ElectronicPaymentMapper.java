package com.redon_agency.chatbot.user.payment_config.mapper;

import com.redon_agency.chatbot.entity.BankInSepay;
import com.redon_agency.chatbot.entity.ElectronicPaymentGateway;
import com.redon_agency.chatbot.user.payment_config.dto.response.CAccountRes;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ElectronicPaymentMapper {
    public List<CAccountRes> toListCAccountRes(List<ElectronicPaymentGateway> request, List<BankInSepay> banks) {
        return request.stream().map(
                (account) -> {
                    BankInSepay bankInSepay = banks.stream().filter((bank) -> Objects.equals(bank.getBankCode(), account.getBankCode())).findFirst().orElse(new BankInSepay());
                    return toCAccountRes(account, bankInSepay);
                }
        ).toList();
    }
    public CAccountRes toCAccountRes(ElectronicPaymentGateway request, BankInSepay bank) {

        return CAccountRes.builder()
                .id(request.getId())
                .accountId(request.getAccountId())
                .accountName(request.getAccountHolderName())
                .accountNumber(request.getAccountNumber())
                .isVA(request.getIsVa())
                .isDelete(!Objects.equals("OCB", bank.getBankCode()))
                .status(request.getStatus().name())
                .iconBank(bank.getLogoPath())
                .bankCode(request.getBankCode())
                .build();
    }
}
